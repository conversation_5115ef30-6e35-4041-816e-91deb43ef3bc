name: safescan_flutter
description: "SafeScan - Your Personal Product Safety Guardian"
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.8.0

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8

  # UI and Icons
  material_design_icons_flutter: ^7.0.7296

  # State Management
  provider: ^6.1.2
  riverpod: ^2.5.1
  flutter_riverpod: ^2.5.1

  # Navigation
  go_router: ^16.0.0

  # HTTP and API
  http: ^1.4.0
  dio: ^5.7.0

  # Database and Backend
  supabase_flutter: ^2.8.0

  # Image Processing
  image_picker: ^1.1.2
  image: ^4.2.0

  # Local Storage
  shared_preferences: ^2.3.2
  hive: ^2.2.3
  hive_flutter: ^1.1.0

  # JSON and Serialization
  json_annotation: ^4.9.0

  # Utilities
  uuid: ^4.5.1
  intl: ^0.20.2
  url_launcher: ^6.3.1

  # Loading and UI Components
  shimmer: ^3.0.0
  cached_network_image: ^3.4.1
  flutter_spinkit: ^5.2.1
  lottie: ^3.1.3

  # Charts and Graphs
  fl_chart: ^1.0.0

  # Permissions
  permission_handler: ^11.0.1
  camera: ^0.10.5+5

  # Scandit Barcode Scanning SDK
  scandit_flutter_datacapture_core: '>=7.4.1 <7.4.2'
  scandit_flutter_datacapture_barcode: '>=7.4.1 <7.4.2'

  # Environment Variables
  flutter_dotenv: ^5.1.0

  # Logging
  logger: ^2.0.2

  flutter_slidable: ^4.0.0
  google_fonts: ^6.2.1

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^6.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/
    - assets/icons/
    - assets/animations/
    - assets/fonts/
    - .env

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Inter
  #     fonts:
  #       - asset: assets/fonts/Inter-Regular.ttf
  #       - asset: assets/fonts/Inter-Medium.ttf
  #         weight: 500
  #       - asset: assets/fonts/Inter-SemiBold.ttf
  #         weight: 600
  #       - asset: assets/fonts/Inter-Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
