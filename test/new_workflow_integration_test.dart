import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import '../lib/services/new_scan_workflow_service.dart';
import '../lib/services/gemini_service.dart';
import '../lib/services/supabase_init_service.dart';
import '../lib/models/product.dart';
import '../lib/models/ingredient.dart';

// Generate mocks
@GenerateMocks([GeminiService, SupabaseInitService])
import 'new_workflow_integration_test.mocks.dart';

void main() {
  group('New Scan Workflow Service Tests', () {
    late MockGeminiService mockGeminiService;
    late MockSupabaseInitService mockSupabaseService;

    setUp(() {
      mockGeminiService = MockGeminiService();
      mockSupabaseService = MockSupabaseInitService();
    });

    group('Workflow Execution Tests', () {
      test('should execute complete workflow successfully with existing product', () async {
        // Arrange
        const testBarcode = '1234567890123';
        final existingProduct = Product(
          id: 'test-product-1',
          name: 'Test Product',
          barcode: testBarcode,
          safetyScore: 85,
          safetyRating: 'good',
          ingredients: [
            Ingredient(
              id: 'ingredient-1',
              name: 'Water',
              safetyLevel: 'green',
              safetyRating: 5,
              createdAt: DateTime.now(),
              updatedAt: DateTime.now(),
            ),
          ],
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        // Mock database check to return existing product
        // Note: In real implementation, we'd mock the Supabase client
        
        // Act
        final result = await NewScanWorkflowService.executeWorkflow(
          barcode: testBarcode,
          onStageUpdate: (stage, detail) {
            print('Test stage: $stage - $detail');
          },
        );

        // Assert
        expect(result, isNotNull);
        // Note: This test would need proper mocking setup for Supabase
      });

      test('should handle product not found scenario', () async {
        // Arrange
        const testBarcode = '9999999999999';

        // Act & Assert
        expect(
          () => NewScanWorkflowService.executeWorkflow(
            barcode: testBarcode,
            onStageUpdate: (stage, detail) {
              print('Test stage: $stage - $detail');
            },
          ),
          // Should handle gracefully and return null for not found
          completes,
        );
      });
    });

    group('Safety Rating Logic Tests', () {
      test('should mark product as NOT RECOMMENDED when RED ingredients present', () {
        // Arrange
        final ingredients = [
          Ingredient(
            id: '1',
            name: 'Safe Ingredient',
            safetyLevel: 'green',
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
          Ingredient(
            id: '2',
            name: 'Unsafe Ingredient',
            safetyLevel: 'red', // This should trigger NOT RECOMMENDED
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
        ];

        // Act
        final rating = NewScanWorkflowService._determineProductRating(ingredients);

        // Assert
        expect(rating, equals('NOT RECOMMENDED'));
      });

      test('should mark product as GOOD when only GREEN and YELLOW ingredients', () {
        // Arrange
        final ingredients = [
          Ingredient(
            id: '1',
            name: 'Safe Ingredient',
            safetyLevel: 'green',
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
          Ingredient(
            id: '2',
            name: 'Caution Ingredient',
            safetyLevel: 'yellow',
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
        ];

        // Act
        final rating = NewScanWorkflowService._determineProductRating(ingredients);

        // Assert
        expect(rating, equals('GOOD'));
      });

      test('should mark product as NOT RECOMMENDED when no ingredients', () {
        // Arrange
        final ingredients = <Ingredient>[];

        // Act
        final rating = NewScanWorkflowService._determineProductRating(ingredients);

        // Assert
        expect(rating, equals('NOT RECOMMENDED'));
      });
    });

    group('Ingredient Analysis Tests', () {
      test('should analyze ingredient with Gemini AI when not in database', () async {
        // Arrange
        const ingredientName = 'Unknown Ingredient';
        const mockAIResponse = '''
{
  "safety_level": "yellow",
  "safety_rating": 3,
  "description": "Test ingredient description",
  "health_risks": ["Minor concern"],
  "category": "additive",
  "concern_level": "Medium"
}
''';

        when(mockGeminiService.generateContent(any))
            .thenAnswer((_) async => mockAIResponse);

        // Act
        final result = await NewScanWorkflowService._analyzeIngredientWithGemini(ingredientName);

        // Assert
        expect(result, isNotNull);
        expect(result!['safety_level'], equals('yellow'));
        expect(result['safety_rating'], equals(3));
        expect(result['description'], equals('Test ingredient description'));
      });

      test('should handle Gemini AI analysis failure gracefully', () async {
        // Arrange
        const ingredientName = 'Test Ingredient';

        when(mockGeminiService.generateContent(any))
            .thenThrow(Exception('AI service unavailable'));

        // Act
        final result = await NewScanWorkflowService._analyzeIngredientWithGemini(ingredientName);

        // Assert
        expect(result, isNull);
      });
    });

    group('Safety Analysis Tests', () {
      test('should correctly analyze overall product safety', () async {
        // Arrange
        final ingredients = [
          Ingredient(
            id: '1',
            name: 'Green Ingredient',
            safetyLevel: 'green',
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
          Ingredient(
            id: '2',
            name: 'Yellow Ingredient',
            safetyLevel: 'yellow',
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
          Ingredient(
            id: '3',
            name: 'Red Ingredient',
            safetyLevel: 'red',
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
        ];

        // Act
        final analysis = await NewScanWorkflowService._analyzeOverallProductSafety(ingredients);

        // Assert
        expect(analysis['total_ingredients'], equals(3));
        expect(analysis['green_ingredients'], equals(1));
        expect(analysis['yellow_ingredients'], equals(1));
        expect(analysis['red_ingredients'], equals(1));
        expect(analysis['concerning_ingredients'], contains('Red Ingredient'));
        expect(analysis['safe_ingredients'], contains('Green Ingredient'));
        expect(analysis['caution_ingredients'], contains('Yellow Ingredient'));
      });
    });

    group('Error Handling Tests', () {
      test('should create appropriate error products for different failure scenarios', () {
        // Test camera error
        final cameraError = NewScanWorkflowService.createCameraErrorProduct();
        expect(cameraError.safetyRating, equals('error'));
        expect(cameraError.name, equals('Camera Error'));

        // Test barcode detection error
        final barcodeError = NewScanWorkflowService.createBarcodeDetectionErrorProduct();
        expect(barcodeError.safetyRating, equals('error'));
        expect(barcodeError.name, equals('Barcode Detection Error'));

        // Test API error
        final apiError = NewScanWorkflowService.createAPIErrorProduct('123456789');
        expect(apiError.safetyRating, equals('error'));
        expect(apiError.barcode, equals('123456789'));

        // Test safety analysis error
        final safetyError = NewScanWorkflowService.createSafetyAnalysisErrorProduct('123456789', 'Test Product');
        expect(safetyError.safetyRating, equals('unknown'));
        expect(safetyError.name, equals('Test Product'));
      });
    });

    group('Stage Tracking Tests', () {
      test('should track workflow stages correctly', () async {
        // Arrange
        final stageUpdates = <String>[];
        const testBarcode = '1234567890123';

        // Act
        try {
          await NewScanWorkflowService.executeWorkflow(
            barcode: testBarcode,
            onStageUpdate: (stage, detail) {
              stageUpdates.add(stage);
            },
          );
        } catch (e) {
          // Expected to fail in test environment without proper setup
        }

        // Assert
        expect(stageUpdates, isNotEmpty);
        expect(stageUpdates.first, equals('extracting_barcode'));
      });
    });
  });

  group('Workflow Integration with UI Tests', () {
    test('should provide proper stage updates for UI feedback', () {
      final stages = <String>[];
      final details = <String>[];

      // Simulate stage updates
      NewScanWorkflowService._updateStage(
        (stage, detail) {
          stages.add(stage);
          details.add(detail);
        },
        'checking_database',
        'Checking database for existing product...',
      );

      expect(stages.last, equals('checking_database'));
      expect(details.last, equals('Checking database for existing product...'));
    });
  });
}
