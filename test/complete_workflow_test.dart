import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';

import '../lib/main.dart';
import '../lib/screens/scan_screen.dart';
import '../lib/widgets/enhanced_camera_scan_widget.dart';
import '../lib/widgets/enhanced_safety_display_widget.dart';
import '../lib/providers/scan_workflow_provider.dart';
import '../lib/services/new_scan_workflow_service.dart';
import '../lib/models/product.dart';
import '../lib/models/ingredient.dart';

// Generate mocks for testing
@GenerateMocks([NewScanWorkflowService])
import 'complete_workflow_test.mocks.dart';

void main() {
  group('Complete Workflow Integration Tests', () {
    late MockNewScanWorkflowService mockWorkflowService;

    setUp(() {
      mockWorkflowService = MockNewScanWorkflowService();
    });

    testWidgets('should display scan screen with enhanced camera widget', (WidgetTester tester) async {
      // Build the app
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: const ScanScreen(),
          ),
        ),
      );

      // Verify scan screen is displayed
      expect(find.text('Scan Product'), findsOneWidget);
      expect(find.byType(EnhancedCameraScanWidget), findsOneWidget);
    });

    testWidgets('should show workflow progress during scanning', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: Consumer(
              builder: (context, ref, child) {
                final workflowState = ref.watch(scanWorkflowControllerProvider);
                return Scaffold(
                  body: Column(
                    children: [
                      if (workflowState.isScanning)
                        LinearProgressIndicator(value: workflowState.progress),
                      Text('Stage: ${workflowState.currentStage}'),
                      Text('Detail: ${workflowState.stageDetail}'),
                    ],
                  ),
                );
              },
            ),
          ),
        ),
      );

      // Get the workflow controller
      final container = ProviderContainer();
      final controller = container.read(scanWorkflowControllerProvider.notifier);

      // Simulate workflow execution
      controller.executeWorkflow(barcode: '1234567890123');
      await tester.pump();

      // Verify progress indicator is shown
      expect(find.byType(LinearProgressIndicator), findsOneWidget);
      expect(find.textContaining('Stage:'), findsOneWidget);

      container.dispose();
    });

    group('Safety Display Widget Tests', () {
      testWidgets('should display GOOD badge for safe products', (WidgetTester tester) async {
        final safeProduct = Product(
          id: 'test-1',
          name: 'Safe Product',
          barcode: '123456789',
          safetyScore: 90,
          safetyRating: 'good',
          ingredients: [
            Ingredient(
              id: '1',
              name: 'Water',
              safetyLevel: 'green',
              createdAt: DateTime.now(),
              updatedAt: DateTime.now(),
            ),
          ],
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: EnhancedSafetyDisplayWidget(product: safeProduct),
            ),
          ),
        );

        // Verify GOOD badge is displayed
        expect(find.text('GOOD'), findsOneWidget);
        expect(find.byIcon(Icons.check_circle), findsOneWidget);
      });

      testWidgets('should display NOT RECOMMENDED badge for unsafe products', (WidgetTester tester) async {
        final unsafeProduct = Product(
          id: 'test-2',
          name: 'Unsafe Product',
          barcode: '987654321',
          safetyScore: 20,
          safetyRating: 'not recommended',
          ingredients: [
            Ingredient(
              id: '1',
              name: 'Dangerous Chemical',
              safetyLevel: 'red',
              createdAt: DateTime.now(),
              updatedAt: DateTime.now(),
            ),
          ],
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: EnhancedSafetyDisplayWidget(product: unsafeProduct),
            ),
          ),
        );

        // Verify NOT RECOMMENDED badge is displayed
        expect(find.text('NOT RECOMMENDED'), findsOneWidget);
        expect(find.byIcon(Icons.warning), findsOneWidget);
      });

      testWidgets('should display ingredients with correct color coding', (WidgetTester tester) async {
        final mixedProduct = Product(
          id: 'test-3',
          name: 'Mixed Product',
          barcode: '555666777',
          safetyScore: 60,
          safetyRating: 'good',
          ingredients: [
            Ingredient(
              id: '1',
              name: 'Safe Ingredient',
              safetyLevel: 'green',
              createdAt: DateTime.now(),
              updatedAt: DateTime.now(),
            ),
            Ingredient(
              id: '2',
              name: 'Caution Ingredient',
              safetyLevel: 'yellow',
              createdAt: DateTime.now(),
              updatedAt: DateTime.now(),
            ),
          ],
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: EnhancedSafetyDisplayWidget(product: mixedProduct),
            ),
          ),
        );

        // Verify ingredients are displayed with color coding
        expect(find.text('Safe Ingredient'), findsOneWidget);
        expect(find.text('Caution Ingredient'), findsOneWidget);
        expect(find.text('GREEN: Safe'), findsOneWidget);
        expect(find.text('YELLOW: Caution'), findsOneWidget);
      });
    });

    group('Workflow State Management Tests', () {
      test('should track workflow stages correctly', () {
        final container = ProviderContainer();
        final controller = container.read(scanWorkflowControllerProvider.notifier);

        // Initial state
        expect(controller.state.isIdle, true);
        expect(controller.state.currentStage, 'idle');

        // Simulate stage updates
        controller._handleStageUpdate('checking_database', 'Checking database...');
        expect(controller.state.currentStage, 'checking_database');
        expect(controller.state.stageDetail, 'Checking database...');

        controller._handleStageUpdate('analyzing_safety', 'Analyzing safety...');
        expect(controller.state.currentStage, 'analyzing_safety');
        expect(controller.state.progress, greaterThan(0.5));

        container.dispose();
      });

      test('should handle errors correctly', () {
        final container = ProviderContainer();
        final controller = container.read(scanWorkflowControllerProvider.notifier);

        // Test camera error
        controller.handleCameraError('Camera permission denied');
        expect(controller.state.hasError, true);
        expect(controller.state.currentStage, 'camera_error');
        expect(controller.getErrorType(), 'camera_error');

        // Test API error
        controller.handleAPIError('Network timeout');
        expect(controller.state.currentStage, 'api_error');
        expect(controller.getErrorType(), 'api_error');

        container.dispose();
      });

      test('should calculate progress correctly', () {
        final container = ProviderContainer();
        final controller = container.read(scanWorkflowControllerProvider.notifier);

        // Test progress calculation
        expect(controller._calculateProgress('extracting_barcode'), 0.1);
        expect(controller._calculateProgress('checking_database'), 0.2);
        expect(controller._calculateProgress('analyzing_safety'), 0.8);
        expect(controller._calculateProgress('complete'), 1.0);

        container.dispose();
      });
    });

    group('Error Handling Tests', () {
      testWidgets('should display appropriate error widgets', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: Column(
                children: [
                  CameraErrorWidget(
                    onRetry: () {},
                    onOpenSettings: () {},
                  ),
                  BarcodeDetectionErrorWidget(
                    onRetry: () {},
                    onManualEntry: () {},
                  ),
                ],
              ),
            ),
          ),
        );

        // Verify error widgets are displayed
        expect(find.text('Camera Initialization Failed'), findsOneWidget);
        expect(find.text('Barcode Detection Failed'), findsOneWidget);
        expect(find.text('Try Again'), findsAtLeastNWidgets(2));
      });
    });

    group('End-to-End Workflow Tests', () {
      testWidgets('should complete full workflow from scan to product display', (WidgetTester tester) async {
        // Create a test product
        final testProduct = Product(
          id: 'test-product',
          name: 'Test Product',
          barcode: '1234567890123',
          safetyScore: 85,
          safetyRating: 'good',
          ingredients: [
            Ingredient(
              id: '1',
              name: 'Water',
              safetyLevel: 'green',
              createdAt: DateTime.now(),
              updatedAt: DateTime.now(),
            ),
          ],
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        // Mock the workflow service
        when(mockWorkflowService.executeWorkflow(
          barcode: anyNamed('barcode'),
          userId: anyNamed('userId'),
          imagePath: anyNamed('imagePath'),
          onStageUpdate: anyNamed('onStageUpdate'),
        )).thenAnswer((_) async => testProduct);

        await tester.pumpWidget(
          ProviderScope(
            child: MaterialApp(
              home: const ScanScreen(),
            ),
          ),
        );

        // Verify scan screen is displayed
        expect(find.text('Scan Product'), findsOneWidget);

        // Note: Full camera integration testing would require additional setup
        // This test verifies the UI structure is correct
      });
    });

    group('Safety Logic Tests', () {
      test('should correctly determine product safety rating', () {
        // Test RED ingredient scenario
        final redIngredients = [
          Ingredient(
            id: '1',
            name: 'Safe',
            safetyLevel: 'green',
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
          Ingredient(
            id: '2',
            name: 'Unsafe',
            safetyLevel: 'red',
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
        ];
        expect(NewScanWorkflowService._determineProductRating(redIngredients), 'NOT RECOMMENDED');

        // Test GREEN and YELLOW only scenario
        final safeIngredients = [
          Ingredient(
            id: '1',
            name: 'Safe',
            safetyLevel: 'green',
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
          Ingredient(
            id: '2',
            name: 'Caution',
            safetyLevel: 'yellow',
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
        ];
        expect(NewScanWorkflowService._determineProductRating(safeIngredients), 'GOOD');

        // Test empty ingredients
        expect(NewScanWorkflowService._determineProductRating([]), 'NOT RECOMMENDED');
      });
    });
  });
}
