import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:go_router/go_router.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'router.dart'; // Import the public router
import 'constants/mcp_notifier.dart'; // Import the notifier

import 'screens/splash_screen.dart';
import 'screens/home_screen.dart';
import 'screens/search_screen.dart';
import 'screens/profile_screen.dart';
import 'screens/product_detail_screen.dart';
import 'screens/auth/login_screen.dart';
import 'screens/auth/register_screen.dart';
import 'constants/app_theme.dart';
import 'services/supabase_init_service.dart';
import 'services/gemini_service.dart';
// Old services removed - using new workflow service
import 'routes/app_router.dart'; // Make sure this import is correct


void main() async {
  // Ensure Flutter bindings are initialized
  WidgetsFlutterBinding.ensureInitialized();
  
  SystemChrome.setSystemUIOverlayStyle(
    const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarIconBrightness: Brightness.light,
      statusBarBrightness: Brightness.dark,
      systemNavigationBarColor: Colors.white,
      systemNavigationBarIconBrightness: Brightness.dark,
    ),
  );
  
  try {
    await dotenv.load(fileName: ".env");
    print("Environment variables loaded successfully");
  } catch (e) {
    print("Error loading .env file: $e");
    print("Using default fallback values for environment variables");
  }
  
  await Hive.initFlutter();
  bool mcpTablesAvailable = false;
  
  try {
    // Initialize Supabase with enhanced error handling
    final initialized = await SupabaseInitService.initialize();
    if (initialized) {
      print("✅ Supabase core connection established");
      
      // Check MCP tables availability
      mcpTablesAvailable = await SupabaseInitService.checkMcpTables();
      if (mcpTablesAvailable) {
        print("✅ MCP tables for enhanced analysis are available");
      } else {
        print("⚠️ Warning: MCP tables missing - basic functionality only");
        print("Run the SQL migration from Supabase/supabase_migration.sql to enable enhanced features");
      }
      
      // Initialize Gemini service
      final geminiInitialized = await GeminiService.initialize();
      if (geminiInitialized) {
        print("✅ Gemini API initialized successfully");
      } else {
        print("⚠️ Warning: Gemini API not configured - using fallback analysis");
      }
    } else {
      print("❌ Error: Supabase initialization failed");
      print("Check your .env file and Supabase credentials");
    }
  } catch (e) {
    print("❌ Critical error initializing backend services: $e");
  }
  
  mcpTablesAvailableNotifier.value = mcpTablesAvailable;
  
  runApp(const ProviderScope(
    child: SafeScanApp()
  ));
}

class SafeScanApp extends StatelessWidget {
  const SafeScanApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp.router(
      title: 'SafeScan',
      debugShowCheckedModeBanner: false,
      theme: ThemeData(
        primarySwatch: Colors.green,
        fontFamily: 'Roboto',
        scaffoldBackgroundColor: Colors.white,
        appBarTheme: const AppBarTheme(
          backgroundColor: Colors.white,
          foregroundColor: Colors.black,
          elevation: 0,
        ),
      ),
      routerConfig: AppRouter.router, // Use the router from app_router.dart
    );
  }
}
