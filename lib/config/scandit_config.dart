/// Scandit SDK configuration
/// Replace the license key with your actual Scandit license key
class ScanditConfig {
  // TODO: Replace with your actual Scandit license key from https://ssl.scandit.com/dashboard/
  static const String licenseKey = 'YOUR_SCANDIT_LICENSE_KEY_HERE';
  
  // Scandit SDK configuration
  static const bool enableDebugMode = true;
  static const int scanTimeoutMs = 5000;
  
  /// Validate that the license key has been configured
  static bool get isConfigured => licenseKey != 'YOUR_SCANDIT_LICENSE_KEY_HERE';
  
  /// Get the license key with validation
  static String getLicenseKey() {
    if (!isConfigured) {
      throw Exception(
        'Scandit license key not configured. '
        'Please set your license key in lib/config/scandit_config.dart'
      );
    }
    return licenseKey;
  }
}
