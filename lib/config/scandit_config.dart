/// Scandit SDK configuration
/// Replace the license key with your actual Scandit license key
class ScanditConfig {
  // Scandit license key configured
  static const String licenseKey = 'AmvGIgaeIAXOHM7GgOKBiZo2+u5sIIwqo0Fj1a8Ln8z3aTLJfXmTyc9K9wi6ZLzmI0tc/KRNQSwRWwu9xSjchhJXL1cxC3Z5O36hJI07c1cNCv3PBBgURSI5YTSrZbrh23cQLgRqIyOLTJzOFFl/aiNXI1YWNVPZfVYyy+dG/4kqeRBINkZCGfsyTjtpRcw64k/tgf1Pu0PvD8vD7USfMZwcCf1fcrT9h17pufJUgMAyLaIbyErQydwgePYGTGRGx2rErB1vZ6J3cHc/uyw7Sm1YcRG8NguJeSrJrkQtuZHZBEPQe0ZGbVEVhRAgb8dymFfAJVhYmEhrRc64KzoJBad40frNWhH6OG2k1yVcef41E4WIgAIY5opwx2RpbbaER2H0Vr97dJkCOSenHEAwLkoDdCv9ScPqGk/n9nZw2aG7R3f2dCj2MocarLLLfGZpIUYdXYpd5Y6XUQ+uDVKCVe5Eur4qaEZy/hO0XnxoJU2bVMStyTUdgANa1S2kdVhwE1CHnNhV4KqAS+PsyneyshYFIUbjT5+9V3ylw3FdIRm6LTkKgkVUm1tils6jTs+RA3W+RohWxcGBQagE439DfUVH4eiDWmqJ61S9+BN4fm2OcvEzy1ZfpG4kX7O+b1sWg01CbJNc3FF7OwkrvF11WupLT16kbq2L+X7BNvZ5Z1B3dTbnAVVfYEQQhUNHfYJS6UnJKeVLH1mdaP+ipkJzmDF8a/fVbGzEGhp/XE93zpP2eSRjmXZzDbx63bvWVjWWZxMt9ypiaBaBRvvJ5EXIcUxuvCDVRsqQR1np3+0ZcQ6gajjAGlWvIvEadninXWZ9hkF9s8l8FGOEU/1DCHArGrJ7S3JNQ+YcgT6AgIBATHwVcXVaNTFHpSI7I4IafSq3ljDSMvYQq3FBVag5RkKDKx93B9y0TDKcaW67101+2w6gecRGkX6ySZJhvD3VVyViTXGspaBHlX9Xf0pJs22eG2tkAqsjI5ad4VqqhQGwLkNGyHLtAkJauvrKiXBUVWDIsKCcxiiyaNWgqNFsKXoCqz2yPHQmbRi2xUPiAVN0k/MFm9IYVGg5z2r+M0YAm89t8w5B64sfSdUB4bmuDraiC3oqaM/xqkNzws/U0g+UxmaFdNwNOO5KAHMt4dP8i+FkTqUawvstQ2CvuczD/0bzubIeepWjqL06BQHcTQitnVcaSGvynKH6DMk4LYgAnCkcHRbqMSacbE4Ut9dh/Y0i6p93+jwu8lg1FMVifmJpm6w1wChbqjYTkm96Qi/cmifF7oxsuC+bnCKo9XH+4Ouv69WCPtNkrg3+x4OPNl8u2ahCchi6l3U4z6kqbYi0fgZamuX9pyfpG8hCq8OhxtS9Bn3kyRIc4RVn9oIixIjBdK1OM4g2Ur/UEhpWul1+ngl3SOqowluEcte6uEKQTVaVSsrhyPIsWW3T0AcveshVFZIaHYag+RVieDTXAq+7tQySpqDso/y0/IRn84zUJwoCBXt2CGvLsVsHigIRcId52hUCEdF4mYMCWfoEV9ZEhkfivCqt6cKUKFlZ4E+TFc9rFhIqOHsZX4kuQZQ+khnVOo26WsqvmdnjphuX/nK/zvxSqLlNpXgUk39Jowxt3gnfym5QRuIxqvJRa97yuqk7nIyCCLJaZRPaBGVdBq2cs7k02kMaRYbQ';
  
  // Scandit SDK configuration
  static const bool enableDebugMode = true;
  static const int scanTimeoutMs = 5000;
  
  /// Validate that the license key has been configured
  static bool get isConfigured => licenseKey != 'YOUR_SCANDIT_LICENSE_KEY_HERE';
  
  /// Get the license key with validation
  static String getLicenseKey() {
    if (!isConfigured) {
      throw Exception(
        'Scandit license key not configured. '
        'Please set your license key in lib/config/scandit_config.dart'
      );
    }
    return licenseKey;
  }
}
