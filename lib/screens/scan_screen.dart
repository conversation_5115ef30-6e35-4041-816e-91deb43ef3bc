import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../widgets/enhanced_camera_scan_widget.dart';
import '../providers/product_provider.dart';
import '../providers/scan_workflow_provider.dart';
import '../models/product.dart';
import '../services/logger_service.dart';

class ScanScreen extends ConsumerStatefulWidget {
  const ScanScreen({super.key});

  @override
  ConsumerState<ScanScreen> createState() => _ScanScreenState();
}

class _ScanScreenState extends ConsumerState<ScanScreen> {

  @override
  Widget build(BuildContext context) {

    return Scaffold(
      appBar: AppBar(
        title: const Text('Scan Product'),
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.pop(),
        ),
      ),
      body: EnhancedCameraScanWidget(
        onProductScanned: _handleProductScanned,
        onError: _handleScanError,
      ),
    );
  }

  /// Handle successful product scan from the new workflow
  void _handleProductScanned(Product product) {
    LoggerService.info('Product scanned successfully: ${product.name}');

    // Update the product provider with the scanned product
    final productProvider = ref.read(productControllerProvider.notifier);
    productProvider.setCurrentProduct(product);

    // Navigate to product details screen
    context.go('/product/${product.id}');
  }

  /// Handle scan errors from the new workflow
  void _handleScanError(String error) {
    LoggerService.error('Scan error: $error');

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(error),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 4),
          action: SnackBarAction(
            label: 'Retry',
            textColor: Colors.white,
            onPressed: () {
              // The camera widget will automatically resume scanning
            },
          ),
        ),
      );
    }
  }
}
