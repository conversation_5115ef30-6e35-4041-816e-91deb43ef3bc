import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../widgets/scandit_camera_scan_widget.dart';
import '../providers/product_provider.dart';
import '../models/product.dart';
import '../services/logger_service.dart';

class ScanScreen extends ConsumerStatefulWidget {
  const ScanScreen({super.key});

  @override
  ConsumerState<ScanScreen> createState() => _ScanScreenState();
}

class _ScanScreenState extends ConsumerState<ScanScreen> {

  @override
  Widget build(BuildContext context) {
    // Listen to product provider state for navigation
    ref.listen<AsyncValue<Product?>>(productProvider, (previous, next) {
      next.when(
        data: (product) {
          if (product != null) {
            // Navigate to product details screen
            context.go('/product/${product.id}');
          }
        },
        loading: () {
          // Loading state is handled by the widget
        },
        error: (error, stack) {
          // Error state is handled by the widget
          LoggerService.error('Product scan error: $error');
        },
      );
    });

    // Use the new Scandit-powered scanning widget
    return const ScanditCameraScanWidget();
  }

  /// Handle successful product scan from the new workflow
  void _handleProductScanned(Product product) {
    LoggerService.info('Product scanned successfully: ${product.name}');

    // Update the product provider with the scanned product
    final productProvider = ref.read(productControllerProvider.notifier);
    productProvider.setCurrentProduct(product);

    // Navigate to product details screen
    context.go('/product/${product.id}');
  }

  /// Handle scan errors from the new workflow
  void _handleScanError(String error) {
    LoggerService.error('Scan error: $error');

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(error),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 4),
          action: SnackBarAction(
            label: 'Retry',
            textColor: Colors.white,
            onPressed: () {
              // The camera widget will automatically resume scanning
            },
          ),
        ),
      );
    }
  }
}
