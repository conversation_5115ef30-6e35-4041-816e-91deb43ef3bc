import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../widgets/simple_barcode_scanner_widget.dart';
import '../providers/product_provider.dart';
import '../models/product.dart';
import '../services/logger_service.dart';

class ScanScreen extends ConsumerStatefulWidget {
  const ScanScreen({super.key});

  @override
  ConsumerState<ScanScreen> createState() => _ScanScreenState();
}

class _ScanScreenState extends ConsumerState<ScanScreen> {

  @override
  Widget build(BuildContext context) {
    // Listen to product provider state for navigation
    ref.listen<AsyncValue<Product?>>(productControllerProvider, (previous, next) {
      next.when(
        data: (product) {
          if (product != null) {
            // Navigate to product details screen
            context.go('/product/${product.id}');
          }
        },
        loading: () {
          // Loading state is handled by the widget
        },
        error: (error, stack) {
          // Error state is handled by the widget
          LoggerService.error('Product scan error: $error');
        },
      );
    });

    // Use simple Scandit BarcodeCapture for retail product scanning
    return const SimpleBarcodeScanner();
  }
}
