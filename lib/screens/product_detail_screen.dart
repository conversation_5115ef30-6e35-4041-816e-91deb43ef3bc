import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:go_router/go_router.dart';
import '../constants/app_theme.dart';
import '../models/product.dart';
import '../models/ingredient.dart';
import '../providers/product_provider.dart';
// Old service removed - using new workflow service
import '../widgets/loading_overlay.dart';
import '../widgets/safety_score_widget.dart';
import '../widgets/ingredient_card.dart';
import '../widgets/product_card.dart';
import '../widgets/enhanced_safety_display_widget.dart';

class ProductDetailScreen extends ConsumerStatefulWidget {
  final String productId;

  const ProductDetailScreen({
    super.key,
    required this.productId,
  });

  @override
  ConsumerState<ProductDetailScreen> createState() => _ProductDetailScreenState();
}

class _ProductDetailScreenState extends ConsumerState<ProductDetailScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  bool _isSaved = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _checkIfSaved();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _checkIfSaved() async {
    final controller = ref.read(savedProductsControllerProvider.notifier);
    final isSaved = await controller.isProductSaved(widget.productId);
    setState(() {
      _isSaved = isSaved;
    });
  }

  @override
  Widget build(BuildContext context) {
    final productAsync = ref.watch(productByIdProvider(widget.productId));

    return Scaffold(
      body: productAsync.when(
        data: (product) => product != null 
            ? _buildProductDetail(product)
            : _buildNotFoundView(),
        loading: () => const Center(
          child: CircularProgressIndicator(color: AppTheme.primaryGreen),
        ),
        error: (error, stack) => _buildErrorView(error.toString()),
      ),
    );
  }

  Widget _buildProductDetail(Product product) {
    final colorScheme = Theme.of(context).colorScheme;
    // Move this variable declaration above the widget tree:
    final analyzedIngredients = (product.ingredientsJson?['ingredients'] as List?)?.map((e) => AnalyzedIngredient.fromJson(e)).toList() ?? [];
    return Scaffold(
      backgroundColor: colorScheme.background,
      appBar: AppBar(
        backgroundColor: colorScheme.surface,
        elevation: 0,
        title: const Text('Product Details'),
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 28),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Main product card (Yuka style)
            ProductCard(
              product: product,
              showStatusBadge: true,
              onTap: null,
              onUnsave: null,
            ),
            const SizedBox(height: 28),
            // Ingredients, analysis, etc. (keep structure, update style)
            _buildSectionTitle(context, 'Ingredients'),
            _buildIngredientsList(context, analyzedIngredients),
            const SizedBox(height: 28),
            _buildSectionTitle(context, 'Safety Analysis'),
            EnhancedSafetyDisplayWidget(
              product: product,
              showDetailedAnalysis: false, // Compact view for main screen
            ),
            const SizedBox(height: 28),
            _buildOverviewTab(product),
            const SizedBox(height: 28),
            _buildIngredientsTab(product),
            const SizedBox(height: 28),
            _buildAnalysisTab(product),
            const SizedBox(height: 28),
            _buildAlternativesTab(product),
          ],
        ),
      ),
    );
  }

  Widget _buildSliverAppBar(Product product) {
    return SliverAppBar(
      expandedHeight: 300,
      pinned: true,
      backgroundColor: AppTheme.primaryGreen,
      leading: IconButton(
        icon: const Icon(Icons.arrow_back, color: Colors.white),
        onPressed: () => context.pop(),
      ),
      actions: [
        IconButton(
          icon: Icon(
            _isSaved ? Icons.bookmark : Icons.bookmark_border,
            color: Colors.white,
          ),
          onPressed: () => _toggleSave(product.id),
        ),
        IconButton(
          icon: const Icon(Icons.share, color: Colors.white),
          onPressed: () => _shareProduct(product),
        ),
      ],
      flexibleSpace: FlexibleSpaceBar(
        background: product.imageUrl != null
            ? CachedNetworkImage(
                imageUrl: product.imageUrl!,
                fit: BoxFit.cover,
                placeholder: (context, url) => Container(
                  color: Colors.grey[300],
                  child: const Center(
                    child: CircularProgressIndicator(color: AppTheme.primaryGreen),
                  ),
                ),
                errorWidget: (context, url, error) => _buildDefaultProductImage(),
              )
            : _buildDefaultProductImage(),
      ),
    );
  }

  Widget _buildDefaultProductImage() {
    return Container(
      color: Colors.grey[300],
      child: const Center(
        child: Icon(
          Icons.inventory_2_outlined,
          size: 80,
          color: Colors.grey,
        ),
      ),
    );
  }

  Widget _buildProductHeader(Product product) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      product.name,
                      style: const TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    if (product.brand?.isNotEmpty == true) ...[
                      const SizedBox(height: 4),
                      Text(
                        product.brand!,
                        style: TextStyle(
                          fontSize: 18,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ],
                ),
              ),
              SafetyScoreWidget(
                score: product.safetyScore ?? 0,
                size: 80,
              ),
            ],
          ),
          const SizedBox(height: 16),
          if (product.barcode?.isNotEmpty == true)
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(20),
              ),
              child: Text(
                'Barcode: ${product.barcode}',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[700],
                  fontFamily: 'monospace',
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: TabBar(
        controller: _tabController,
        labelColor: AppTheme.primaryBlue,
        unselectedLabelColor: Colors.grey,
        indicatorColor: AppTheme.primaryBlue,
        tabs: const [
          Tab(text: 'Overview'),
          Tab(text: 'Ingredients'),
          Tab(text: 'Analysis'),
          Tab(text: 'Alternatives'),
        ],
      ),
    );
  }

  Widget _buildTabContent(Product product) {
    return SizedBox(
      height: 600,
      child: TabBarView(
        controller: _tabController,
        children: [
          _buildOverviewTab(product),
          _buildIngredientsTab(product),
          _buildAnalysisTab(product),
          _buildAlternativesTab(product),
        ],
      ),
    );
  }

  Widget _buildOverviewTab(Product product) {
    final bool isVerifiedCurrent = product.matchStatsJson?['verified_current'] == true;
    final String? verificationTimestamp = product.matchStatsJson?['verification_timestamp'];
    final List<String> dataSources = (product.matchStatsJson?['data_sources'] as List?)
        ?.map((source) => source.toString())
        .toList() ?? ['Database lookup'];
    final double confidenceScore = (product.matchStatsJson?['confidence_score'] as num?)?.toDouble() ?? 0.8;
    
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSafeScanResultCard(product),
          
          const SizedBox(height: 24),
          
          _buildSafetyCriteriaCard(product),
          
          const SizedBox(height: 24),
          
          _buildIngredientsOverviewCard(product),
          
          const SizedBox(height: 24),
          
          _buildDataSourcesCard(dataSources, verificationTimestamp),
        ],
      ),
    );
  }

  Widget _buildSafeScanResultCard(Product product) {
    final isSafe = product.isSafe;
    final color = isSafe ? AppTheme.primaryBlue : AppTheme.dangerRed;
    final label = isSafe ? 'SAFE FOR USE' : 'NOT RECOMMENDED';
    final description = isSafe
        ? 'This product meets our safety criteria'
        : 'This product contains ingredients of concern';
    return Container(
      decoration: BoxDecoration(
        color: color.withOpacity(0.13),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: color.withOpacity(0.4), width: 1.5),
        boxShadow: [
          BoxShadow(
            color: color.withOpacity(0.08),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      padding: const EdgeInsets.all(18),
      child: Column(
        children: [
          Row(
            children: [
              Icon(
                isSafe ? Icons.check_circle : Icons.error,
                color: color,
                size: 36,
              ),
              const SizedBox(width: 16),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 6),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.13),
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(color: color.withOpacity(0.4)),
                ),
                child: Text(
                  label,
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                    color: color,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 10),
          Text(
            description,
            style: TextStyle(
              fontSize: 14,
              color: color.withOpacity(0.8),
            ),
          ),
          const SizedBox(height: 18),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              _buildPillMetric('Safety Score', '${product.safetyScore ?? 0}%', (product.safetyScore ?? 0) >= 70 ? AppTheme.primaryBlue : AppTheme.warningOrange),
              _buildPillMetric('Ingredients', '${product.ingredientsJson?['ingredients']?.length ?? 0}', AppTheme.primaryBlue),
              _buildPillMetric('Concerns', '${product.concernsCount ?? 0}', (product.concernsCount ?? 0) == 0 ? AppTheme.primaryBlue : AppTheme.dangerRed),
            ],
          ),
        ],
      ),
    );
  }
  
  Widget _buildPillMetric(String label, String value, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 14, vertical: 8),
      decoration: BoxDecoration(
        color: color.withOpacity(0.13),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Text(
            value,
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 16,
              color: color,
            ),
          ),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: color.withOpacity(0.8),
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildSafetyCriteriaCard(Product product) {
    final criteriaMet = product.safetyCriteriaJson?['criteria_met'] as List? ?? [];
    final criteriaFailed = product.safetyCriteriaJson?['criteria_failed'] as List? ?? [];
    
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.check_circle,
                  color: AppTheme.primaryBlue,
                  size: 24,
                ),
                const SizedBox(width: 12),
                const Text(
                  'Safety Criteria',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.darkBlue,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ...criteriaMet.map((criteria) => _buildCriteriaItem(
              criteria.toString(), 
              true,
            )),
            ...criteriaFailed.map((criteria) => _buildCriteriaItem(
              criteria.toString(), 
              false,
            )),
          ],
        ),
      ),
    );
  }
  
  Widget _buildCriteriaItem(String criteria, bool met) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(2),
            decoration: BoxDecoration(
              color: met ? AppTheme.primaryBlue.withOpacity(0.1) : Colors.red.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              met ? Icons.check_circle : Icons.cancel,
              color: met ? AppTheme.primaryBlue : Colors.red,
              size: 18,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              criteria,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[800],
              ),
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildCriteriaCard(List<String> criteriaMet, List<String> criteriaFailed) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Safety Criteria',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Theme.of(context).colorScheme.onBackground,
          ),
        ),
        const SizedBox(height: 12),
        ...criteriaMet.map((criteria) => _buildCriteriaPill(criteria, true)),
        ...criteriaFailed.map((criteria) => _buildCriteriaPill(criteria, false)),
      ],
    );
  }

  Widget _buildCriteriaPill(String criteria, bool met) {
    final color = met ? AppTheme.primaryBlue : AppTheme.dangerRed;
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: color.withOpacity(0.13),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Row(
        children: [
          Icon(met ? Icons.check_circle : Icons.cancel, color: color, size: 18),
          const SizedBox(width: 10),
          Expanded(
            child: Text(
              criteria,
              style: TextStyle(
                fontSize: 14,
                color: color,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildIngredientsOverviewCard(Product product) {
    final ingredients = product.ingredients ?? [];
    final List<Ingredient> concernIngredients = ingredients
        .where((ing) => ing.safetyRating != null && ing.safetyRating! < 3)
        .toList();
    
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.science,
                  color: AppTheme.primaryBlue,
                  size: 24,
                ),
                const SizedBox(width: 12),
                const Text(
                  'Ingredients Overview',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.darkBlue,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            if (concernIngredients.isNotEmpty) ...[
              const Text(
                'Ingredients of Concern',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: Colors.red,
                ),
              ),
              const SizedBox(height: 8),
              ...concernIngredients.take(3).map((ing) => _buildIngredientPreview(ing)),
              if (concernIngredients.length > 3)
                Padding(
                  padding: const EdgeInsets.only(top: 8),
                  child: Text(
                    '+ ${concernIngredients.length - 3} more ingredients of concern',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600],
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                ),
            ] else ...[
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.green.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Row(
                  children: [
                    Icon(Icons.check_circle, color: AppTheme.primaryGreen, size: 20),
                    SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'No ingredients of concern detected',
                        style: TextStyle(
                          color: AppTheme.primaryGreen,
                          fontSize: 14,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
  
  Widget _buildIngredientPreview(Ingredient ingredient) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Container(
            width: 16,
            height: 16,
            decoration: BoxDecoration(
              color: _getIngredientColor(ingredient.safetyRating),
              shape: BoxShape.circle,
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              ingredient.name,
              style: const TextStyle(fontSize: 14),
            ),
          ),
          Text(
            ingredient.concernLevel ?? 'Unknown',
            style: TextStyle(
              fontSize: 12,
              color: _getIngredientColor(ingredient.safetyRating),
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
  
  Color _getIngredientColor(int? safetyRating) {
    if (safetyRating == null) return Colors.grey;
    switch (safetyRating) {
      case 1:
        return Colors.red;
      case 2:
        return Colors.orange;
      case 3:
        return Colors.yellow.shade700;
      case 4:
        return Colors.lightGreen;
      case 5:
        return AppTheme.primaryGreen;
      default:
        return Colors.grey;
    }
  }
  
  Widget _buildDataSourcesCard(List<String> dataSources, String? verificationTimestamp) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Data Sources',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            ...dataSources.map((source) => Padding(
              padding: const EdgeInsets.only(bottom: 8),
              child: Row(
                children: [
                  const Icon(Icons.data_usage, size: 16, color: Colors.blue),
                  const SizedBox(width: 8),
                  Text(
                    source,
                    style: const TextStyle(fontSize: 14),
                  ),
                ],
              ),
            )),
            if (verificationTimestamp != null) ...[
              const Divider(height: 24),
              Row(
                children: [
                  const Icon(Icons.update, size: 16, color: Colors.grey),
                  const SizedBox(width: 8),
                  Text(
                    'Last verified: ${_formatTimestamp(verificationTimestamp)}',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }
  
  String _formatTimestamp(String timestamp) {
    try {
      final dateTime = DateTime.parse(timestamp);
      final now = DateTime.now();
      final difference = now.difference(dateTime);
      
      if (difference.inMinutes < 60) {
        return '${difference.inMinutes} minutes ago';
      } else if (difference.inHours < 24) {
        return '${difference.inHours} hours ago';
      } else {
        return '${difference.inDays} days ago';
      }
    } catch (e) {
      return timestamp;
    }
  }

  Widget _buildIngredientsTab(Product product) {
    final ingredientsData = product.ingredientsJson;
    if (ingredientsData == null) {
      return const Center(child: Text('No ingredient data available'));
    }

    final ingredients = (ingredientsData['ingredients'] as List?)
        ?.map((e) => AnalyzedIngredient.fromJson(e))
        .toList() ?? [];

    if (ingredients.isEmpty) {
      return const Center(child: Text('No ingredients found'));
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: ingredients.length,
      itemBuilder: (context, index) {
        return IngredientCard(ingredient: ingredients[index]);
      },
    );
  }

  Widget _buildAnalysisTab(Product product) {
    // Use the new Enhanced Safety Display Widget that follows the Mermaid workflow
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: EnhancedSafetyDisplayWidget(
        product: product,
        showDetailedAnalysis: true,
      ),
    );
  }

  Widget _buildLegacyAnalysisTab(Product product) {
    final concerns = product.nutritionConcernsJson;
    final ingredientsData = product.ingredientsJson;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSafetyMetrics(product),
          const SizedBox(height: 24),
          if (concerns != null) _buildConcerns(concerns),
          const SizedBox(height: 24),
          if (ingredientsData != null) _buildIngredientStats(ingredientsData),
        ],
      ),
    );
  }

  Widget _buildSafetyMetrics(Product product) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Safety Assessment',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              SafetyScoreWidget(score: product.safetyScore ?? 0, size: 60),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Safety Score: ${product.safetyScore ?? 0}/100',
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      _getSafetyDescription(product.safetyRating ?? 'unknown'),
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  String _getSafetyDescription(String rating) {
    switch (rating.toLowerCase()) {
      case 'safe':
        return 'This product contains ingredients that are considered safe for most people.';
      case 'generally safe':
        return 'This product is generally safe but may contain some ingredients of moderate concern.';
      case 'caution':
        return 'Exercise caution with this product. It contains ingredients that may pose health risks.';
      case 'avoid':
        return 'Consider avoiding this product. It contains ingredients with significant safety concerns.';
      case 'unsafe':
        return 'This product contains ingredients that are considered unsafe.';
      default:
        return 'Safety analysis is inconclusive for this product.';
    }
  }

  Widget _buildConcerns(Map<String, dynamic> concerns) {
    final concernsList = (concerns['concerns'] as List?)?.cast<String>() ?? [];
    final recommendations = (concerns['recommendations'] as List?)?.cast<String>() ?? [];

    if (concernsList.isEmpty && recommendations.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Health Concerns & Recommendations',
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 12),
        if (concernsList.isNotEmpty) ...[
          const Text(
            'Concerns:',
            style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
          ),
          const SizedBox(height: 8),
          ...concernsList.map((concern) => Padding(
                padding: const EdgeInsets.symmetric(vertical: 2),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Icon(Icons.warning, color: Colors.orange, size: 16),
                    const SizedBox(width: 8),
                    Expanded(child: Text(concern)),
                  ],
                ),
              )),
        ],
        if (recommendations.isNotEmpty) ...[
          const SizedBox(height: 16),
          const Text(
            'Recommendations:',
            style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
          ),
          const SizedBox(height: 8),
          ...recommendations.map((recommendation) => Padding(
                padding: const EdgeInsets.symmetric(vertical: 2),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Icon(Icons.lightbulb, color: AppTheme.primaryGreen, size: 16),
                    const SizedBox(width: 8),
                    Expanded(child: Text(recommendation)),
                  ],
                ),
              )),
        ],
      ],
    );
  }

  Widget _buildIngredientStats(Map<String, dynamic> ingredientsData) {
    final totalCount = ingredientsData['total_count'] as int? ?? 0;
    final matchedCount = ingredientsData['matched_count'] as int? ?? 0;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Ingredient Analysis',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 12),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildStatItem('Total Ingredients', totalCount.toString()),
              _buildStatItem('Analyzed', matchedCount.toString()),
              _buildStatItem('Unknown', (totalCount - matchedCount).toString()),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(String label, String value) {
    return Column(
      children: [
        Text(
          value,
          style: const TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: AppTheme.primaryGreen,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }

  Widget _buildAlternativesTab(Product product) {
    final alternativesAsync = ref.watch(productAlternativesProvider(product));

    return alternativesAsync.when(
      data: (alternatives) => alternatives.isNotEmpty
          ? ListView.builder(
              padding: const EdgeInsets.all(16),
              itemCount: alternatives.length,
              itemBuilder: (context, index) {
                final alternative = alternatives[index];
                return _buildAlternativeCard(alternative);
              },
            )
          : const Center(
              child: Text('No alternatives found'),
            ),
      loading: () => const Center(
        child: CircularProgressIndicator(color: AppTheme.primaryGreen),
      ),
      error: (error, stack) => Center(
        child: Text('Error loading alternatives: $error'),
      ),
    );
  }

  Widget _buildAlternativeCard(Map<String, dynamic> alternative) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        alternative['name'] ?? 'Unknown Product',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      if (alternative['brand'] != null)
                        Text(
                          alternative['brand'],
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey[600],
                          ),
                        ),
                    ],
                  ),
                ),
                SafetyScoreWidget(
                  score: alternative['safety_score'] ?? 0,
                  size: 50,
                ),
              ],
            ),
            const SizedBox(height: 12),
            ElevatedButton(
              onPressed: () {
                // TODO: Navigate to alternative product or search
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryGreen,
              ),
              child: const Text('View Alternative'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNotFoundView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.inventory_2_outlined,
            size: 100,
            color: Colors.grey,
          ),
          const SizedBox(height: 16),
          const Text(
            'Product Not Found',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'This product could not be found in your scanned products.',
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey,
            ),
          ),
          const SizedBox(height: 32),
          ElevatedButton(
            onPressed: () => context.pop(),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.primaryGreen,
            ),
            child: const Text('Go Back'),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorView(String error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.error_outline,
            size: 100,
            color: Colors.red,
          ),
          const SizedBox(height: 16),
          const Text(
            'Error Loading Product',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            error,
            textAlign: TextAlign.center,
            style: const TextStyle(
              fontSize: 16,
              color: Colors.grey,
            ),
          ),
          const SizedBox(height: 32),
          ElevatedButton(
            onPressed: () => context.pop(),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.primaryGreen,
            ),
            child: const Text('Go Back'),
          ),
        ],
      ),
    );
  }

  void _toggleSave(String productId) async {
    final controller = ref.read(savedProductsControllerProvider.notifier);
    
    if (_isSaved) {
      await controller.removeProduct(productId);
      setState(() {
        _isSaved = false;
      });
    } else {
      await controller.saveProduct(productId);
      setState(() {
        _isSaved = true;
      });
    }
  }

  void _shareProduct(Product product) {
    // TODO: Implement sharing functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Sharing functionality coming soon')),
    );
  }

  Widget _buildSectionTitle(BuildContext context, String title) {
    final colorScheme = Theme.of(context).colorScheme;
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Text(
        title,
        style: TextStyle(
          fontSize: 20,
          fontWeight: FontWeight.bold,
          color: colorScheme.onBackground,
        ),
      ),
    );
  }

  Widget _buildIngredientsList(BuildContext context, List<AnalyzedIngredient>? ingredients) {
    if (ingredients == null || ingredients.isEmpty) {
      return const Center(child: Text('No ingredients found'));
    }
    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: ingredients.length,
      itemBuilder: (context, index) {
        return IngredientCard(ingredient: ingredients[index]);
      },
    );
  }

  Widget _buildSafetyAnalysis(BuildContext context, Map<String, dynamic>? safetyAnalysis, Product product) {
    if (safetyAnalysis == null) {
      return const Center(child: Text('No safety analysis data available'));
    }

    final criteriaMet = (safetyAnalysis['criteria_met'] as List?)?.map((e) => e.toString()).toList() ?? <String>[];
    final criteriaFailed = (safetyAnalysis['criteria_failed'] as List?)?.map((e) => e.toString()).toList() ?? <String>[];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSafetyMetrics(product),
        const SizedBox(height: 24),
        if (criteriaMet.isNotEmpty || criteriaFailed.isNotEmpty) ...[
          _buildCriteriaCard(criteriaMet, criteriaFailed),
          const SizedBox(height: 24),
        ],
        _buildIngredientsOverviewCard(product),
        const SizedBox(height: 24),
        _buildDataSourcesCard(
          (safetyAnalysis['data_sources'] as List?)?.map((source) => source.toString()).toList() ?? [],
          safetyAnalysis['verification_timestamp'] as String?,
        ),
      ],
    );
  }
}

class _ProcessStep extends StatelessWidget {
  final int number;
  final String title;
  final String description;
  final bool isComplete;
  
  const _ProcessStep({
    required this.number,
    required this.title,
    required this.description,
    required this.isComplete,
  });
  
  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          width: 24,
          height: 24,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: isComplete ? AppTheme.primaryGreen : Colors.grey[300],
          ),
          child: Center(
            child: Text(
              number.toString(),
              style: TextStyle(
                color: isComplete ? Colors.white : Colors.grey[600],
                fontWeight: FontWeight.bold,
                fontSize: 14,
              ),
            ),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 14,
                ),
              ),
              const SizedBox(height: 2),
              Text(
                description,
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
        ),
        if (isComplete)
          const Icon(
            Icons.check_circle,
            color: AppTheme.primaryGreen,
            size: 18,
          ),
      ],
    );
  }
}

extension StringExtension on String {
  String capitalize() {
    return "${this[0].toUpperCase()}${substring(1)}";
  }
} 