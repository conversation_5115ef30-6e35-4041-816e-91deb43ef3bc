import 'package:flutter/material.dart';
import '../constants/app_theme.dart';

/// Error handling widgets for the new workflow
/// Implements error scenarios from the Mermaid diagram: ER1, ER2, ER3, ER4, ER5

class WorkflowErrorWidget extends StatelessWidget {
  final String errorType;
  final String errorMessage;
  final String? actionLabel;
  final VoidCallback? onAction;
  final VoidCallback? onRetry;

  const WorkflowErrorWidget({
    Key? key,
    required this.errorType,
    required this.errorMessage,
    this.actionLabel,
    this.onAction,
    this.onRetry,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(24),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          _buildErrorIcon(),
          const SizedBox(height: 16),
          Text(
            _getErrorTitle(),
            style: const TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.red,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 12),
          Text(
            errorMessage,
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          _buildActionButtons(),
        ],
      ),
    );
  }

  Widget _buildErrorIcon() {
    IconData icon;
    Color color;

    switch (errorType) {
      case 'camera_error':
        icon = Icons.camera_alt_outlined;
        color = Colors.orange;
        break;
      case 'barcode_detection_error':
        icon = Icons.qr_code_scanner;
        color = Colors.red;
        break;
      case 'api_error':
        icon = Icons.cloud_off;
        color = Colors.blue;
        break;
      case 'safety_analysis_error':
        icon = Icons.analytics;
        color = Colors.purple;
        break;
      case 'database_error':
        icon = Icons.storage;
        color = Colors.indigo;
        break;
      default:
        icon = Icons.error_outline;
        color = Colors.red;
    }

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        shape: BoxShape.circle,
        border: Border.all(color: color, width: 2),
      ),
      child: Icon(icon, size: 48, color: color),
    );
  }

  String _getErrorTitle() {
    switch (errorType) {
      case 'camera_error':
        return 'Camera Initialization Failed';
      case 'barcode_detection_error':
        return 'Barcode Detection Failed';
      case 'api_error':
        return 'Product Database Error';
      case 'safety_analysis_error':
        return 'Safety Analysis Failed';
      case 'database_error':
        return 'Database Connection Error';
      default:
        return 'An Error Occurred';
    }
  }

  Widget _buildActionButtons() {
    return Column(
      children: [
        if (onRetry != null)
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: onRetry,
              icon: const Icon(Icons.refresh),
              label: const Text('Try Again'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryGreen,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
            ),
          ),
        if (onRetry != null && (onAction != null || actionLabel != null))
          const SizedBox(height: 12),
        if (onAction != null && actionLabel != null)
          SizedBox(
            width: double.infinity,
            child: OutlinedButton.icon(
              onPressed: onAction,
              icon: const Icon(Icons.settings),
              label: Text(actionLabel!),
              style: OutlinedButton.styleFrom(
                foregroundColor: AppTheme.primaryGreen,
                side: const BorderSide(color: AppTheme.primaryGreen),
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
            ),
          ),
      ],
    );
  }
}

/// ER1["Camera Initialization Failed"] Error Widget
class CameraErrorWidget extends StatelessWidget {
  final VoidCallback? onRetry;
  final VoidCallback? onOpenSettings;

  const CameraErrorWidget({
    Key? key,
    this.onRetry,
    this.onOpenSettings,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return WorkflowErrorWidget(
      errorType: 'camera_error',
      errorMessage: 'Unable to access camera. Please check camera permissions and ensure no other app is using the camera.',
      actionLabel: 'Open Settings',
      onAction: onOpenSettings,
      onRetry: onRetry,
    );
  }
}

/// ER2["Barcode Detection Failed"] Error Widget
class BarcodeDetectionErrorWidget extends StatelessWidget {
  final VoidCallback? onRetry;
  final VoidCallback? onManualEntry;

  const BarcodeDetectionErrorWidget({
    Key? key,
    this.onRetry,
    this.onManualEntry,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return WorkflowErrorWidget(
      errorType: 'barcode_detection_error',
      errorMessage: 'Could not detect a barcode in the image. Please ensure the barcode is clearly visible and well-lit.',
      actionLabel: 'Enter Manually',
      onAction: onManualEntry,
      onRetry: onRetry,
    );
  }
}

/// ER3["Open Food Facts API Failed"] Error Widget
class APIErrorWidget extends StatelessWidget {
  final VoidCallback? onRetry;
  final VoidCallback? onOfflineMode;

  const APIErrorWidget({
    Key? key,
    this.onRetry,
    this.onOfflineMode,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return WorkflowErrorWidget(
      errorType: 'api_error',
      errorMessage: 'Unable to retrieve product information from external databases. Please check your internet connection.',
      actionLabel: 'Continue Offline',
      onAction: onOfflineMode,
      onRetry: onRetry,
    );
  }
}

/// ER4["Safety Analysis Failed"] Error Widget
class SafetyAnalysisErrorWidget extends StatelessWidget {
  final VoidCallback? onRetry;
  final VoidCallback? onContinueAnyway;

  const SafetyAnalysisErrorWidget({
    Key? key,
    this.onRetry,
    this.onContinueAnyway,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return WorkflowErrorWidget(
      errorType: 'safety_analysis_error',
      errorMessage: 'Safety analysis could not be completed. The product information may be incomplete.',
      actionLabel: 'Continue Anyway',
      onAction: onContinueAnyway,
      onRetry: onRetry,
    );
  }
}

/// ER5["Profile Save Failed"] Error Widget
class DatabaseSaveErrorWidget extends StatelessWidget {
  final VoidCallback? onRetry;
  final VoidCallback? onContinueWithoutSaving;

  const DatabaseSaveErrorWidget({
    Key? key,
    this.onRetry,
    this.onContinueWithoutSaving,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return WorkflowErrorWidget(
      errorType: 'database_error',
      errorMessage: 'Failed to save product analysis to your history. You can still view the results.',
      actionLabel: 'Continue Without Saving',
      onAction: onContinueWithoutSaving,
      onRetry: onRetry,
    );
  }
}

/// Generic error snackbar for quick error display
class ErrorSnackBar {
  static void show(
    BuildContext context, {
    required String message,
    String? actionLabel,
    VoidCallback? onAction,
    Duration duration = const Duration(seconds: 4),
  }) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        duration: duration,
        action: actionLabel != null && onAction != null
            ? SnackBarAction(
                label: actionLabel,
                textColor: Colors.white,
                onPressed: onAction,
              )
            : null,
      ),
    );
  }

  /// Show camera permission error
  static void showCameraPermissionError(BuildContext context, VoidCallback onOpenSettings) {
    show(
      context,
      message: 'Camera permission required for barcode scanning',
      actionLabel: 'Settings',
      onAction: onOpenSettings,
    );
  }

  /// Show network error
  static void showNetworkError(BuildContext context, VoidCallback onRetry) {
    show(
      context,
      message: 'Network error. Please check your connection.',
      actionLabel: 'Retry',
      onAction: onRetry,
    );
  }

  /// Show product not found error
  static void showProductNotFound(BuildContext context, VoidCallback onManualEntry) {
    show(
      context,
      message: 'Product not found in database',
      actionLabel: 'Enter Manually',
      onAction: onManualEntry,
      duration: const Duration(seconds: 6),
    );
  }
}

/// Error dialog for critical errors that need user attention
class ErrorDialog {
  static Future<void> show(
    BuildContext context, {
    required String title,
    required String message,
    String? actionLabel,
    VoidCallback? onAction,
  }) {
    return showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            const Icon(Icons.error, color: Colors.red),
            const SizedBox(width: 8),
            Text(title),
          ],
        ),
        content: Text(message),
        actions: [
          if (actionLabel != null && onAction != null)
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                onAction();
              },
              child: Text(actionLabel),
            ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  /// Show critical camera error dialog
  static Future<void> showCameraError(BuildContext context, VoidCallback onOpenSettings) {
    return show(
      context,
      title: 'Camera Access Required',
      message: 'SafeScan needs camera access to scan product barcodes. Please enable camera permission in your device settings.',
      actionLabel: 'Open Settings',
      onAction: onOpenSettings,
    );
  }

  /// Show critical network error dialog
  static Future<void> showNetworkError(BuildContext context) {
    return show(
      context,
      title: 'Network Connection Required',
      message: 'SafeScan requires an internet connection to analyze products. Please check your network connection and try again.',
    );
  }
}
