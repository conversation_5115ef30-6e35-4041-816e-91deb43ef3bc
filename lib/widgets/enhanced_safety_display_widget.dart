import 'package:flutter/material.dart';
import '../models/product.dart';
import '../models/ingredient.dart';
import '../constants/app_theme.dart';

/// Enhanced Safety Display Widget implementing the new Mermaid workflow safety display
/// GG["Show Product Safety Status"] → HH{"Product Status?"} → II/JJ["Display Badge"] → KK["Show Ingredients with Colors"]
class EnhancedSafetyDisplayWidget extends StatelessWidget {
  final Product product;
  final bool showDetailedAnalysis;

  const EnhancedSafetyDisplayWidget({
    Key? key,
    required this.product,
    this.showDetailedAnalysis = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // GG["Show Product Safety Status"] → HH{"Product Status?"}
        _buildSafetyStatusHeader(),
        
        const SizedBox(height: 16),
        
        // KK["Show All Ingredients with Safety Colors"]
        _buildIngredientsWithSafetyColors(),
        
        if (showDetailedAnalysis) ...[
          const SizedBox(height: 16),
          
          // LL["Highlight Concerning Ingredients"]
          _buildConcerningIngredientsSection(),
          
          const SizedBox(height: 16),
          
          // MM["Show Detailed Safety Information"]
          _buildDetailedSafetyInformation(),
          
          const SizedBox(height: 16),
          
          // NN["Display Alternative Product Suggestions"]
          _buildAlternativeProductSuggestions(),
        ],
      ],
    );
  }

  /// GG["Show Product Safety Status"] → HH{"Product Status?"} → II/JJ["Display Badge"]
  Widget _buildSafetyStatusHeader() {
    final isGood = product.safetyRating?.toLowerCase() == 'good';
    final isNotRecommended = product.safetyRating?.toLowerCase() == 'not recommended';
    
    Color badgeColor;
    Color textColor;
    IconData icon;
    String statusText;
    
    if (isGood) {
      // II["Display Green GOOD Badge"]
      badgeColor = Colors.green;
      textColor = Colors.white;
      icon = Icons.check_circle;
      statusText = 'GOOD';
    } else if (isNotRecommended) {
      // JJ["Display Red NOT RECOMMENDED Badge"]
      badgeColor = Colors.red;
      textColor = Colors.white;
      icon = Icons.warning;
      statusText = 'NOT RECOMMENDED';
    } else {
      // Default/Unknown status
      badgeColor = Colors.orange;
      textColor = Colors.white;
      icon = Icons.help;
      statusText = 'UNKNOWN';
    }

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: badgeColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: badgeColor.withOpacity(0.3),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Row(
        children: [
          Icon(icon, color: textColor, size: 32),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  statusText,
                  style: TextStyle(
                    color: textColor,
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  _getSafetyStatusDescription(statusText),
                  style: TextStyle(
                    color: textColor.withOpacity(0.9),
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
          // Safety score
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: textColor.withOpacity(0.2),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Text(
              '${product.safetyScore ?? 0}/100',
              style: TextStyle(
                color: textColor,
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// KK["Show All Ingredients with Safety Colors"]
  Widget _buildIngredientsWithSafetyColors() {
    if (product.ingredients.isEmpty) {
      return const Card(
        child: Padding(
          padding: EdgeInsets.all(16),
          child: Text('No ingredient information available'),
        ),
      );
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Ingredients Safety Analysis',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            
            // Safety color legend
            _buildSafetyColorLegend(),
            
            const SizedBox(height: 16),
            
            // Ingredients list with color coding
            ...product.ingredients.map((ingredient) => _buildIngredientItem(ingredient)),
          ],
        ),
      ),
    );
  }

  /// Safety Color Legend from the Mermaid diagram
  Widget _buildSafetyColorLegend() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(8),
      ),
      child: const Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _SafetyLegendItem(
            color: Colors.green,
            label: 'GREEN: Safe',
            icon: Icons.check_circle,
          ),
          _SafetyLegendItem(
            color: Colors.orange,
            label: 'YELLOW: Caution',
            icon: Icons.warning,
          ),
          _SafetyLegendItem(
            color: Colors.red,
            label: 'RED: Unsafe',
            icon: Icons.dangerous,
          ),
        ],
      ),
    );
  }

  Widget _buildIngredientItem(Ingredient ingredient) {
    Color safetyColor;
    IconData safetyIcon;
    
    switch (ingredient.safetyLevel.toLowerCase()) {
      case 'green':
        safetyColor = Colors.green;
        safetyIcon = Icons.check_circle;
        break;
      case 'yellow':
        safetyColor = Colors.orange;
        safetyIcon = Icons.warning;
        break;
      case 'red':
        safetyColor = Colors.red;
        safetyIcon = Icons.dangerous;
        break;
      default:
        safetyColor = Colors.grey;
        safetyIcon = Icons.help;
    }

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.left(color: safetyColor, width: 4),
        color: safetyColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Icon(safetyIcon, color: safetyColor, size: 20),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  ingredient.name,
                  style: const TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 16,
                  ),
                ),
                if (ingredient.description != null && ingredient.description!.isNotEmpty) ...[
                  const SizedBox(height: 4),
                  Text(
                    ingredient.description!,
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 14,
                    ),
                  ),
                ],
                if (ingredient.healthRisks.isNotEmpty) ...[
                  const SizedBox(height: 4),
                  Text(
                    'Risks: ${ingredient.healthRisks.join(', ')}',
                    style: TextStyle(
                      color: Colors.red[700],
                      fontSize: 12,
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                ],
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: safetyColor,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              ingredient.safetyLevel.toUpperCase(),
              style: const TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// LL["Highlight Concerning Ingredients"]
  Widget _buildConcerningIngredientsSection() {
    final concerningIngredients = product.ingredients
        .where((ingredient) => ingredient.safetyLevel.toLowerCase() == 'red')
        .toList();

    if (concerningIngredients.isEmpty) {
      return const Card(
        child: Padding(
          padding: EdgeInsets.all(16),
          child: Row(
            children: [
              Icon(Icons.check_circle, color: Colors.green, size: 24),
              SizedBox(width: 12),
              Text(
                'No concerning ingredients found',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: Colors.green,
                ),
              ),
            ],
          ),
        ),
      );
    }

    return Card(
      color: Colors.red[50],
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.warning, color: Colors.red[700], size: 24),
                const SizedBox(width: 12),
                const Text(
                  'Concerning Ingredients',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.red,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            ...concerningIngredients.map((ingredient) => Padding(
              padding: const EdgeInsets.only(bottom: 8),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Icon(Icons.dangerous, color: Colors.red[700], size: 16),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          ingredient.name,
                          style: const TextStyle(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        if (ingredient.healthRisks.isNotEmpty)
                          Text(
                            ingredient.healthRisks.join(', '),
                            style: TextStyle(
                              color: Colors.red[600],
                              fontSize: 12,
                            ),
                          ),
                      ],
                    ),
                  ),
                ],
              ),
            )),
          ],
        ),
      ),
    );
  }

  /// MM["Show Detailed Safety Information"]
  Widget _buildDetailedSafetyInformation() {
    final safetyAnalysis = product.safetyCriteriaJson;
    
    if (safetyAnalysis == null) {
      return const SizedBox.shrink();
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Detailed Safety Analysis',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            
            // Safety criteria met/failed
            if (safetyAnalysis['criteria_met'] != null) ...[
              _buildCriteriaSection('Criteria Met', safetyAnalysis['criteria_met'], Colors.green),
              const SizedBox(height: 8),
            ],
            
            if (safetyAnalysis['criteria_failed'] != null) ...[
              _buildCriteriaSection('Criteria Failed', safetyAnalysis['criteria_failed'], Colors.red),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildCriteriaSection(String title, dynamic criteria, Color color) {
    final criteriaList = criteria is List ? criteria : [criteria.toString()];
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: TextStyle(
            fontWeight: FontWeight.w600,
            color: color,
          ),
        ),
        const SizedBox(height: 4),
        ...criteriaList.map((criterion) => Padding(
          padding: const EdgeInsets.only(left: 16, bottom: 4),
          child: Row(
            children: [
              Icon(
                color == Colors.green ? Icons.check : Icons.close,
                color: color,
                size: 16,
              ),
              const SizedBox(width: 8),
              Expanded(child: Text(criterion.toString())),
            ],
          ),
        )),
      ],
    );
  }

  /// NN["Display Alternative Product Suggestions"]
  Widget _buildAlternativeProductSuggestions() {
    if (product.alternatives.isEmpty) {
      return const Card(
        child: Padding(
          padding: EdgeInsets.all(16),
          child: Text('No alternative products available'),
        ),
      );
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Alternative Product Suggestions',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            ...product.alternatives.map((alternative) => ListTile(
              leading: const Icon(Icons.recommend, color: AppTheme.primaryGreen),
              title: Text(alternative.name),
              subtitle: Text(alternative.reason ?? 'Safer alternative'),
              trailing: const Icon(Icons.arrow_forward_ios, size: 16),
              onTap: () {
                // Navigate to alternative product
              },
            )),
          ],
        ),
      ),
    );
  }

  String _getSafetyStatusDescription(String status) {
    switch (status) {
      case 'GOOD':
        return 'This product meets safety standards with no concerning ingredients';
      case 'NOT RECOMMENDED':
        return 'This product contains unsafe ingredients and should be avoided';
      case 'UNKNOWN':
        return 'Safety analysis could not be completed';
      default:
        return 'Safety status unknown';
    }
  }
}

class _SafetyLegendItem extends StatelessWidget {
  final Color color;
  final String label;
  final IconData icon;

  const _SafetyLegendItem({
    required this.color,
    required this.label,
    required this.icon,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(icon, color: color, size: 16),
        const SizedBox(width: 4),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w500,
            color: Colors.grey[700],
          ),
        ),
      ],
    );
  }
}
