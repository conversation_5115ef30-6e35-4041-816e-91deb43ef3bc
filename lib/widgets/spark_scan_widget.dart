import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:scandit_flutter_datacapture_core/scandit_flutter_datacapture_core.dart';
import 'package:scandit_flutter_datacapture_barcode/scandit_flutter_datacapture_spark_scan.dart';
import '../services/scandit_service.dart';
import '../services/logger_service.dart';
import '../providers/product_provider.dart';
import '../models/product.dart';

/// SparkScan widget for retail product barcode scanning
/// Uses Scandit's pre-built retail scanning solution
class SparkScanWidget extends ConsumerStatefulWidget {
  const SparkScanWidget({super.key});

  @override
  ConsumerState<SparkScanWidget> createState() => _SparkScanWidgetState();
}

class _SparkScanWidgetState extends ConsumerState<SparkScanWidget> 
    implements SparkScanListener {
  
  // SparkScan components
  late DataCaptureContext _dataCaptureContext;
  late SparkScan _sparkScan;
  late SparkScanView _sparkScanView;
  
  // State management
  bool _isInitialized = false;
  bool _isProcessing = false;
  String _statusMessage = 'Initializing SparkScan...';
  
  @override
  void initState() {
    super.initState();
    _initializeSparkScan();
  }

  @override
  void dispose() {
    _sparkScan.removeListener(this);
    super.dispose();
  }

  Future<void> _initializeSparkScan() async {
    try {
      setState(() {
        _statusMessage = 'Initializing SparkScan for retail scanning...';
      });

      // Initialize SparkScan SDK
      await SparkScanService.initialize();
      
      // Create data capture context
      _dataCaptureContext = SparkScanService.createDataCaptureContext();
      
      // Create SparkScan with retail settings
      final settings = SparkScanService.createSparkScanSettings();
      _sparkScan = SparkScan.withSettings(settings);
      _sparkScan.addListener(this);

      // Create SparkScan view with retail UI settings
      final viewSettings = SparkScanService.createSparkScanViewSettings();
      _sparkScanView = SparkScanView.forContext(
        Container(), // Child widget (empty container)
        _dataCaptureContext,
        _sparkScan,
        viewSettings,
      );
      
      setState(() {
        _isInitialized = true;
        _statusMessage = 'Ready to scan retail products';
      });
      
      LoggerService.info('✅ SparkScan initialized for retail scanning');
      
    } catch (e) {
      LoggerService.error('SparkScan initialization error: $e');
      setState(() {
        _statusMessage = 'Failed to initialize scanner: $e';
      });
    }
  }

  // SparkScanListener implementation
  @override
  Future<void> didScan(
    SparkScan sparkScan,
    SparkScanSession session,
    Future<FrameData> Function() getFrameData
  ) async {
    if (_isProcessing) return;

    final barcode = session.newlyRecognizedBarcode;
    if (barcode == null) return;

    final barcodeData = barcode.data;

    if (barcodeData != null && barcodeData.isNotEmpty) {
      await _handleBarcodeDetected(barcodeData);
    }
  }

  @override
  Future<void> didUpdateSession(
    SparkScan sparkScan,
    SparkScanSession session,
    Future<FrameData> Function() getFrameData
  ) async {
    // Handle session updates if needed
  }

  Future<void> _handleBarcodeDetected(String barcode) async {
    if (_isProcessing) return;
    
    setState(() {
      _isProcessing = true;
      _statusMessage = 'Processing product: $barcode';
    });

    try {
      LoggerService.info('🛒 SparkScan detected retail barcode: $barcode');

      // Trigger the product analysis workflow
      await ref.read(productControllerProvider.notifier).scanProductByBarcode(barcode);

    } catch (e) {
      LoggerService.error('Error processing barcode: $e');
      setState(() {
        _statusMessage = 'Processing failed: $e';
        _isProcessing = false;
      });
      
      // Resume scanning after error
      Timer(const Duration(seconds: 3), () {
        if (mounted) {
          setState(() {
            _statusMessage = 'Ready to scan retail products';
          });
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    // Listen to product provider state for navigation
    ref.listen<AsyncValue<Product?>>(productControllerProvider, (previous, next) {
      next.when(
        data: (product) {
          if (product != null && _isProcessing) {
            // Navigate to product details screen
            Navigator.of(context).pushReplacementNamed('/product/${product.id}');
          }
        },
        loading: () {
          setState(() {
            _statusMessage = 'Analyzing product...';
          });
        },
        error: (error, stack) {
          LoggerService.error('Product analysis error: $error');
          setState(() {
            _statusMessage = 'Analysis failed: $error';
            _isProcessing = false;
          });
          
          Timer(const Duration(seconds: 3), () {
            if (mounted) {
              setState(() {
                _statusMessage = 'Ready to scan retail products';
              });
            }
          });
        },
      );
    });

    return Scaffold(
      backgroundColor: Colors.black,
      body: Stack(
        children: [
          // SparkScan view (full screen)
          if (_isInitialized)
            Positioned.fill(child: _sparkScanView)
          else
            const Center(
              child: CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              ),
            ),

          // Status overlay
          Positioned(
            top: MediaQuery.of(context).padding.top + 20,
            left: 20,
            right: 20,
            child: Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.black.withOpacity(0.7),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Row(
                children: [
                  Icon(
                    _isProcessing 
                        ? Icons.hourglass_empty 
                        : _isInitialized 
                            ? Icons.shopping_cart 
                            : Icons.settings,
                    color: _isProcessing 
                        ? Colors.orange 
                        : _isInitialized 
                            ? Colors.green 
                            : Colors.blue,
                    size: 24,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      _statusMessage,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),

          // Back button
          Positioned(
            top: MediaQuery.of(context).padding.top + 10,
            left: 10,
            child: IconButton(
              onPressed: () => Navigator.of(context).pop(),
              icon: const Icon(
                Icons.arrow_back,
                color: Colors.white,
                size: 28,
              ),
            ),
          ),

          // Instructions overlay (only when ready)
          if (_isInitialized && !_isProcessing)
            Positioned(
              bottom: 100,
              left: 20,
              right: 20,
              child: Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.black.withOpacity(0.7),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.shopping_cart,
                      color: Colors.white,
                      size: 32,
                    ),
                    SizedBox(height: 8),
                    Text(
                      'SparkScan - Retail Product Scanner',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    SizedBox(height: 4),
                    Text(
                      'Tap the scan button or point at a barcode',
                      style: TextStyle(
                        color: Colors.white70,
                        fontSize: 14,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),
        ],
      ),
    );
  }
}
