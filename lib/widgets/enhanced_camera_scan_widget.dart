import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:camera/camera.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/new_scan_workflow_service.dart';
import '../services/gemini_service.dart';
import '../services/logger_service.dart';
import '../constants/app_theme.dart';
import '../models/product.dart';
import '../widgets/error_handling_widgets.dart';
import '../providers/scan_workflow_provider.dart';

/// Enhanced Camera Scan Widget implementing the new Mermaid workflow
/// B["Open Scan View"] → C["Initialize Camera with Gemini AI"] → D["Auto-Detect Barcode using Gemini AI"]
class EnhancedCameraScanWidget extends ConsumerStatefulWidget {
  final Function(Product) onProductScanned;
  final Function(String)? onError;

  const EnhancedCameraScanWidget({
    Key? key,
    required this.onProductScanned,
    this.onError,
  }) : super(key: key);

  @override
  ConsumerState<EnhancedCameraScanWidget> createState() => _EnhancedCameraScanWidgetState();
}

class _EnhancedCameraScanWidgetState extends ConsumerState<EnhancedCameraScanWidget> {
  CameraController? _controller;
  List<CameraDescription>? _cameras;
  bool _isInitialized = false;
  bool _isScanning = false;
  String _statusMessage = 'Initializing camera...';
  Timer? _autoScanTimer;
  DateTime? _lastScanTime;
  String? _lastScannedBarcode;
  
  // Workflow stage tracking
  String _currentStage = 'initializing';
  String _stageDetail = 'Setting up camera...';

  @override
  void initState() {
    super.initState();
    _initializeCamera();
  }

  @override
  void dispose() {
    _autoScanTimer?.cancel();
    _controller?.dispose();
    super.dispose();
  }

  /// C["Initialize Camera with Gemini AI"]
  Future<void> _initializeCamera() async {
    try {
      setState(() {
        _currentStage = 'initializing_camera';
        _stageDetail = 'Initializing camera with Gemini AI...';
        _statusMessage = 'Initializing camera with AI...';
      });

      _cameras = await availableCameras();
      
      if (_cameras == null || _cameras!.isEmpty) {
        throw Exception('No cameras available');
      }

      _controller = CameraController(
        _cameras!.first,
        ResolutionPreset.high,
        enableAudio: false,
      );

      await _controller!.initialize();

      if (mounted) {
        setState(() {
          _isInitialized = true;
          _currentStage = 'camera_ready';
          _stageDetail = 'Camera ready for barcode detection';
          _statusMessage = 'Point camera at product barcode';
        });

        // Start auto-scanning for barcodes
        _startAutoScan();
      }
    } catch (e) {
      LoggerService.error('Camera initialization failed: $e');
      setState(() {
        _currentStage = 'camera_error';
        _stageDetail = 'Camera initialization failed';
        _statusMessage = 'Camera error: ${e.toString()}';
      });
      
      // ER1["Camera Initialization Failed"]
      widget.onError?.call('Camera initialization failed: ${e.toString()}');
    }
  }

  /// D["Auto-Detect Barcode using Gemini AI"] - Start continuous scanning
  void _startAutoScan() {
    _autoScanTimer = Timer.periodic(const Duration(seconds: 2), (timer) {
      if (_isInitialized && !_isScanning) {
        _autoScanForBarcode();
      }
    });
  }

  void _stopAutoScan() {
    _autoScanTimer?.cancel();
    _autoScanTimer = null;
  }

  /// D["Auto-Detect Barcode using Gemini AI"] - Continuous barcode detection
  Future<void> _autoScanForBarcode() async {
    // Prevent too frequent scans
    if (_lastScanTime != null &&
        DateTime.now().difference(_lastScanTime!).inSeconds < 3) {
      return;
    }

    _lastScanTime = DateTime.now();

    try {
      setState(() {
        _currentStage = 'detecting_barcode';
        _stageDetail = 'Detecting barcode with AI...';
      });

      // Capture image for analysis
      final image = await _controller!.takePicture();

      // D["Auto-Detect Barcode using Gemini AI"]
      final barcode = await GeminiService.extractBarcodeFromImage(image.path);

      // Clean up the temporary image file
      try {
        await File(image.path).delete();
      } catch (e) {
        LoggerService.info('Failed to delete temporary image: $e');
      }

      // E{"Barcode Detected?"}
      if (barcode != null && barcode.isNotEmpty) {
        // E -- Yes --> F["Extract Barcode Value"]
        await _handleBarcodeDetected(barcode);
      } else {
        // E -- No --> D (continue scanning)
        setState(() {
          _currentStage = 'scanning';
          _stageDetail = 'No barcode detected, continuing scan...';
          _statusMessage = 'Scanning for barcode...';
        });
      }
    } catch (e) {
      LoggerService.error('Error during auto-scan: $e');
      
      // ER2["Barcode Detection Failed"]
      setState(() {
        _currentStage = 'detection_error';
        _stageDetail = 'Barcode detection failed';
        _statusMessage = 'Detection error, retrying...';
      });
    }
  }

  /// Handle successful barcode detection and start workflow
  Future<void> _handleBarcodeDetected(String barcode) async {
    // Prevent multiple scans of the same barcode
    if (_isScanning || barcode == _lastScannedBarcode) return;

    setState(() {
      _isScanning = true;
      _lastScannedBarcode = barcode;
      _currentStage = 'barcode_detected';
      _stageDetail = 'Barcode detected: $barcode';
      _statusMessage = 'Barcode detected! Analyzing product...';
    });

    _stopAutoScan(); // Stop scanning while processing

    try {
      LoggerService.info('Barcode detected: $barcode');
      
      // Execute the complete workflow from the Mermaid diagram
      final product = await NewScanWorkflowService.executeWorkflow(
        barcode: barcode,
        userId: null, // Will be set by the service if user is authenticated
        onStageUpdate: (stage, detail) {
          if (mounted) {
            setState(() {
              _currentStage = stage;
              _stageDetail = detail;
              _statusMessage = detail;
            });
          }
        },
      );

      if (product != null) {
        LoggerService.info('Product analysis completed successfully');
        widget.onProductScanned(product);
      } else {
        // L["Show 'Product not found' Message"]
        setState(() {
          _currentStage = 'product_not_found';
          _stageDetail = 'Product not found in database';
          _statusMessage = 'Product not found. Try another product.';
        });
        
        widget.onError?.call('Product not found in database');
        
        // Resume scanning after a delay
        Timer(const Duration(seconds: 3), () {
          if (mounted) {
            _resumeScanning();
          }
        });
      }
    } catch (e) {
      LoggerService.error('Error in product analysis workflow: $e');
      
      setState(() {
        _currentStage = 'analysis_error';
        _stageDetail = 'Product analysis failed';
        _statusMessage = 'Analysis failed. Please try again.';
      });
      
      widget.onError?.call('Product analysis failed: ${e.toString()}');
      
      // Resume scanning after error
      Timer(const Duration(seconds: 3), () {
        if (mounted) {
          _resumeScanning();
        }
      });
    }
  }

  /// Resume scanning after error or completion
  void _resumeScanning() {
    setState(() {
      _isScanning = false;
      _lastScannedBarcode = null;
      _currentStage = 'scanning';
      _stageDetail = 'Ready to scan next product';
      _statusMessage = 'Point camera at product barcode';
    });
    
    _startAutoScan();
  }

  /// Manual capture button for when auto-detection fails
  Future<void> _manualCapture() async {
    if (_controller == null || !_controller!.value.isInitialized || _isScanning) {
      return;
    }

    setState(() {
      _isScanning = true;
      _currentStage = 'manual_capture';
      _stageDetail = 'Capturing image manually...';
      _statusMessage = 'Capturing image...';
    });

    try {
      final image = await _controller!.takePicture();
      
      setState(() {
        _stageDetail = 'Analyzing captured image...';
        _statusMessage = 'Analyzing image with AI...';
      });

      final barcode = await GeminiService.extractBarcodeFromImage(image.path);

      // Clean up
      try {
        await File(image.path).delete();
      } catch (e) {
        LoggerService.info('Failed to delete temporary image: $e');
      }

      if (barcode != null && barcode.isNotEmpty) {
        await _handleBarcodeDetected(barcode);
      } else {
        setState(() {
          _currentStage = 'no_barcode_found';
          _stageDetail = 'No barcode found in image';
          _statusMessage = 'No barcode detected. Try again.';
        });
        
        Timer(const Duration(seconds: 2), () {
          if (mounted) {
            _resumeScanning();
          }
        });
      }
    } catch (e) {
      LoggerService.error('Error in manual capture: $e');
      setState(() {
        _currentStage = 'capture_error';
        _stageDetail = 'Manual capture failed';
        _statusMessage = 'Capture failed. Please try again.';
      });
      
      Timer(const Duration(seconds: 2), () {
        if (mounted) {
          _resumeScanning();
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (!_isInitialized) {
      return _buildInitializingView();
    }

    return _buildCameraView();
  }

  Widget _buildInitializingView() {
    return Container(
      color: Colors.black,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const CircularProgressIndicator(color: AppTheme.primaryGreen),
            const SizedBox(height: 16),
            Text(
              _statusMessage,
              style: const TextStyle(color: Colors.white, fontSize: 16),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              _stageDetail,
              style: const TextStyle(color: Colors.white70, fontSize: 14),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCameraView() {
    return Stack(
      children: [
        // Camera preview
        SizedBox.expand(
          child: CameraPreview(_controller!),
        ),
        
        // Scanning overlay
        _buildScanningOverlay(),
        
        // Status information
        _buildStatusOverlay(),
        
        // Manual capture button
        _buildManualCaptureButton(),
      ],
    );
  }

  Widget _buildScanningOverlay() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.black.withOpacity(0.3),
      ),
      child: CustomPaint(
        painter: ScanningOverlayPainter(),
        child: Container(),
      ),
    );
  }

  Widget _buildStatusOverlay() {
    return Positioned(
      top: 50,
      left: 16,
      right: 16,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.black.withOpacity(0.7),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          children: [
            Row(
              children: [
                _buildStageIndicator(),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        _statusMessage,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        _stageDetail,
                        style: const TextStyle(
                          color: Colors.white70,
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            if (_isScanning) ...[
              const SizedBox(height: 12),
              const LinearProgressIndicator(
                color: AppTheme.primaryGreen,
                backgroundColor: Colors.white24,
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildStageIndicator() {
    IconData icon;
    Color color;

    switch (_currentStage) {
      case 'initializing_camera':
        icon = Icons.camera_alt;
        color = Colors.orange;
        break;
      case 'camera_ready':
      case 'scanning':
        icon = Icons.qr_code_scanner;
        color = AppTheme.primaryGreen;
        break;
      case 'detecting_barcode':
        icon = Icons.search;
        color = Colors.blue;
        break;
      case 'barcode_detected':
        icon = Icons.check_circle;
        color = AppTheme.primaryGreen;
        break;
      case 'checking_database':
        icon = Icons.storage;
        color = Colors.purple;
        break;
      case 'querying_api':
        icon = Icons.cloud_download;
        color = Colors.blue;
        break;
      case 'analyzing_safety':
        icon = Icons.analytics;
        color = Colors.orange;
        break;
      case 'complete':
        icon = Icons.done_all;
        color = AppTheme.primaryGreen;
        break;
      case 'camera_error':
      case 'detection_error':
      case 'analysis_error':
        icon = Icons.error;
        color = Colors.red;
        break;
      case 'product_not_found':
        icon = Icons.search_off;
        color = Colors.orange;
        break;
      default:
        icon = Icons.info;
        color = Colors.grey;
    }

    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: color.withOpacity(0.2),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color, width: 1),
      ),
      child: Icon(icon, color: color, size: 20),
    );
  }

  Widget _buildManualCaptureButton() {
    return Positioned(
      bottom: 50,
      left: 0,
      right: 0,
      child: Center(
        child: GestureDetector(
          onTap: _isScanning ? null : _manualCapture,
          child: Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              color: _isScanning ? Colors.grey : AppTheme.primaryGreen,
              shape: BoxShape.circle,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.3),
                  blurRadius: 10,
                  offset: const Offset(0, 5),
                ),
              ],
            ),
            child: Icon(
              _isScanning ? Icons.hourglass_empty : Icons.camera_alt,
              color: Colors.white,
              size: 32,
            ),
          ),
        ),
      ),
    );
  }
}

/// Custom painter for scanning overlay
class ScanningOverlayPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = AppTheme.primaryGreen
      ..strokeWidth = 3
      ..style = PaintingStyle.stroke;

    final rect = Rect.fromCenter(
      center: Offset(size.width / 2, size.height / 2),
      width: size.width * 0.7,
      height: size.height * 0.4,
    );

    // Draw corner brackets
    final cornerLength = 30.0;

    // Top-left corner
    canvas.drawLine(
      Offset(rect.left, rect.top + cornerLength),
      Offset(rect.left, rect.top),
      paint,
    );
    canvas.drawLine(
      Offset(rect.left, rect.top),
      Offset(rect.left + cornerLength, rect.top),
      paint,
    );

    // Top-right corner
    canvas.drawLine(
      Offset(rect.right - cornerLength, rect.top),
      Offset(rect.right, rect.top),
      paint,
    );
    canvas.drawLine(
      Offset(rect.right, rect.top),
      Offset(rect.right, rect.top + cornerLength),
      paint,
    );

    // Bottom-left corner
    canvas.drawLine(
      Offset(rect.left, rect.bottom - cornerLength),
      Offset(rect.left, rect.bottom),
      paint,
    );
    canvas.drawLine(
      Offset(rect.left, rect.bottom),
      Offset(rect.left + cornerLength, rect.bottom),
      paint,
    );

    // Bottom-right corner
    canvas.drawLine(
      Offset(rect.right - cornerLength, rect.bottom),
      Offset(rect.right, rect.bottom),
      paint,
    );
    canvas.drawLine(
      Offset(rect.right, rect.bottom),
      Offset(rect.right, rect.bottom - cornerLength),
      paint,
    );
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}
