import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:scandit_flutter_datacapture_core/scandit_flutter_datacapture_core.dart';
import 'package:scandit_flutter_datacapture_barcode/scandit_flutter_datacapture_barcode.dart';
import 'package:scandit_flutter_datacapture_barcode/scandit_flutter_datacapture_barcode_capture.dart';
import '../services/scandit_service.dart';
import '../services/logger_service.dart';
import '../providers/product_provider.dart';
import '../models/product.dart';

/// Scandit-powered camera scanning widget for accurate barcode detection
/// Based on official Scandit Flutter sample implementation
class ScanditCameraScanWidget extends ConsumerStatefulWidget {
  const ScanditCameraScanWidget({super.key});

  @override
  ConsumerState<ScanditCameraScanWidget> createState() => _ScanditCameraScanWidgetState();
}

class _ScanditCameraScanWidgetState extends ConsumerState<ScanditCameraScanWidget>
    with WidgetsBindingObserver
    implements BarcodeCaptureListener {

  // Scandit components
  late DataCaptureContext _dataCaptureContext;
  Camera? _camera = Camera.defaultCamera;
  late BarcodeCapture _barcodeCapture;
  late DataCaptureView _captureView;

  // State management
  bool _isPermissionMessageVisible = false;
  bool _isProcessing = false;
  String _statusMessage = 'Initializing scanner...';
  String _currentStage = 'initializing';

  DateTime? _lastScanTime;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _initializeScanner();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _barcodeCapture.removeListener(this);
    _barcodeCapture.isEnabled = false;
    _camera?.switchToDesiredState(FrameSourceState.off);
    _dataCaptureContext.removeAllModes();
    super.dispose();
  }

  Future<void> _initializeScanner() async {
    try {
      setState(() {
        _statusMessage = 'Initializing Scandit SDK...';
        _currentStage = 'initializing';
      });

      // Initialize Scandit SDK
      await ScanditService.initialize();

      // Create data capture context
      _dataCaptureContext = ScanditService.createDataCaptureContext();

      // Configure camera
      if (_camera != null) {
        _camera!.applySettings(ScanditService.getRecommendedCameraSettings());
        _dataCaptureContext.setFrameSource(_camera!);
      }

      // Create barcode capture with settings
      final settings = ScanditService.createBarcodeCaptureSettings();
      _barcodeCapture = BarcodeCapture.forContext(_dataCaptureContext, settings);
      _barcodeCapture.addListener(this);

      // Create data capture view
      _captureView = DataCaptureView.forContext(_dataCaptureContext);

      // Add overlay for visual feedback
      final overlay = BarcodeCaptureOverlay.withBarcodeCaptureForViewWithStyle(
        _barcodeCapture,
        _captureView,
        BarcodeCaptureOverlayStyle.frame,
      );

      // Configure viewfinder
      overlay.viewfinder = RectangularViewfinder.withStyleAndLineStyle(
        RectangularViewfinderStyle.square,
        RectangularViewfinderLineStyle.light,
      );

      // Improve visual feedback
      overlay.brush = Brush(
        const Color.fromARGB(0, 0, 0, 0),
        const Color.fromARGB(255, 255, 255, 255),
        3,
      );

      _captureView.addOverlay(overlay);

      // Check permissions and start scanning
      _checkPermission();

      setState(() {
        _currentStage = 'ready';
        _statusMessage = 'Point camera at product barcode';
      });

      LoggerService.info('✅ Scandit scanner initialized successfully');

    } catch (e) {
      LoggerService.error('Scanner initialization error: $e');
      setState(() {
        _currentStage = 'error';
        _statusMessage = 'Scanner initialization failed';
      });
    }
  }

  void _checkPermission() {
    Permission.camera.request().isGranted.then((value) => setState(() {
      _isPermissionMessageVisible = !value;
      if (value) {
        _camera?.switchToDesiredState(FrameSourceState.on);
        _barcodeCapture.isEnabled = true;
      }
    }));
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {
      _checkPermission();
    } else if (state == AppLifecycleState.paused) {
      _camera?.switchToDesiredState(FrameSourceState.off);
    }
  }

  Future<void> _handleBarcodeDetected(String barcode) async {
    // Prevent too frequent scans
    if (_lastScanTime != null &&
        DateTime.now().difference(_lastScanTime!).inSeconds < 3) {
      return;
    }

    _lastScanTime = DateTime.now();

    try {
      setState(() {
        _currentStage = 'processing';
        _statusMessage = 'Processing barcode...';
        _isProcessing = true;
      });

      LoggerService.info('🎯 Scandit detected barcode: $barcode');

      // Trigger the product analysis workflow
      await ref.read(productControllerProvider.notifier).scanProductByBarcode(barcode);

    } catch (e) {
      LoggerService.error('Error processing barcode: $e');
      setState(() {
        _currentStage = 'error';
        _statusMessage = 'Processing failed';
        _isProcessing = false;
      });

      // Resume scanning after error
      Timer(const Duration(seconds: 3), () {
        _resumeScanning();
      });
    }
  }

  void _resumeScanning() {
    if (mounted) {
      setState(() {
        _currentStage = 'ready';
        _statusMessage = 'Point camera at product barcode';
        _isProcessing = false;
      });

      _barcodeCapture.isEnabled = true;
    }
  }

  // Scandit BarcodeCaptureListener implementation
  @override
  Future<void> didScan(
    BarcodeCapture barcodeCapture,
    BarcodeCaptureSession session,
    Future<FrameData> getFrameData(),
  ) async {
    if (_isProcessing) return;

    _barcodeCapture.isEnabled = false;
    final code = session.newlyRecognizedBarcode;

    if (code == null) return;

    final data = (code.data == null || code.data?.isEmpty == true)
        ? code.rawData
        : code.data;

    if (data != null && data.isNotEmpty) {
      final humanReadableSymbology = SymbologyDescription.forSymbology(code.symbology);
      LoggerService.info('📊 Scandit detected: $data (${humanReadableSymbology.readableName})');

      await _handleBarcodeDetected(data);
    } else {
      _barcodeCapture.isEnabled = true;
    }
  }

  @override
  Future<void> didUpdateSession(
    BarcodeCapture barcodeCapture,
    BarcodeCaptureSession session,
    Future<FrameData> getFrameData(),
  ) async {
    // Handle session updates if needed
  }

  @override
  Widget build(BuildContext context) {
    // Listen to product provider state for navigation
    ref.listen<AsyncValue<Product?>>(productControllerProvider, (previous, next) {
      next.when(
        data: (product) {
          if (product != null && _isProcessing) {
            // Navigate to product details screen
            Navigator.of(context).pushReplacementNamed('/product/${product.id}');
          }
        },
        loading: () {
          setState(() {
            _statusMessage = 'Analyzing product...';
          });
        },
        error: (error, stack) {
          LoggerService.error('Product scan error: $error');
          setState(() {
            _currentStage = 'error';
            _statusMessage = 'Analysis failed';
            _isProcessing = false;
          });

          Timer(const Duration(seconds: 3), () {
            _resumeScanning();
          });
        },
      );
    });

    return Scaffold(
      backgroundColor: Colors.black,
      body: Stack(
        children: [
          // Camera view
          if (_isPermissionMessageVisible)
            const Center(
              child: Text(
                'No permission to access the camera!',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            )
          else if (_currentStage == 'initializing')
            const Center(
              child: CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              ),
            )
          else
            _captureView,

          // Status overlay
          Positioned(
            top: MediaQuery.of(context).padding.top + 20,
            left: 20,
            right: 20,
            child: Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.black.withOpacity(0.7),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Row(
                children: [
                  _buildStageIcon(),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      _statusMessage,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),

          // Back button
          Positioned(
            top: MediaQuery.of(context).padding.top + 10,
            left: 10,
            child: IconButton(
              onPressed: () => Navigator.of(context).pop(),
              icon: const Icon(
                Icons.arrow_back,
                color: Colors.white,
                size: 28,
              ),
            ),
          ),

          // Instructions overlay
          if (_currentStage == 'ready')
            Positioned(
              bottom: 100,
              left: 20,
              right: 20,
              child: Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.black.withOpacity(0.7),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.qr_code_scanner,
                      color: Colors.white,
                      size: 32,
                    ),
                    SizedBox(height: 8),
                    Text(
                      'Position the barcode within the viewfinder',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    SizedBox(height: 4),
                    Text(
                      'Professional Scandit scanner for accurate detection',
                      style: TextStyle(
                        color: Colors.white70,
                        fontSize: 14,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildStageIcon() {
    switch (_currentStage) {
      case 'initializing':
        return const SizedBox(
          width: 20,
          height: 20,
          child: CircularProgressIndicator(
            strokeWidth: 2,
            valueColor: AlwaysStoppedAnimation<Color>(Colors.blue),
          ),
        );
      case 'ready':
        return const Icon(Icons.qr_code_scanner, color: Colors.green, size: 24);
      case 'processing':
        return const SizedBox(
          width: 20,
          height: 20,
          child: CircularProgressIndicator(
            strokeWidth: 2,
            valueColor: AlwaysStoppedAnimation<Color>(Colors.orange),
          ),
        );
      case 'error':
        return const Icon(Icons.error, color: Colors.red, size: 24);
      default:
        return const Icon(Icons.info, color: Colors.blue, size: 24);
    }
  }
}
