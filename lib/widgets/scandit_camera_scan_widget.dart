import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/scandit_service.dart';
import '../services/logger_service.dart';
import '../providers/product_provider.dart';

/// Scandit-powered camera scanning widget for accurate barcode detection
class ScanditCameraScanWidget extends ConsumerStatefulWidget {
  const ScanditCameraScanWidget({super.key});

  @override
  ConsumerState<ScanditCameraScanWidget> createState() => _ScanditCameraScanWidgetState();
}

class _ScanditCameraScanWidgetState extends ConsumerState<ScanditCameraScanWidget> {
  bool _isInitializing = true;
  bool _isScanning = false;
  String _statusMessage = 'Initializing scanner...';
  String _currentStage = 'initializing';
  String _stageDetail = 'Setting up Scandit SDK...';
  
  Timer? _statusTimer;
  DateTime? _lastScanTime;

  @override
  void initState() {
    super.initState();
    _initializeScanner();
  }

  @override
  void dispose() {
    _statusTimer?.cancel();
    ScanditService.stopScanning();
    super.dispose();
  }

  Future<void> _initializeScanner() async {
    try {
      setState(() {
        _statusMessage = 'Initializing Scandit SDK...';
        _stageDetail = 'Setting up professional barcode scanner...';
      });

      final success = await ScanditService.initialize();
      
      if (success) {
        setState(() {
          _isInitializing = false;
          _currentStage = 'ready';
          _statusMessage = 'Point camera at product barcode';
          _stageDetail = 'Ready to scan retail products';
        });
        
        await ScanditService.startScanning();
        
        setState(() {
          _isScanning = true;
        });
        
        LoggerService.info('✅ Scandit scanner ready');
      } else {
        setState(() {
          _isInitializing = false;
          _currentStage = 'error';
          _statusMessage = 'Failed to initialize scanner';
          _stageDetail = 'Please check camera permissions';
        });
      }
    } catch (e) {
      LoggerService.error('Scanner initialization error: $e');
      setState(() {
        _isInitializing = false;
        _currentStage = 'error';
        _statusMessage = 'Scanner initialization failed';
        _stageDetail = 'Error: $e';
      });
    }
  }

  Future<void> _handleBarcodeDetected(String barcode) async {
    // Prevent too frequent scans
    if (_lastScanTime != null &&
        DateTime.now().difference(_lastScanTime!).inSeconds < 3) {
      return;
    }

    _lastScanTime = DateTime.now();

    try {
      setState(() {
        _currentStage = 'processing';
        _statusMessage = 'Processing barcode...';
        _stageDetail = 'Analyzing product: $barcode';
      });

      LoggerService.info('🎯 Scandit detected barcode: $barcode');

      // Stop scanning temporarily
      await ScanditService.stopScanning();

      // Trigger the product analysis workflow
      await ref.read(productProvider.notifier).scanProductByBarcode(barcode);

      // Navigate to product detail or handle result
      final productState = ref.read(productProvider);
      
      productState.when(
        data: (product) {
          if (product != null) {
            LoggerService.info('✅ Product analysis complete: ${product.name}');
            // Navigation will be handled by the parent widget/provider
          }
        },
        loading: () {
          setState(() {
            _statusMessage = 'Analyzing product...';
            _stageDetail = 'Please wait while we analyze the product';
          });
        },
        error: (error, stack) {
          setState(() {
            _currentStage = 'error';
            _statusMessage = 'Analysis failed';
            _stageDetail = error.toString();
          });
          
          // Resume scanning after error
          Timer(const Duration(seconds: 3), () {
            _resumeScanning();
          });
        },
      );

    } catch (e) {
      LoggerService.error('Error processing barcode: $e');
      setState(() {
        _currentStage = 'error';
        _statusMessage = 'Processing failed';
        _stageDetail = 'Error: $e';
      });
      
      Timer(const Duration(seconds: 3), () {
        _resumeScanning();
      });
    }
  }

  void _resumeScanning() {
    if (mounted) {
      setState(() {
        _currentStage = 'ready';
        _statusMessage = 'Point camera at product barcode';
        _stageDetail = 'Ready to scan retail products';
      });
      
      ScanditService.startScanning();
    }
  }

  void _handleScannerError() {
    setState(() {
      _currentStage = 'error';
      _statusMessage = 'Scanner error';
      _stageDetail = 'Failed to create scanner widget';
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: Stack(
        children: [
          // Camera view
          if (_isInitializing)
            const Center(
              child: CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              ),
            )
          else if (ScanditService.isInitialized)
            ScanditService.createScanningWidget(
              onBarcodeScanned: _handleBarcodeDetected,
              onError: _handleScannerError,
            )
          else
            const Center(
              child: Text(
                'Scanner not available',
                style: TextStyle(color: Colors.white),
              ),
            ),

          // Status overlay
          Positioned(
            top: MediaQuery.of(context).padding.top + 20,
            left: 20,
            right: 20,
            child: Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.black.withOpacity(0.7),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Row(
                    children: [
                      _buildStageIcon(),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              _statusMessage,
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              _stageDetail,
                              style: const TextStyle(
                                color: Colors.white70,
                                fontSize: 14,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),

          // Back button
          Positioned(
            top: MediaQuery.of(context).padding.top + 10,
            left: 10,
            child: IconButton(
              onPressed: () => Navigator.of(context).pop(),
              icon: const Icon(
                Icons.arrow_back,
                color: Colors.white,
                size: 28,
              ),
            ),
          ),

          // Instructions overlay
          if (_currentStage == 'ready')
            Positioned(
              bottom: 100,
              left: 20,
              right: 20,
              child: Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.black.withOpacity(0.7),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.qr_code_scanner,
                      color: Colors.white,
                      size: 32,
                    ),
                    SizedBox(height: 8),
                    Text(
                      'Position the barcode within the viewfinder',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    SizedBox(height: 4),
                    Text(
                      'Scandit will automatically detect and scan the barcode',
                      style: TextStyle(
                        color: Colors.white70,
                        fontSize: 14,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildStageIcon() {
    switch (_currentStage) {
      case 'initializing':
        return const SizedBox(
          width: 20,
          height: 20,
          child: CircularProgressIndicator(
            strokeWidth: 2,
            valueColor: AlwaysStoppedAnimation<Color>(Colors.blue),
          ),
        );
      case 'ready':
        return const Icon(Icons.qr_code_scanner, color: Colors.green, size: 24);
      case 'processing':
        return const SizedBox(
          width: 20,
          height: 20,
          child: CircularProgressIndicator(
            strokeWidth: 2,
            valueColor: AlwaysStoppedAnimation<Color>(Colors.orange),
          ),
        );
      case 'error':
        return const Icon(Icons.error, color: Colors.red, size: 24);
      default:
        return const Icon(Icons.info, color: Colors.blue, size: 24);
    }
  }
}
