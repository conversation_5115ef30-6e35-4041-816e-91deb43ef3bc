import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:go_router/go_router.dart';
import 'package:scandit_flutter_datacapture_core/scandit_flutter_datacapture_core.dart';
import 'package:scandit_flutter_datacapture_barcode/scandit_flutter_datacapture_barcode.dart';
import 'package:scandit_flutter_datacapture_barcode/scandit_flutter_datacapture_barcode_capture.dart';
import '../services/scandit_service.dart';
import '../services/logger_service.dart';
import '../providers/product_provider.dart';
import '../models/product.dart';

/// Simple Scandit BarcodeCapture scanner for retail products
/// Uses Scandit's core scanning engine with clean, simple UI
class SimpleBarcodeScanner extends ConsumerStatefulWidget {
  const SimpleBarcodeScanner({super.key});

  @override
  ConsumerState<SimpleBarcodeScanner> createState() => _SimpleBarcodeScannerState();
}

class _SimpleBarcodeScannerState extends ConsumerState<SimpleBarcodeScanner> 
    with WidgetsBindingObserver 
    implements BarcodeCaptureListener {
  
  // Scandit components
  late DataCaptureContext _dataCaptureContext;
  Camera? _camera;
  late BarcodeCapture _barcodeCapture;
  late DataCaptureView _captureView;
  
  // State management
  bool _isInitialized = false;
  bool _isProcessing = false;
  bool _isPermissionMessageVisible = false;
  String _statusMessage = 'Initializing scanner...';
  
  DateTime? _lastScanTime;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _initializeScanner();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _barcodeCapture.removeListener(this);
    _barcodeCapture.isEnabled = false;
    _camera?.switchToDesiredState(FrameSourceState.off);
    _dataCaptureContext.removeAllModes();
    super.dispose();
  }

  Future<void> _initializeScanner() async {
    try {
      setState(() {
        _statusMessage = 'Initializing Scandit scanner...';
      });

      // Initialize Scandit SDK first
      await ScanditService.initialize();

      // Create data capture context
      _dataCaptureContext = ScanditService.createDataCaptureContext();

      // Get camera after SDK initialization
      _camera = Camera.defaultCamera;

      // Configure camera
      if (_camera != null) {
        _camera!.applySettings(BarcodeCapture.recommendedCameraSettings);
        _dataCaptureContext.setFrameSource(_camera!);
      }
      
      // Create barcode capture with retail settings
      final settings = BarcodeCaptureSettings();
      
      // Enable retail barcode symbologies
      settings.enableSymbologies({
        Symbology.ean8,           // EAN-8
        Symbology.ean13Upca,      // EAN-13 & UPC-A (most common for retail)
        Symbology.upce,           // UPC-E
        Symbology.qr,             // QR codes
        Symbology.code128,        // Code 128
        Symbology.code39,         // Code 39
      });
      
      _barcodeCapture = BarcodeCapture.forContext(_dataCaptureContext, settings);
      _barcodeCapture.addListener(this);
      
      // Create data capture view
      _captureView = DataCaptureView.forContext(_dataCaptureContext);
      
      // Add overlay for visual feedback
      final overlay = BarcodeCaptureOverlay.withBarcodeCaptureForViewWithStyle(
        _barcodeCapture,
        _captureView,
        BarcodeCaptureOverlayStyle.frame,
      );
      
      // Configure viewfinder
      overlay.viewfinder = RectangularViewfinder.withStyleAndLineStyle(
        RectangularViewfinderStyle.square,
        RectangularViewfinderLineStyle.light,
      );
      
      _captureView.addOverlay(overlay);
      
      // Check permissions and start scanning
      _checkPermission();
      
      setState(() {
        _isInitialized = true;
        _statusMessage = 'Point camera at barcode';
      });
      
      LoggerService.info('✅ Scandit BarcodeCapture initialized');
      
    } catch (e) {
      LoggerService.error('Scanner initialization error: $e');
      setState(() {
        _statusMessage = 'Failed to initialize scanner: $e';
      });
    }
  }

  void _checkPermission() {
    Permission.camera.request().isGranted.then((value) => setState(() {
      _isPermissionMessageVisible = !value;
      if (value) {
        _camera?.switchToDesiredState(FrameSourceState.on);
        _barcodeCapture.isEnabled = true;
      }
    }));
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {
      _checkPermission();
    } else if (state == AppLifecycleState.paused) {
      _camera?.switchToDesiredState(FrameSourceState.off);
    }
  }

  // BarcodeCaptureListener implementation
  @override
  Future<void> didScan(
    BarcodeCapture barcodeCapture, 
    BarcodeCaptureSession session, 
    Future<FrameData> Function() getFrameData
  ) async {
    if (_isProcessing) return;
    
    // Prevent too frequent scans
    if (_lastScanTime != null &&
        DateTime.now().difference(_lastScanTime!).inSeconds < 2) {
      return;
    }

    _lastScanTime = DateTime.now();
    
    final barcode = session.newlyRecognizedBarcode;
    if (barcode == null) return;

    final barcodeData = barcode.data;
    
    if (barcodeData != null && barcodeData.isNotEmpty) {
      await _handleBarcodeDetected(barcodeData);
    }
  }

  @override
  Future<void> didUpdateSession(
    BarcodeCapture barcodeCapture, 
    BarcodeCaptureSession session, 
    Future<FrameData> Function() getFrameData
  ) async {
    // Handle session updates if needed
  }

  Future<void> _handleBarcodeDetected(String barcode) async {
    if (_isProcessing) return;
    
    setState(() {
      _isProcessing = true;
      _statusMessage = 'Processing barcode: $barcode';
    });

    try {
      LoggerService.info('🛒 Scandit detected retail barcode: $barcode');

      // Disable scanning temporarily
      _barcodeCapture.isEnabled = false;

      // Trigger the product analysis workflow
      await ref.read(productControllerProvider.notifier).scanProductByBarcode(barcode);

    } catch (e) {
      LoggerService.error('Error processing barcode: $e');
      setState(() {
        _statusMessage = 'Processing failed: $e';
        _isProcessing = false;
      });
      
      // Resume scanning after error
      Timer(const Duration(seconds: 3), () {
        if (mounted) {
          setState(() {
            _statusMessage = 'Point camera at barcode';
          });
          _barcodeCapture.isEnabled = true;
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    // Listen to product provider state for navigation
    ref.listen<AsyncValue<Product?>>(productControllerProvider, (previous, next) {
      next.when(
        data: (product) {
          if (product != null && _isProcessing) {
            // Navigate to product details screen using GoRouter
            LoggerService.info('✅ Product analysis complete, navigating to product details: ${product.name}');
            context.go('/product/${product.id}');
          }
        },
        loading: () {
          setState(() {
            _statusMessage = 'Analyzing product...';
          });
        },
        error: (error, stack) {
          LoggerService.error('Product analysis error: $error');
          setState(() {
            _statusMessage = 'Analysis failed: $error';
            _isProcessing = false;
          });
          
          Timer(const Duration(seconds: 3), () {
            if (mounted) {
              setState(() {
                _statusMessage = 'Point camera at barcode';
              });
              _barcodeCapture.isEnabled = true;
            }
          });
        },
      );
    });

    return Scaffold(
      backgroundColor: Colors.black,
      body: Stack(
        children: [
          // Camera view
          if (_isPermissionMessageVisible)
            const Center(
              child: Text(
                'Camera permission required to scan barcodes',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
                textAlign: TextAlign.center,
              ),
            )
          else if (!_isInitialized)
            const Center(
              child: CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              ),
            )
          else
            _captureView,

          // Status overlay
          Positioned(
            top: MediaQuery.of(context).padding.top + 20,
            left: 20,
            right: 20,
            child: Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.black.withValues(alpha: 0.7),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Row(
                children: [
                  Icon(
                    _isProcessing 
                        ? Icons.hourglass_empty 
                        : _isInitialized 
                            ? Icons.qr_code_scanner 
                            : Icons.settings,
                    color: _isProcessing 
                        ? Colors.orange 
                        : _isInitialized 
                            ? Colors.green 
                            : Colors.blue,
                    size: 24,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      _statusMessage,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),

          // Back button
          Positioned(
            top: MediaQuery.of(context).padding.top + 10,
            left: 10,
            child: IconButton(
              onPressed: () => Navigator.of(context).pop(),
              icon: const Icon(
                Icons.arrow_back,
                color: Colors.white,
                size: 28,
              ),
            ),
          ),

          // Instructions overlay (only when ready)
          if (_isInitialized && !_isProcessing && !_isPermissionMessageVisible)
            Positioned(
              bottom: 100,
              left: 20,
              right: 20,
              child: Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.black.withValues(alpha: 0.7),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.qr_code_scanner,
                      color: Colors.white,
                      size: 32,
                    ),
                    SizedBox(height: 8),
                    Text(
                      'Scandit Barcode Scanner',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    SizedBox(height: 4),
                    Text(
                      'Point camera at any retail product barcode',
                      style: TextStyle(
                        color: Colors.white70,
                        fontSize: 14,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),
        ],
      ),
    );
  }
}
