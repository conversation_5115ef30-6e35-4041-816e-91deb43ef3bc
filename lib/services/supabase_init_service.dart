import 'package:flutter/foundation.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
// Old service removed - using new workflow service
import 'logger_service.dart';

class SupabaseInitService {
  static bool _initialized = false;
  
  // Add getter for initialization status
  static bool get isInitialized => _initialized;
  
  // Core required tables
  static final List<String> _requiredTables = [
    'product_analysis_fullscan', // Main products table
    'ingredients',
    'product_ingredients',
    'saved_products',
    'user_preferences',
    'scan_results',
  ];
  
  // MCP-specific tables that enhance functionality
  static final List<String> _mcpTables = [
    'scan_logs',
    'unknown_barcodes',
    'pending_ingredients',
    'allergens',
    'brands',
  ];
  
  /// Initialize Supabase with environment variables
  static Future<bool> initialize() async {
    if (_initialized) {
      LoggerService.info('Supabase already initialized');
      return true;
    }
    
    try {
      // Load environment variables with fallback
      try {
        await dotenv.load(fileName: ".env");
        LoggerService.info("Environment variables loaded successfully");
      } catch (e) {
        LoggerService.info("Using default Supabase configuration values");
      }
      
      final supabaseUrl = dotenv.env['SUPABASE_URL'] ?? 'https://lwnfzmvqahraputvfkos.supabase.co';
      final supabaseKey = dotenv.env['SUPABASE_ANON_KEY'] ?? 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx3bmZ6bXZxYWhyYXB1dHZma29zIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYwMzUzNTgsImV4cCI6MjA2MTYxMTM1OH0.MkBqqCFPxk-ZpBGZv9miSnZeZuKmFin8MgC54lKP-Ao';
      
      LoggerService.info('Initializing Supabase with URL: $supabaseUrl');
      
      // Initialize with proper configuration
      await Supabase.initialize(
        url: supabaseUrl,
        anonKey: supabaseKey,
        debug: kDebugMode,
        authOptions: const FlutterAuthClientOptions(
          authFlowType: AuthFlowType.pkce,
        ),
      );
      
      _initialized = true;
      LoggerService.info('Supabase initialized successfully');
      
      // Verify core tables exist
      final connectionStatus = await verifyConnection();
      if (!connectionStatus) {
        LoggerService.warning('Core tables verification failed');
        return false;
      }
      
      // Check MCP tables (non-blocking)
      final mcpStatus = await checkMcpTables();
      if (!mcpStatus) {
        LoggerService.warning('MCP tables missing - enhanced features disabled');
      }
      
      return true;
    } catch (e) {
      LoggerService.error('Error initializing Supabase: $e');
      _initialized = false;
      return false;
    }
  }
  
  /// Verify connection to Supabase and check required tables
  static Future<bool> verifyConnection() async {
    if (!_initialized) {
      LoggerService.warning('Supabase not initialized. Call initialize() first.');
      return false;
    }
    
    try {
      final supabase = Supabase.instance.client;
      
      // Test with actual table name
      await supabase.from('product_analysis_fullscan').select('id').limit(1);
      
      LoggerService.info('Supabase connection verified successfully');
      return true;
    } catch (e) {
      LoggerService.error('Supabase connection verification failed: $e');
      return false;
    }
  }
  
  /// Check if MCP-specific tables exist
  static Future<bool> checkMcpTables() async {
    if (!_initialized) {
      LoggerService.warning('Supabase not initialized. Call initialize() first.');
      return false;
    }
    
    try {
      final supabase = Supabase.instance.client;
      final existingTables = <String>[];
      final missingTables = <String>[];
      
      // Map of fallback tables to check if primary ones don't exist
      final fallbackTables = {
        'ai_analysis_cache': ['scan_results'],
        'scan_events': ['scan_logs', 'scan_results'],
        'product_analysis_fallback': ['products'],
        'user_feedback': ['assisted_ingredients', 'profiles', 'saved_products'],
        'pending_ingredients': ['pending_ingredients'],
      };
      
      for (final tableName in _mcpTables) {
        bool tableExists = false;
        
        // First try the actual table/view
        try {
          await supabase
              .from(tableName)
              .select()
              .limit(0);
          existingTables.add(tableName);
          tableExists = true;
        } catch (e) {
          // Check if this is a "does not exist" error
          if (e.toString().contains('does not exist')) {
            // Try fallback tables
            if (fallbackTables.containsKey(tableName)) {
              for (final fallbackTable in fallbackTables[tableName]!) {
                try {
                  await supabase
                      .from(fallbackTable)
                      .select()
                      .limit(0);
                  LoggerService.info('Found fallback table $fallbackTable for $tableName');
                  existingTables.add('$tableName (via $fallbackTable)');
                  tableExists = true;
                  break;
                } catch (fallbackError) {
                  // Continue to next fallback
                }
              }
            }
          }
        }
        
        if (!tableExists) {
          missingTables.add(tableName);
        }
      }
      
      if (missingTables.isNotEmpty) {
        LoggerService.warning('MCP tables missing: ${missingTables.join(', ')}');
        return false;
      }
      
      LoggerService.info('All MCP tables exist or have valid fallbacks: ${existingTables.join(', ')}');
      return true;
    } catch (e) {
      LoggerService.error('Error checking MCP tables: $e');
      return false;
    }
  }
  
  /// Get current Supabase client instance
  static SupabaseClient get client {
    if (!_initialized) {
      throw Exception('Supabase not initialized. Call initialize() first.');
    }
    return Supabase.instance.client;
  }
  
  /// Check if specific tables exist
  static Future<Map<String, bool>> checkTables(List<String> tableNames) async {
    if (!_initialized) {
      throw Exception('Supabase not initialized. Call initialize() first.');
    }
    
    final supabase = Supabase.instance.client;
    final results = <String, bool>{};
    
    for (final tableName in tableNames) {
      try {
        await supabase
            .from(tableName)
            .select()
            .limit(0);
        results[tableName] = true;
      } catch (e) {
        results[tableName] = false;
      }
    }
    
    return results;
  }
  
  /// Create missing tables based on schema
  static Future<bool> createMissingTables() async {
    if (!_initialized) {
      throw Exception('Supabase not initialized. Call initialize() first.');
    }
    
    try {
      final supabase = Supabase.instance.client;
      
      // Check which tables are missing
      final tableStatus = await checkTables(_requiredTables);
      final missingTables = tableStatus.entries
          .where((entry) => !entry.value)
          .map((entry) => entry.key)
          .toList();
      
      if (missingTables.isEmpty) {
        LoggerService.info('All required tables already exist');
        return true;
      }
      
      LoggerService.info('Creating missing tables: ${missingTables.join(', ')}');
      
      // Execute SQL to create missing tables
      // Note: This would require RPC functions set up in Supabase
      // For security reasons, table creation should be done through migrations
      // This is just a placeholder for demonstration
      
      LoggerService.info('Table creation would need to be done through proper migrations');
      return false;
    } catch (e) {
      LoggerService.error('Error creating missing tables: $e');
      return false;
    }
  }
} 
