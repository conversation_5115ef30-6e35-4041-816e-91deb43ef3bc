import 'dart:convert';
import 'package:http/http.dart' as http;
import '../models/product.dart';
import '../models/ingredient.dart';
import '../services/gemini_service.dart';
import '../services/supabase_init_service.dart';
import '../services/logger_service.dart';

/// New Scan Workflow Service implementing the exact Mermaid flow diagram
/// Follows the workflow: Camera → Barcode Detection → Database Check → API → Ingredient Analysis → Safety Rating
class NewScanWorkflowService {
  
  /// Main workflow entry point following the Mermaid diagram exactly
  /// A["User Taps Scan Product Button"] → B["Open Scan View"] → C["Initialize Camera with Gemini AI"]
  static Future<Product?> executeWorkflow({
    required String barcode,
    String? userId,
    String? imagePath,
    Function(String stage, String detail)? onStageUpdate,
  }) async {
    try {
      print('🚀 Starting new scan workflow for barcode: $barcode');
      LoggerService.info('Starting new scan workflow for barcode: $barcode');

      // F["Extract Barcode Value"] - barcode already extracted by camera
      _updateStage(onStageUpdate, 'extracting_barcode', 'Barcode extracted: $barcode');

      // G["Check Database for Existing Product Profile"]
      _updateStage(onStageUpdate, 'checking_database', 'Checking database for existing product...');
      print('🔍 Checking database for product: $barcode');
      final existingProduct = await _checkDatabaseForProduct(barcode);
      print('📊 Database check result: ${existingProduct?.name ?? 'not found'}');
      
      // H{"Product Profile Exists?"}
      if (existingProduct != null) {
        // I["Retrieve Existing Product Profile"]
        _updateStage(onStageUpdate, 'retrieving_profile', 'Found existing product profile');
        LoggerService.info('Found existing product in database');
        return existingProduct;
      }
      
      // J["Query Open Food Facts API"]
      _updateStage(onStageUpdate, 'querying_api', 'Querying Open Food Facts API...');
      print('🌐 Querying Open Food Facts API for: $barcode');
      final apiData = await _queryOpenFoodFactsAPI(barcode);
      print('📡 API response: ${apiData != null ? 'success' : 'failed'}');

      // K{"Open Food Facts Success?"}
      if (apiData == null) {
        // L["Show 'Product not found' Message"]
        _updateStage(onStageUpdate, 'product_not_found', 'Product not found in database');
        LoggerService.warning('Product not found in Open Food Facts API');
        print('❌ Product not found in Open Food Facts API');
        return null;
      }
      
      // M["Extract Product Details & Ingredients List"]
      _updateStage(onStageUpdate, 'extracting_details', 'Extracting product details and ingredients...');
      final productDetails = await _extractProductDetails(apiData);
      final ingredientsList = await _extractIngredientsList(apiData);
      
      // N["Match Ingredients with Supabase Ingredients Database"]
      _updateStage(onStageUpdate, 'matching_ingredients', 'Matching ingredients with database...');
      final matchedIngredients = await _matchIngredientsWithDatabase(ingredientsList);
      
      // O["Process Each Ingredient"] & P{"Ingredient Found in Supabase DB?"}
      _updateStage(onStageUpdate, 'processing_ingredients', 'Processing ingredient safety data...');
      final processedIngredients = await _processIngredients(matchedIngredients, onStageUpdate);
      
      // V["Collect All Safety Ratings"] → W["Combine All Ingredients Safety Data"]
      _updateStage(onStageUpdate, 'analyzing_safety', 'Analyzing overall product safety...');
      final safetyAnalysis = await _analyzeOverallProductSafety(processedIngredients);
      
      // X["Analyze Overall Product Safety"] → Y{"Any RED (Unsafe) Ingredients?"}
      final productRating = _determineProductRating(processedIngredients);
      
      // Create Product Profile (CC or DD)
      _updateStage(onStageUpdate, 'creating_profile', 'Creating product profile...');
      final product = await _createProductProfile(
        barcode: barcode,
        productDetails: productDetails,
        ingredients: processedIngredients,
        safetyAnalysis: safetyAnalysis,
        productRating: productRating,
        userId: userId,
      );
      
      // EE["Save Product Profile to Database"]
      _updateStage(onStageUpdate, 'saving_profile', 'Saving product profile to database...');
      await _saveProductToDatabase(product);
      
      // FF["Display Detailed Product Info"]
      _updateStage(onStageUpdate, 'complete', 'Product analysis complete');
      LoggerService.info('Workflow completed successfully for barcode: $barcode');
      
      return product;
      
    } catch (e, stackTrace) {
      print('💥 Error in scan workflow: $e');
      print('📍 Stack trace: $stackTrace');
      LoggerService.error('Error in scan workflow: $e', stackTrace: stackTrace);
      _updateStage(onStageUpdate, 'error', 'Analysis failed: ${e.toString()}');
      rethrow;
    }
  }
  
  /// Helper method to update stage
  static void _updateStage(Function(String, String)? callback, String stage, String detail) {
    callback?.call(stage, detail);
    LoggerService.info('Workflow stage: $stage - $detail');
  }
  
  /// G["Check Database for Existing Product Profile"]
  static Future<Product?> _checkDatabaseForProduct(String barcode) async {
    try {
      print('🔍 Checking database for barcode: $barcode');
      final supabase = SupabaseInitService.client;
      print('📊 Supabase client initialized: ${supabase != null}');

      // Check product_analysis_fullscan table (main table)
      print('🔍 Querying product_analysis_fullscan table...');
      try {
        final response = await supabase
            .from('product_analysis_fullscan')
            .select('*')
            .eq('barcode', barcode)
            .limit(1);

        print('📊 Product_analysis_fullscan response: ${response.length} records found');

        if (response.isNotEmpty) {
          print('✅ Found product in product_analysis_fullscan table');
          return Product.fromDatabaseJson(response.first);
        }
      } catch (e) {
        print('⚠️ Product_analysis_fullscan table query failed: $e');
        // Continue to check scan_results table
      }

      // Check scan_results table as fallback
      print('🔍 Querying scan_results table...');
      try {
        final response = await supabase
            .from('scan_results')
            .select('*')
            .eq('barcode', barcode)
            .limit(1);

        print('📊 Scan_results response: ${response.length} records found');

        if (response.isNotEmpty) {
          print('✅ Found product in scan_results table');
          // Convert scan_results to Product format
          final scanResult = response.first;
          return Product(
            id: scanResult['product_id'] ?? scanResult['id'],
            name: scanResult['product_name'] ?? 'Unknown Product',
            barcode: scanResult['barcode'],
            brand: '',
            ingredients: [],
            safetyScore: 0,
            safetyRating: 'UNKNOWN',
            scannedAt: DateTime.tryParse(scanResult['scan_date'] ?? '') ?? DateTime.now(),
          );
        }
      } catch (e) {
        print('⚠️ Scan_results table query failed: $e');
      }
      
      if (response.isNotEmpty) {
        return Product.fromDatabaseJson(response.first);
      }
      
      // Also check products table as fallback
      final fallbackResponse = await supabase
          .from('products')
          .select('*')
          .eq('barcode', barcode)
          .limit(1);
      
      if (fallbackResponse.isNotEmpty) {
        return Product.fromDatabaseJson(fallbackResponse.first);
      }
      
      return null;
    } catch (e) {
      print('❌ Error checking database: $e');
      LoggerService.error('Error checking database for product: $e');
      return null;
    }
  }
  
  /// J["Query Open Food Facts API"]
  static Future<Map<String, dynamic>?> _queryOpenFoodFactsAPI(String barcode) async {
    try {
      final url = 'https://world.openfoodfacts.org/api/v0/product/$barcode.json';
      final response = await http.get(Uri.parse(url));
      
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['status'] == 1 && data['product'] != null) {
          return data['product'];
        }
      }
      
      return null;
    } catch (e) {
      LoggerService.error('Error querying Open Food Facts API: $e');
      return null;
    }
  }
  
  /// M["Extract Product Details & Ingredients List"]
  static Future<Map<String, dynamic>> _extractProductDetails(Map<String, dynamic> apiData) async {
    return {
      'name': apiData['product_name'] ?? apiData['product_name_en'] ?? 'Unknown Product',
      'brand': apiData['brands'] ?? '',
      'image_url': apiData['image_url'] ?? apiData['image_front_url'] ?? '',
      'description': apiData['generic_name'] ?? '',
    };
  }
  
  static Future<List<String>> _extractIngredientsList(Map<String, dynamic> apiData) async {
    final ingredientsText = apiData['ingredients_text'] ?? apiData['ingredients_text_en'] ?? '';
    
    if (ingredientsText.isEmpty) {
      return [];
    }
    
    // Parse ingredients list - split by comma and clean up
    return ingredientsText
        .split(',')
        .map((ingredient) => ingredient.trim())
        .where((ingredient) => ingredient.isNotEmpty)
        .toList();
  }
  
  /// N["Match Ingredients with Supabase Ingredients Database"]
  static Future<List<Map<String, dynamic>>> _matchIngredientsWithDatabase(List<String> ingredientsList) async {
    final matchedIngredients = <Map<String, dynamic>>[];
    
    for (final ingredientName in ingredientsList) {
      final normalizedName = ingredientName.toLowerCase().trim();
      
      try {
        final supabase = SupabaseInitService.client;
        
        // Try exact match first
        var response = await supabase
            .from('ingredients')
            .select('*')
            .eq('name', normalizedName)
            .limit(1);
        
        if (response.isNotEmpty) {
          matchedIngredients.add({
            'original_name': ingredientName,
            'matched': true,
            'ingredient_data': response.first,
          });
          continue;
        }
        
        // Try case-insensitive search
        response = await supabase
            .from('ingredients')
            .select('*')
            .ilike('name', '%$normalizedName%')
            .limit(1);
        
        if (response.isNotEmpty) {
          matchedIngredients.add({
            'original_name': ingredientName,
            'matched': true,
            'ingredient_data': response.first,
          });
        } else {
          // Not found in database
          matchedIngredients.add({
            'original_name': ingredientName,
            'matched': false,
            'ingredient_data': null,
          });
        }
      } catch (e) {
        LoggerService.error('Error matching ingredient $ingredientName: $e');
        matchedIngredients.add({
          'original_name': ingredientName,
          'matched': false,
          'ingredient_data': null,
        });
      }
    }
    
    return matchedIngredients;
  }
  
  /// O["Process Each Ingredient"] - handles both matched and unmatched ingredients
  static Future<List<Ingredient>> _processIngredients(
    List<Map<String, dynamic>> matchedIngredients,
    Function(String, String)? onStageUpdate,
  ) async {
    final processedIngredients = <Ingredient>[];
    
    for (final ingredientData in matchedIngredients) {
      final originalName = ingredientData['original_name'] as String;
      final isMatched = ingredientData['matched'] as bool;
      
      if (isMatched) {
        // Q["Get Safety Rating from DB"]
        final dbData = ingredientData['ingredient_data'] as Map<String, dynamic>;
        final ingredient = Ingredient.fromJson(dbData);
        processedIngredients.add(ingredient);
      } else {
        // R["Send to Gemini AI for Safety Analysis"]
        _updateStage(onStageUpdate, 'analyzing_ingredient', 'Analyzing unknown ingredient: $originalName');
        
        try {
          // S["Gemini Analyzes Safety Based on DB Pattern"] → T["Get Safety Rating from Gemini"]
          final aiAnalysis = await _analyzeIngredientWithGemini(originalName);
          
          if (aiAnalysis != null) {
            // U["Store New Ingredient in Supabase DB"]
            await _storeNewIngredientInDatabase(originalName, aiAnalysis);
            
            // Create ingredient from AI analysis
            final ingredient = _createIngredientFromAIAnalysis(originalName, aiAnalysis);
            processedIngredients.add(ingredient);
          } else {
            // Fallback for failed AI analysis
            final fallbackIngredient = _createFallbackIngredient(originalName);
            processedIngredients.add(fallbackIngredient);
          }
        } catch (e) {
          LoggerService.error('Error analyzing ingredient with AI: $originalName - $e');
          final fallbackIngredient = _createFallbackIngredient(originalName);
          processedIngredients.add(fallbackIngredient);
        }
      }
    }
    
    return processedIngredients;
  }

  /// R["Send to Gemini AI for Safety Analysis"] → S["Gemini Analyzes Safety Based on DB Pattern"]
  static Future<Map<String, dynamic>?> _analyzeIngredientWithGemini(String ingredientName) async {
    try {
      // Use the existing GeminiService method for ingredient analysis
      final response = await GeminiService.assessUnknownIngredient(ingredientName);

      // Convert the response to match our expected format
      return {
        'safety_level': _mapSafetyLevel(response['safety_level']),
        'safety_rating': response['safety_rating'] ?? 3,
        'description': response['description'] ?? 'AI-analyzed ingredient',
        'health_risks': response['health_risks'] ?? [],
        'category': response['category'] ?? 'unknown',
        'concern_level': response['concern_level'] ?? 'Medium',
      };
    } catch (e) {
      LoggerService.error('Error analyzing ingredient with Gemini: $ingredientName - $e');
      return null;
    }
  }

  /// Map safety level from GeminiService response to our format
  static String _mapSafetyLevel(dynamic safetyLevel) {
    if (safetyLevel == null) return 'yellow';

    final level = safetyLevel.toString().toLowerCase();
    switch (level) {
      case 'safe':
      case 'low':
        return 'green';
      case 'moderate':
      case 'medium':
        return 'yellow';
      case 'high':
      case 'unsafe':
      case 'dangerous':
        return 'red';
      default:
        return level.contains('green') ? 'green' :
               level.contains('red') ? 'red' : 'yellow';
    }
  }

  /// U["Store New Ingredient in Supabase DB"]
  static Future<void> _storeNewIngredientInDatabase(String name, Map<String, dynamic> aiAnalysis) async {
    try {
      final supabase = SupabaseInitService.client;

      final ingredientData = {
        'name': name.toLowerCase().trim(),
        'description': aiAnalysis['description'] ?? '',
        'safety_level': aiAnalysis['safety_level'] ?? 'yellow',
        'safety_rating': aiAnalysis['safety_rating'] ?? 3,
        'health_risks': aiAnalysis['health_risks'] ?? [],
        'category': aiAnalysis['category'] ?? 'unknown',
        'concern_level': aiAnalysis['concern_level'] ?? 'Medium',
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      };

      await supabase.from('ingredients').insert(ingredientData);
      LoggerService.info('Stored new ingredient in database: $name');
    } catch (e) {
      LoggerService.error('Error storing new ingredient in database: $name - $e');
      // Don't rethrow - this is not critical for the workflow
    }
  }

  /// Create ingredient from AI analysis
  static Ingredient _createIngredientFromAIAnalysis(String name, Map<String, dynamic> aiAnalysis) {
    return Ingredient(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      name: name,
      description: aiAnalysis['description'] ?? '',
      safetyLevel: aiAnalysis['safety_level'] ?? 'yellow',
      safetyRating: aiAnalysis['safety_rating'] ?? 3,
      healthRisks: List<String>.from(aiAnalysis['health_risks'] ?? []),
      category: aiAnalysis['category'] ?? 'unknown',
      concernLevel: aiAnalysis['concern_level'] ?? 'Medium',
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }

  /// Create fallback ingredient for failed analysis
  static Ingredient _createFallbackIngredient(String name) {
    return Ingredient(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      name: name,
      description: 'Ingredient analysis unavailable',
      safetyLevel: 'yellow', // Default to caution
      safetyRating: 3,
      healthRisks: ['Analysis unavailable'],
      category: 'unknown',
      concernLevel: 'Medium',
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }

  /// W["Combine All Ingredients Safety Data"] → X["Analyze Overall Product Safety"]
  static Future<Map<String, dynamic>> _analyzeOverallProductSafety(List<Ingredient> ingredients) async {
    final safetyAnalysis = {
      'total_ingredients': ingredients.length,
      'green_ingredients': 0,
      'yellow_ingredients': 0,
      'red_ingredients': 0,
      'unknown_ingredients': 0,
      'concerning_ingredients': <String>[],
      'safe_ingredients': <String>[],
      'caution_ingredients': <String>[],
    };

    for (final ingredient in ingredients) {
      switch (ingredient.safetyLevel.toLowerCase()) {
        case 'green':
          safetyAnalysis['green_ingredients'] = (safetyAnalysis['green_ingredients'] as int) + 1;
          (safetyAnalysis['safe_ingredients'] as List<String>).add(ingredient.name);
          break;
        case 'yellow':
          safetyAnalysis['yellow_ingredients'] = (safetyAnalysis['yellow_ingredients'] as int) + 1;
          (safetyAnalysis['caution_ingredients'] as List<String>).add(ingredient.name);
          break;
        case 'red':
          safetyAnalysis['red_ingredients'] = (safetyAnalysis['red_ingredients'] as int) + 1;
          (safetyAnalysis['concerning_ingredients'] as List<String>).add(ingredient.name);
          break;
        default:
          safetyAnalysis['unknown_ingredients'] = (safetyAnalysis['unknown_ingredients'] as int) + 1;
          (safetyAnalysis['caution_ingredients'] as List<String>).add(ingredient.name);
      }
    }

    return safetyAnalysis;
  }

  /// Y{"Any RED (Unsafe) Ingredients?"} → AA{"Only GREEN and YELLOW Ingredients?"}
  /// Implements the exact logic from the Mermaid diagram
  static String _determineProductRating(List<Ingredient> ingredients) {
    // Y{"Any RED (Unsafe) Ingredients?"}
    final hasRedIngredients = ingredients.any((ingredient) => ingredient.safetyLevel.toLowerCase() == 'red');

    if (hasRedIngredients) {
      // Y -- Yes --> Z["Mark Product as NOT RECOMMENDED"]
      return 'NOT RECOMMENDED';
    }

    // Y -- No --> AA{"Only GREEN and YELLOW Ingredients?"}
    final hasOnlyGreenAndYellow = ingredients.every((ingredient) =>
        ingredient.safetyLevel.toLowerCase() == 'green' ||
        ingredient.safetyLevel.toLowerCase() == 'yellow'
    );

    if (hasOnlyGreenAndYellow && ingredients.isNotEmpty) {
      // AA -- Yes --> BB["Mark Product as GOOD"]
      return 'GOOD';
    } else {
      // AA -- No --> Z["Mark Product as NOT RECOMMENDED"]
      return 'NOT RECOMMENDED';
    }
  }

  /// CC["Create Product Profile with NOT RECOMMENDED"] or DD["Create Product Profile with GOOD"]
  static Future<Product> _createProductProfile({
    required String barcode,
    required Map<String, dynamic> productDetails,
    required List<Ingredient> ingredients,
    required Map<String, dynamic> safetyAnalysis,
    required String productRating,
    String? userId,
  }) async {
    // Calculate safety score based on ingredients
    final safetyScore = _calculateSafetyScore(ingredients);

    return Product(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      name: productDetails['name'] ?? 'Unknown Product',
      brand: productDetails['brand'],
      barcode: barcode,
      imageUrl: productDetails['image_url'],
      safetyScore: safetyScore,
      safetyRating: productRating.toLowerCase(),
      ingredients: ingredients,
      alternatives: [], // Will be populated later if needed
      scannedAt: DateTime.now(),
      concerningIngredients: ingredients.where((i) => i.safetyLevel == 'red').toList(),
      concernsCount: safetyAnalysis['red_ingredients'] as int,
      ingredientsJson: {
        'ingredients': ingredients.map((i) => i.toJson()).toList(),
        'safety_analysis': safetyAnalysis,
      },
      safetyCriteriaJson: {
        'rating': productRating,
        'criteria_met': productRating == 'GOOD' ? ['No unsafe ingredients'] : [],
        'criteria_failed': productRating == 'NOT RECOMMENDED' ? ['Contains unsafe ingredients'] : [],
      },
      userId: userId,
      description: productDetails['description'],
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }

  /// Calculate safety score (0-100) based on ingredients
  static int _calculateSafetyScore(List<Ingredient> ingredients) {
    if (ingredients.isEmpty) return 50;

    final totalScore = ingredients.fold<int>(0, (sum, ingredient) => sum + ingredient.safetyScore);
    return (totalScore / ingredients.length).round();
  }

  /// EE["Save Product Profile to Database"]
  static Future<void> _saveProductToDatabase(Product product) async {
    try {
      final supabase = SupabaseInitService.client;

      final productData = {
        'barcode': product.barcode,
        'name': product.name,
        'brand': product.brand,
        'image': product.imageUrl,
        'safety_score': product.safetyScore,
        'safety_rating': product.safetyRating,
        'user_id': product.userId,
        'ingredients_json': product.ingredientsJson,
        'safety_criteria_json': product.safetyCriteriaJson,
        'concerns_count': product.concernsCount,
        'scanned_at': product.scannedAt?.toIso8601String(),
        'created_at': product.createdAt?.toIso8601String(),
        'updated_at': product.updatedAt?.toIso8601String(),
      };

      // Remove null values
      productData.removeWhere((key, value) => value == null);

      await supabase.from('product_analysis_fullscan').insert(productData);
      LoggerService.info('Saved product profile to database: ${product.barcode}');
    } catch (e) {
      LoggerService.error('Error saving product to database: $e');
      // Don't rethrow - the product can still be returned to the user
    }
  }

  /// Error handling methods for the workflow

  /// ER1["Camera Initialization Failed"]
  static Product createCameraErrorProduct() {
    return Product(
      id: 'error_camera',
      name: 'Camera Error',
      barcode: '',
      safetyScore: 0,
      safetyRating: 'error',
      ingredients: [],
      alternatives: [],
      scannedAt: DateTime.now(),
      description: 'Camera initialization failed. Please check camera permissions and try again.',
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }

  /// ER2["Barcode Detection Failed"]
  static Product createBarcodeDetectionErrorProduct() {
    return Product(
      id: 'error_barcode',
      name: 'Barcode Detection Error',
      barcode: '',
      safetyScore: 0,
      safetyRating: 'error',
      ingredients: [],
      alternatives: [],
      scannedAt: DateTime.now(),
      description: 'Could not detect barcode in image. Please ensure the barcode is clearly visible and try again.',
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }

  /// ER3["Open Food Facts API Failed"]
  static Product createAPIErrorProduct(String barcode) {
    return Product(
      id: 'error_api',
      name: 'API Error',
      barcode: barcode,
      safetyScore: 0,
      safetyRating: 'error',
      ingredients: [],
      alternatives: [],
      scannedAt: DateTime.now(),
      description: 'Failed to retrieve product information from external databases. Please try again later.',
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }

  /// ER4["Safety Analysis Failed"]
  static Product createSafetyAnalysisErrorProduct(String barcode, String productName) {
    return Product(
      id: 'error_safety',
      name: productName,
      barcode: barcode,
      safetyScore: 50, // Default neutral score
      safetyRating: 'unknown',
      ingredients: [],
      alternatives: [],
      scannedAt: DateTime.now(),
      description: 'Safety analysis could not be completed. Product information may be incomplete.',
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }

  /// ER5["Profile Save Failed"]
  static void handleProfileSaveError(String barcode, dynamic error) {
    LoggerService.error('Failed to save product profile for barcode $barcode: $error');
    // Continue without save - user still gets the analysis results
  }
}
