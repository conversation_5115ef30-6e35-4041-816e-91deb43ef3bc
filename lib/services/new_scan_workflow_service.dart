import 'dart:convert';
import 'package:http/http.dart' as http;
import '../models/product.dart';
import '../models/ingredient.dart';
import '../services/gemini_service.dart';
import '../services/supabase_init_service.dart';
import '../services/logger_service.dart';

/// New Scan Workflow Service implementing the exact Mermaid flow diagram
/// Follows the workflow: Camera → Barcode Detection → Database Check → API → Ingredient Analysis → Safety Rating
class NewScanWorkflowService {
  
  /// Main workflow entry point following the Mermaid diagram exactly
  /// A["User Taps Scan Product Button"] → B["Open Scan View"] → C["Initialize Camera with Gemini AI"]
  static Future<Product?> executeWorkflow({
    required String barcode,
    String? userId,
    String? imagePath,
    Function(String stage, String detail)? onStageUpdate,
  }) async {
    try {
      print('🚀 Starting new scan workflow for barcode: $barcode');
      LoggerService.info('Starting new scan workflow for barcode: $barcode');

      // F["Extract Barcode Value"] - barcode already extracted by camera
      _updateStage(onStageUpdate, 'extracting_barcode', 'Barcode extracted: $barcode');

      // G["Check Database for Existing Product Profile"]
      _updateStage(onStageUpdate, 'checking_database', 'Checking database for existing product...');
      print('🔍 Checking database for product: $barcode');
      final existingProduct = await _checkDatabaseForProduct(barcode);
      print('📊 Database check result: ${existingProduct?.name ?? 'not found'}');
      
      // Always fetch from OpenFoodFacts to get complete product information
      _updateStage(onStageUpdate, 'querying_api', 'Fetching product details from OpenFoodFacts...');
      print('🌐 Querying Open Food Facts API for: $barcode');
      final apiData = await _queryOpenFoodFactsAPI(barcode);
      print('📡 API response: ${apiData != null ? 'success' : 'failed'}');

      // K{"Open Food Facts Success?"}
      if (apiData == null) {
        // If API fails but we have existing product, return it
        if (existingProduct != null) {
          _updateStage(onStageUpdate, 'retrieving_profile', 'Using existing product profile');
          LoggerService.info('API failed, using existing product from database');
          print('⚠️ API failed, returning existing product: ${existingProduct.name}');
          return existingProduct;
        }

        // L["Show 'Product not found' Message"]
        _updateStage(onStageUpdate, 'product_not_found', 'Product not found in database');
        LoggerService.warning('Product not found in Open Food Facts API');
        print('❌ Product not found in Open Food Facts API');
        return null;
      }
      
      // M["Extract Product Details & Ingredients List"]
      _updateStage(onStageUpdate, 'extracting_details', 'Extracting product details and ingredients...');
      final productDetails = await _extractProductDetails(apiData);
      final ingredientsList = await _extractIngredientsList(apiData);
      
      // N["Match Ingredients with Supabase Ingredients Database"]
      _updateStage(onStageUpdate, 'matching_ingredients', 'Matching ingredients with database...');
      final matchedIngredients = await _matchIngredientsWithDatabase(ingredientsList);
      
      // O["Process Each Ingredient"] & P{"Ingredient Found in Supabase DB?"}
      _updateStage(onStageUpdate, 'processing_ingredients', 'Processing ingredient safety data...');
      final processedIngredients = await _processIngredients(matchedIngredients, onStageUpdate);
      
      // V["Collect All Safety Ratings"] → W["Combine All Ingredients Safety Data"]
      _updateStage(onStageUpdate, 'analyzing_safety', 'Analyzing overall product safety...');
      final safetyAnalysis = await _analyzeOverallProductSafety(processedIngredients);
      
      // X["Analyze Overall Product Safety"] → Y{"Any RED (Unsafe) Ingredients?"}
      final productRating = _determineProductRating(processedIngredients);
      
      // Create Product Profile (CC or DD)
      _updateStage(onStageUpdate, 'creating_profile', 'Creating product profile...');
      final product = await _createProductProfile(
        barcode: barcode,
        productDetails: productDetails,
        ingredients: processedIngredients,
        safetyAnalysis: safetyAnalysis,
        productRating: productRating,
        userId: userId,
      );
      
      // EE["Save Product Profile to Database"] - Always save enhanced data
      _updateStage(onStageUpdate, 'saving_profile', 'Saving enhanced product profile to database...');
      await _saveProductToDatabase(product);

      // FF["Display Detailed Product Info"]
      _updateStage(onStageUpdate, 'complete', 'Product analysis complete');
      LoggerService.info('Workflow completed successfully for barcode: $barcode');
      print('✅ Enhanced product ready: ${product.name} (${product.safetyRating})');

      return product;
      
    } catch (e, stackTrace) {
      print('💥 Error in scan workflow: $e');
      print('📍 Stack trace: $stackTrace');
      LoggerService.error('Error in scan workflow: $e', stackTrace: stackTrace);
      _updateStage(onStageUpdate, 'error', 'Analysis failed: ${e.toString()}');
      rethrow;
    }
  }
  
  /// Helper method to update stage
  static void _updateStage(Function(String, String)? callback, String stage, String detail) {
    callback?.call(stage, detail);
    LoggerService.info('Workflow stage: $stage - $detail');
  }
  
  /// G["Check Database for Existing Product Profile"]
  static Future<Product?> _checkDatabaseForProduct(String barcode) async {
    try {
      print('🔍 Checking database for barcode: $barcode');
      final supabase = SupabaseInitService.client;
      print('📊 Supabase client initialized: ${supabase != null}');

      // Check products table (main table)
      print('🔍 Querying products table...');
      try {
        // First try without user filter (for public products)
        var response = await supabase
            .from('products')
            .select('*')
            .eq('barcode', barcode)
            .limit(1);

        print('📊 Products table response (public): ${response.length} records found');

        if (response.isNotEmpty) {
          print('✅ Found product in products table (public)');
          return Product.fromDatabaseJson(response.first);
        }

        // Try with current user filter if authenticated
        final user = supabase.auth.currentUser;
        if (user != null) {
          print('🔍 Trying with user filter: ${user.id}');
          response = await supabase
              .from('products')
              .select('*')
              .eq('barcode', barcode)
              .eq('user_id', user.id)
              .limit(1);

          print('📊 Products table response (user): ${response.length} records found');

          if (response.isNotEmpty) {
            print('✅ Found product in products table (user)');
            return Product.fromDatabaseJson(response.first);
          }
        }
      } catch (e) {
        print('⚠️ Products table query failed: $e');
        print('⚠️ This might be due to RLS policies or table permissions');
        // Continue to check scan_results table
      }

      // Check scan_results table as fallback
      print('🔍 Querying scan_results table...');
      try {
        final response = await supabase
            .from('scan_results')
            .select('*')
            .eq('barcode', barcode)
            .limit(1);

        print('📊 Scan_results response: ${response.length} records found');

        if (response.isNotEmpty) {
          print('✅ Found product in scan_results table');
          // Convert scan_results to Product format
          final scanResult = response.first;
          return Product(
            id: scanResult['product_id'] ?? scanResult['id'],
            name: scanResult['product_name'] ?? 'Unknown Product',
            barcode: scanResult['barcode'],
            brand: '',
            ingredients: [],
            safetyScore: 0,
            safetyRating: 'UNKNOWN',
            scannedAt: DateTime.tryParse(scanResult['scan_date'] ?? '') ?? DateTime.now(),
          );
        }
      } catch (e) {
        print('⚠️ Scan_results table query failed: $e');
      }

      print('❌ No product found in any table');
      return null;
    } catch (e) {
      print('❌ Error checking database: $e');
      LoggerService.error('Error checking database for product: $e');
      return null;
    }
  }
  
  /// J["Query Open Food Facts API"]
  static Future<Map<String, dynamic>?> _queryOpenFoodFactsAPI(String barcode) async {
    // Try multiple barcode variations to handle partial extractions
    final barcodeVariations = _generateBarcodeVariations(barcode);

    for (final barcodeVariation in barcodeVariations) {
      try {
        final url = 'https://world.openfoodfacts.org/api/v0/product/$barcodeVariation.json';
        print('🌐 OpenFoodFacts API URL: $url');

        final response = await http.get(Uri.parse(url));
        print('📊 OpenFoodFacts API Status: ${response.statusCode}');

        if (response.statusCode == 200) {
          final data = json.decode(response.body);
          print('📦 OpenFoodFacts API Response status: ${data['status']}');

          if (data['status'] == 1 && data['product'] != null) {
            final product = data['product'];
            print('✅ OpenFoodFacts Product found with barcode $barcodeVariation: ${product['product_name'] ?? 'Unknown'}');
            print('🏷️ Brand: ${product['brands'] ?? 'Unknown'}');
            print('🖼️ Image: ${product['image_url'] ?? 'No image'}');
            return product;
          } else {
            print('❌ OpenFoodFacts: Product not found with barcode $barcodeVariation (status: ${data['status']})');
          }
        } else {
          print('❌ OpenFoodFacts API HTTP Error for $barcodeVariation: ${response.statusCode}');
        }
      } catch (e) {
        print('💥 OpenFoodFacts API Exception for $barcodeVariation: $e');
      }
    }

    LoggerService.error('Product not found with any barcode variation');
    return null;
  }

  /// Generate barcode variations to handle partial extractions
  static List<String> _generateBarcodeVariations(String barcode) {
    final variations = <String>[barcode]; // Start with original

    // Special case: if we detect a pattern that might be a partial barcode
    // For example: 9470001033 might be part of 894700010335
    if (barcode.length == 10) {
      // Try common UPC prefixes
      variations.add('0$barcode'); // Single leading zero
      variations.add('00$barcode'); // Double leading zero
      variations.add('89$barcode'); // Common prefix for food products
      variations.add('04$barcode'); // Common prefix for US products
      variations.add('03$barcode'); // Another common prefix

      // Special reconstruction for patterns like 9470001033 -> 894700010335
      // If barcode starts with 947, try 8947 prefix
      if (barcode.startsWith('947')) {
        final reconstructed = '8$barcode';
        variations.add(reconstructed);
        // Also try with additional digits
        variations.add('${reconstructed}5'); // Add common suffix
        variations.add('${reconstructed}0'); // Add common suffix
      }

      // If barcode starts with 470, try 8947 prefix
      if (barcode.startsWith('470')) {
        variations.add('89$barcode');
      }

      // Standard padding
      variations.add(barcode.padLeft(12, '0')); // UPC-A
      variations.add(barcode.padLeft(13, '0')); // EAN-13
    }
    // If barcode is 11 digits, try single digit prefixes
    else if (barcode.length == 11) {
      variations.add('0$barcode'); // Single leading zero
      variations.add('8$barcode'); // Common prefix
      variations.add('4$barcode'); // Another common prefix
      variations.add(barcode.padLeft(12, '0')); // UPC-A
      variations.add(barcode.padLeft(13, '0')); // EAN-13
    }
    // If barcode is 12 digits, try adding leading zero for EAN-13
    else if (barcode.length == 12) {
      variations.add('0$barcode'); // EAN-13
    }

    // Remove duplicates while preserving order
    final uniqueVariations = <String>[];
    for (final variation in variations) {
      if (!uniqueVariations.contains(variation)) {
        uniqueVariations.add(variation);
      }
    }

    print('🔄 Generated barcode variations: ${uniqueVariations.join(', ')}');
    return uniqueVariations;
  }
  
  /// M["Extract Product Details & Ingredients List"]
  static Future<Map<String, dynamic>> _extractProductDetails(Map<String, dynamic> apiData) async {
    // Extract comprehensive product information from OpenFoodFacts
    final productName = apiData['product_name'] ??
                       apiData['product_name_en'] ??
                       apiData['product_name_fr'] ??
                       'Unknown Product';

    final brands = apiData['brands'] ?? '';
    final imageUrl = apiData['image_url'] ??
                    apiData['image_front_url'] ??
                    apiData['image_front_small_url'] ?? '';

    final description = apiData['generic_name'] ??
                       apiData['generic_name_en'] ??
                       apiData['categories'] ?? '';

    // Extract nutrition information
    final nutritionData = apiData['nutriments'] ?? {};
    final nutritionScore = {
      'energy': nutritionData['energy-kcal_100g'],
      'fat': nutritionData['fat_100g'],
      'saturated_fat': nutritionData['saturated-fat_100g'],
      'carbohydrates': nutritionData['carbohydrates_100g'],
      'sugars': nutritionData['sugars_100g'],
      'fiber': nutritionData['fiber_100g'],
      'proteins': nutritionData['proteins_100g'],
      'salt': nutritionData['salt_100g'],
      'sodium': nutritionData['sodium_100g'],
    };

    return {
      'name': productName,
      'brand': brands,
      'image_url': imageUrl,
      'description': description,
      'categories': apiData['categories'] ?? '',
      'quantity': apiData['quantity'] ?? '',
      'packaging': apiData['packaging'] ?? '',
      'nutrition_score': nutritionScore,
      'nutrition_grade': apiData['nutrition_grade_fr'] ?? apiData['nutriscore_grade'],
      'ecoscore_grade': apiData['ecoscore_grade'],
      'nova_group': apiData['nova_group'],
    };
  }
  
  static Future<List<String>> _extractIngredientsList(Map<String, dynamic> apiData) async {
    final ingredientsText = apiData['ingredients_text'] ?? apiData['ingredients_text_en'] ?? '';
    
    if (ingredientsText.isEmpty) {
      return [];
    }
    
    // Parse ingredients list - split by comma and clean up
    return ingredientsText
        .split(',')
        .map((ingredient) => ingredient.trim())
        .where((ingredient) => ingredient.isNotEmpty)
        .toList();
  }
  
  /// N["Match Ingredients with Supabase Ingredients Database"]
  static Future<List<Map<String, dynamic>>> _matchIngredientsWithDatabase(List<String> ingredientsList) async {
    final matchedIngredients = <Map<String, dynamic>>[];
    
    for (final ingredientName in ingredientsList) {
      final normalizedName = ingredientName.toLowerCase().trim();
      
      try {
        final supabase = SupabaseInitService.client;
        
        // Try exact match first
        var response = await supabase
            .from('ingredients')
            .select('*')
            .eq('name', normalizedName)
            .limit(1);
        
        if (response.isNotEmpty) {
          matchedIngredients.add({
            'original_name': ingredientName,
            'matched': true,
            'ingredient_data': response.first,
          });
          continue;
        }
        
        // Try case-insensitive search
        response = await supabase
            .from('ingredients')
            .select('*')
            .ilike('name', '%$normalizedName%')
            .limit(1);
        
        if (response.isNotEmpty) {
          matchedIngredients.add({
            'original_name': ingredientName,
            'matched': true,
            'ingredient_data': response.first,
          });
        } else {
          // Not found in database
          matchedIngredients.add({
            'original_name': ingredientName,
            'matched': false,
            'ingredient_data': null,
          });
        }
      } catch (e) {
        LoggerService.error('Error matching ingredient $ingredientName: $e');
        matchedIngredients.add({
          'original_name': ingredientName,
          'matched': false,
          'ingredient_data': null,
        });
      }
    }
    
    return matchedIngredients;
  }
  
  /// O["Process Each Ingredient"] - handles both matched and unmatched ingredients
  static Future<List<Ingredient>> _processIngredients(
    List<Map<String, dynamic>> matchedIngredients,
    Function(String, String)? onStageUpdate,
  ) async {
    final processedIngredients = <Ingredient>[];
    
    for (final ingredientData in matchedIngredients) {
      final originalName = ingredientData['original_name'] as String;
      final isMatched = ingredientData['matched'] as bool;
      
      if (isMatched) {
        // Q["Get Safety Rating from DB"]
        final dbData = ingredientData['ingredient_data'] as Map<String, dynamic>;
        final ingredient = Ingredient.fromJson(dbData);
        processedIngredients.add(ingredient);
      } else {
        // R["Send to Gemini AI for Safety Analysis"]
        _updateStage(onStageUpdate, 'analyzing_ingredient', 'Analyzing unknown ingredient: $originalName');
        
        try {
          // S["Gemini Analyzes Safety Based on DB Pattern"] → T["Get Safety Rating from Gemini"]
          final aiAnalysis = await _analyzeIngredientWithGemini(originalName);
          
          if (aiAnalysis != null) {
            // U["Store New Ingredient in Supabase DB"]
            await _storeNewIngredientInDatabase(originalName, aiAnalysis);
            
            // Create ingredient from AI analysis
            final ingredient = _createIngredientFromAIAnalysis(originalName, aiAnalysis);
            processedIngredients.add(ingredient);
          } else {
            // Fallback for failed AI analysis
            final fallbackIngredient = _createFallbackIngredient(originalName);
            processedIngredients.add(fallbackIngredient);
          }
        } catch (e) {
          LoggerService.error('Error analyzing ingredient with AI: $originalName - $e');
          final fallbackIngredient = _createFallbackIngredient(originalName);
          processedIngredients.add(fallbackIngredient);
        }
      }
    }
    
    return processedIngredients;
  }

  /// R["Send to Gemini AI for Safety Analysis"] → S["Gemini Analyzes Safety Based on DB Pattern"]
  static Future<Map<String, dynamic>?> _analyzeIngredientWithGemini(String ingredientName) async {
    try {
      // Use the existing GeminiService method for ingredient analysis
      final response = await GeminiService.assessUnknownIngredient(ingredientName);

      // Convert the response to match our expected format
      return {
        'safety_level': _mapSafetyLevel(response['safety_level']),
        'safety_rating': response['safety_rating'] ?? 3,
        'description': response['description'] ?? 'AI-analyzed ingredient',
        'health_risks': response['health_risks'] ?? [],
        'category': response['category'] ?? 'unknown',
        'concern_level': response['concern_level'] ?? 'Medium',
      };
    } catch (e) {
      LoggerService.error('Error analyzing ingredient with Gemini: $ingredientName - $e');
      return null;
    }
  }

  /// Map safety level from GeminiService response to our format
  static String _mapSafetyLevel(dynamic safetyLevel) {
    if (safetyLevel == null) return 'yellow';

    final level = safetyLevel.toString().toLowerCase();
    switch (level) {
      case 'safe':
      case 'low':
        return 'green';
      case 'moderate':
      case 'medium':
        return 'yellow';
      case 'high':
      case 'unsafe':
      case 'dangerous':
        return 'red';
      default:
        return level.contains('green') ? 'green' :
               level.contains('red') ? 'red' : 'yellow';
    }
  }

  /// U["Store New Ingredient in Supabase DB"]
  static Future<void> _storeNewIngredientInDatabase(String name, Map<String, dynamic> aiAnalysis) async {
    try {
      final supabase = SupabaseInitService.client;

      final ingredientData = {
        'name': name.toLowerCase().trim(),
        'description': aiAnalysis['description'] ?? '',
        'safety_level': aiAnalysis['safety_level'] ?? 'yellow',
        'safety_rating': aiAnalysis['safety_rating'] ?? 3,
        'health_risks': aiAnalysis['health_risks'] ?? [],
        'category': aiAnalysis['category'] ?? 'unknown',
        'concern_level': aiAnalysis['concern_level'] ?? 'Medium',
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      };

      await supabase.from('ingredients').insert(ingredientData);
      LoggerService.info('Stored new ingredient in database: $name');
    } catch (e) {
      LoggerService.error('Error storing new ingredient in database: $name - $e');
      // Don't rethrow - this is not critical for the workflow
    }
  }

  /// Create ingredient from AI analysis
  static Ingredient _createIngredientFromAIAnalysis(String name, Map<String, dynamic> aiAnalysis) {
    return Ingredient(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      name: name,
      description: aiAnalysis['description'] ?? '',
      safetyLevel: aiAnalysis['safety_level'] ?? 'yellow',
      safetyRating: aiAnalysis['safety_rating'] ?? 3,
      healthRisks: List<String>.from(aiAnalysis['health_risks'] ?? []),
      category: aiAnalysis['category'] ?? 'unknown',
      concernLevel: aiAnalysis['concern_level'] ?? 'Medium',
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }

  /// Create fallback ingredient for failed analysis
  static Ingredient _createFallbackIngredient(String name) {
    return Ingredient(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      name: name,
      description: 'Ingredient analysis unavailable',
      safetyLevel: 'yellow', // Default to caution
      safetyRating: 3,
      healthRisks: ['Analysis unavailable'],
      category: 'unknown',
      concernLevel: 'Medium',
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }

  /// W["Combine All Ingredients Safety Data"] → X["Analyze Overall Product Safety"]
  static Future<Map<String, dynamic>> _analyzeOverallProductSafety(List<Ingredient> ingredients) async {
    final safetyAnalysis = {
      'total_ingredients': ingredients.length,
      'green_ingredients': 0,
      'yellow_ingredients': 0,
      'red_ingredients': 0,
      'unknown_ingredients': 0,
      'concerning_ingredients': <String>[],
      'safe_ingredients': <String>[],
      'caution_ingredients': <String>[],
    };

    for (final ingredient in ingredients) {
      switch (ingredient.safetyLevel.toLowerCase()) {
        case 'green':
          safetyAnalysis['green_ingredients'] = (safetyAnalysis['green_ingredients'] as int) + 1;
          (safetyAnalysis['safe_ingredients'] as List<String>).add(ingredient.name);
          break;
        case 'yellow':
          safetyAnalysis['yellow_ingredients'] = (safetyAnalysis['yellow_ingredients'] as int) + 1;
          (safetyAnalysis['caution_ingredients'] as List<String>).add(ingredient.name);
          break;
        case 'red':
          safetyAnalysis['red_ingredients'] = (safetyAnalysis['red_ingredients'] as int) + 1;
          (safetyAnalysis['concerning_ingredients'] as List<String>).add(ingredient.name);
          break;
        default:
          safetyAnalysis['unknown_ingredients'] = (safetyAnalysis['unknown_ingredients'] as int) + 1;
          (safetyAnalysis['caution_ingredients'] as List<String>).add(ingredient.name);
      }
    }

    return safetyAnalysis;
  }

  /// Y{"Any RED (Unsafe) Ingredients?"} → AA{"Only GREEN and YELLOW Ingredients?"}
  /// Implements the exact logic from the Mermaid diagram
  static String _determineProductRating(List<Ingredient> ingredients) {
    // Y{"Any RED (Unsafe) Ingredients?"}
    final hasRedIngredients = ingredients.any((ingredient) => ingredient.safetyLevel.toLowerCase() == 'red');

    if (hasRedIngredients) {
      // Y -- Yes --> Z["Mark Product as NOT RECOMMENDED"]
      return 'NOT RECOMMENDED';
    }

    // Y -- No --> AA{"Only GREEN and YELLOW Ingredients?"}
    final hasOnlyGreenAndYellow = ingredients.every((ingredient) =>
        ingredient.safetyLevel.toLowerCase() == 'green' ||
        ingredient.safetyLevel.toLowerCase() == 'yellow'
    );

    if (hasOnlyGreenAndYellow && ingredients.isNotEmpty) {
      // AA -- Yes --> BB["Mark Product as GOOD"]
      return 'GOOD';
    } else {
      // AA -- No --> Z["Mark Product as NOT RECOMMENDED"]
      return 'NOT RECOMMENDED';
    }
  }

  /// CC["Create Product Profile with NOT RECOMMENDED"] or DD["Create Product Profile with GOOD"]
  static Future<Product> _createProductProfile({
    required String barcode,
    required Map<String, dynamic> productDetails,
    required List<Ingredient> ingredients,
    required Map<String, dynamic> safetyAnalysis,
    required String productRating,
    String? userId,
  }) async {
    // Calculate safety score based on product rating
    final safetyScore = _calculateSafetyScore(productRating);

    return Product(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      name: productDetails['name'] ?? 'Unknown Product',
      brand: productDetails['brand'],
      barcode: barcode,
      imageUrl: productDetails['image_url'],
      safetyScore: safetyScore,
      safetyRating: productRating.toLowerCase(),
      ingredients: ingredients,
      alternatives: [], // Will be populated later if needed
      scannedAt: DateTime.now(),
      concerningIngredients: ingredients.where((i) => i.safetyLevel == 'red').toList(),
      concernsCount: safetyAnalysis['red_ingredients'] as int,
      ingredientsJson: {
        'ingredients': ingredients.map((i) => i.toJson()).toList(),
        'safety_analysis': safetyAnalysis,
        'categories': productDetails['categories'],
        'quantity': productDetails['quantity'],
        'packaging': productDetails['packaging'],
      },
      alternativesJson: {}, // Empty for now
      nutritionScore: productDetails['nutrition_score'] ?? {},
      nutritionConcerns: {
        'nutrition_grade': productDetails['nutrition_grade'],
        'ecoscore_grade': productDetails['ecoscore_grade'],
        'nova_group': productDetails['nova_group'],
      },
      safetyCriteriaJson: {
        'rating': productRating,
        'criteria_met': productRating == 'GOOD' ? ['No unsafe ingredients'] : [],
        'criteria_failed': productRating == 'NOT RECOMMENDED' ? ['Contains unsafe ingredients'] : [],
      },
      matchStatsJson: {
        'total_ingredients': ingredients.length,
        'matched_ingredients': ingredients.where((i) => i.safetyLevel != 'unknown').length,
        'confidence_score': ingredients.isNotEmpty ?
          (ingredients.where((i) => i.safetyLevel != 'unknown').length / ingredients.length) : 0.0,
      },
      userId: userId,
      description: productDetails['description'],
      editedImage: null, // Not from OpenFoodFacts
      productUrl: 'https://world.openfoodfacts.org/product/$barcode',
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }

  /// Calculate simple safety score based on product rating
  static int _calculateSafetyScore(String productRating) {
    switch (productRating.toUpperCase()) {
      case 'GOOD':
        return 85; // High score for good products
      case 'NOT RECOMMENDED':
        return 25; // Low score for not recommended products
      default:
        return 50; // Neutral score for unknown
    }
  }

  /// EE["Save Product Profile to Database"]
  static Future<void> _saveProductToDatabase(Product product) async {
    try {
      print('💾 Attempting to save product to database: ${product.barcode}');
      final supabase = SupabaseInitService.client;
      final user = supabase.auth.currentUser;

      final productData = {
        'barcode': product.barcode,
        'name': product.name,
        'brand': product.brand ?? '',
        'image': product.imageUrl,  // Matches actual schema
        'edited_image': product.editedImage,
        'product_url': product.productUrl,
        'safety_score': product.safetyScore ?? 0,
        'safety_rating': product.safetyRating ?? 'UNKNOWN',
        'user_id': user?.id, // Use current user ID if authenticated
        'ingredients_json': product.ingredientsJson ?? {},
        'alternatives_json': product.alternativesJson ?? {},
        'nutrition_score': product.nutritionScore ?? {},  // Changed from nutrition_json
        'nutrition_concerns': product.nutritionConcerns ?? {},  // Correct column name
        'match_stats_json': product.matchStatsJson ?? {},
        'concerns_count': product.concernsCount ?? 0,
        'scanned_at': product.scannedAt?.toIso8601String() ?? DateTime.now().toIso8601String(),
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      };

      // Remove null values
      productData.removeWhere((key, value) => value == null);

      print('💾 Product data prepared: ${productData.keys.join(', ')}');

      try {
        // Use upsert to insert or update existing product
        await supabase.from('products').upsert(productData, onConflict: 'barcode');
        print('✅ Successfully saved/updated product in products table');
        LoggerService.info('Saved/updated product profile to database: ${product.barcode}');
      } catch (e) {
        print('❌ Failed to save to products table: $e');

        // Try fallback to scan_results table
        try {
          final scanData = {
            'barcode': product.barcode,
            'product_name': product.name,
            'user_id': user?.id,
            'scan_date': DateTime.now().toIso8601String(),
            'confidence_score': 0.8,
            'source': 'new_workflow',
            'created_at': DateTime.now().toIso8601String(),
            'updated_at': DateTime.now().toIso8601String(),
          };

          await supabase.from('scan_results').insert(scanData);
          print('✅ Successfully saved to scan_results table as fallback');
          LoggerService.info('Saved product to scan_results table: ${product.barcode}');
        } catch (fallbackError) {
          print('❌ Fallback save also failed: $fallbackError');
          throw e; // Rethrow original error
        }
      }
    } catch (e) {
      print('💥 Error saving product to database: $e');
      LoggerService.error('Error saving product to database: $e');
      // Don't rethrow - the product can still be returned to the user
    }
  }

  /// Error handling methods for the workflow

  /// ER1["Camera Initialization Failed"]
  static Product createCameraErrorProduct() {
    return Product(
      id: 'error_camera',
      name: 'Camera Error',
      barcode: '',
      safetyScore: 0,
      safetyRating: 'error',
      ingredients: [],
      alternatives: [],
      scannedAt: DateTime.now(),
      description: 'Camera initialization failed. Please check camera permissions and try again.',
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }

  /// ER2["Barcode Detection Failed"]
  static Product createBarcodeDetectionErrorProduct() {
    return Product(
      id: 'error_barcode',
      name: 'Barcode Detection Error',
      barcode: '',
      safetyScore: 0,
      safetyRating: 'error',
      ingredients: [],
      alternatives: [],
      scannedAt: DateTime.now(),
      description: 'Could not detect barcode in image. Please ensure the barcode is clearly visible and try again.',
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }

  /// ER3["Open Food Facts API Failed"]
  static Product createAPIErrorProduct(String barcode) {
    return Product(
      id: 'error_api',
      name: 'API Error',
      barcode: barcode,
      safetyScore: 0,
      safetyRating: 'error',
      ingredients: [],
      alternatives: [],
      scannedAt: DateTime.now(),
      description: 'Failed to retrieve product information from external databases. Please try again later.',
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }

  /// ER4["Safety Analysis Failed"]
  static Product createSafetyAnalysisErrorProduct(String barcode, String productName) {
    return Product(
      id: 'error_safety',
      name: productName,
      barcode: barcode,
      safetyScore: 50, // Default neutral score
      safetyRating: 'unknown',
      ingredients: [],
      alternatives: [],
      scannedAt: DateTime.now(),
      description: 'Safety analysis could not be completed. Product information may be incomplete.',
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }

  /// ER5["Profile Save Failed"]
  static void handleProfileSaveError(String barcode, dynamic error) {
    LoggerService.error('Failed to save product profile for barcode $barcode: $error');
    // Continue without save - user still gets the analysis results
  }
}
