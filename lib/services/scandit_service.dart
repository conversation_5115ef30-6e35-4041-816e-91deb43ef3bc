import 'dart:async';
import 'package:flutter/material.dart';
import 'package:scandit_flutter_datacapture_barcode/scandit_flutter_datacapture_barcode.dart';
import 'package:scandit_flutter_datacapture_core/scandit_flutter_datacapture_core.dart';
import 'package:permission_handler/permission_handler.dart';
import 'logger_service.dart';

/// Scandit barcode scanning service for accurate retail product scanning
class ScanditService {
  static const String _licenseKey = 'YOUR_SCANDIT_LICENSE_KEY'; // Replace with your actual license key
  
  static DataCaptureContext? _dataCaptureContext;
  static Camera? _camera;
  static BarcodeCapture? _barcodeCapture;
  static DataCaptureView? _dataCaptureView;
  
  static bool _isInitialized = false;
  static StreamController<String>? _barcodeStreamController;
  
  /// Initialize Scandit SDK
  static Future<bool> initialize() async {
    if (_isInitialized) return true;
    
    try {
      LoggerService.info('Initializing Scandit SDK...');
      
      // Request camera permission
      final cameraPermission = await Permission.camera.request();
      if (cameraPermission != PermissionStatus.granted) {
        LoggerService.error('Camera permission denied');
        return false;
      }
      
      // Initialize DataCapture context with license key
      _dataCaptureContext = DataCaptureContext.forLicenseKey(_licenseKey);
      
      // Get default camera
      _camera = Camera.defaultCamera;
      if (_camera != null) {
        await _camera!.applySettings(CameraSettings());
        _dataCaptureContext!.setFrameSource(_camera!);
      }
      
      // Configure barcode capture settings
      final settings = BarcodeCaptureSettings();
      
      // Enable common retail barcode symbologies
      settings.enableSymbologies({
        Symbology.ean13Upca,    // Most common for retail products
        Symbology.ean8,         // Short EAN codes
        Symbology.upce,         // UPC-E codes
        Symbology.code128,      // Code 128
        Symbology.code39,       // Code 39
        Symbology.dataMatrix,   // Data Matrix
        Symbology.qr,           // QR codes
      });
      
      // Create barcode capture mode
      _barcodeCapture = BarcodeCapture.forDataCaptureContext(_dataCaptureContext!, settings);
      
      _isInitialized = true;
      LoggerService.info('✅ Scandit SDK initialized successfully');
      return true;
      
    } catch (e) {
      LoggerService.error('Failed to initialize Scandit SDK: $e');
      return false;
    }
  }
  
  /// Create a barcode scanning widget
  static Widget createScanningWidget({
    required Function(String barcode) onBarcodeScanned,
    required VoidCallback onError,
  }) {
    if (!_isInitialized) {
      return const Center(
        child: Text('Scandit not initialized'),
      );
    }
    
    try {
      // Create data capture view
      _dataCaptureView = DataCaptureView.forContext(_dataCaptureContext!);
      
      // Set up barcode capture listener
      _barcodeCapture!.addListener(_ScanditBarcodeListener(onBarcodeScanned));
      
      // Create overlay for visual feedback
      final overlay = BarcodeCaptureOverlay.withBarcodeCaptureForView(
        _barcodeCapture!,
        _dataCaptureView!,
      );
      
      // Configure overlay appearance
      overlay.viewfinder = RectangularViewfinder.withStyle(RectangularViewfinderStyle.square);
      
      return _dataCaptureView!;
      
    } catch (e) {
      LoggerService.error('Error creating scanning widget: $e');
      onError();
      return const Center(
        child: Text('Error creating scanner'),
      );
    }
  }
  
  /// Start scanning
  static Future<void> startScanning() async {
    try {
      if (_camera != null && _barcodeCapture != null) {
        await _camera!.switchToDesiredState(FrameSourceState.on);
        _barcodeCapture!.isEnabled = true;
        LoggerService.info('📷 Scandit scanning started');
      }
    } catch (e) {
      LoggerService.error('Error starting scanning: $e');
    }
  }
  
  /// Stop scanning
  static Future<void> stopScanning() async {
    try {
      if (_camera != null && _barcodeCapture != null) {
        _barcodeCapture!.isEnabled = false;
        await _camera!.switchToDesiredState(FrameSourceState.off);
        LoggerService.info('📷 Scandit scanning stopped');
      }
    } catch (e) {
      LoggerService.error('Error stopping scanning: $e');
    }
  }
  
  /// Dispose resources
  static Future<void> dispose() async {
    try {
      await stopScanning();
      _barcodeCapture?.removeListener(_ScanditBarcodeListener((_) {}));
      _dataCaptureView?.removeFromSuperview();
      _dataCaptureContext?.dispose();
      
      _dataCaptureContext = null;
      _camera = null;
      _barcodeCapture = null;
      _dataCaptureView = null;
      _isInitialized = false;
      
      LoggerService.info('🧹 Scandit resources disposed');
    } catch (e) {
      LoggerService.error('Error disposing Scandit resources: $e');
    }
  }
  
  /// Check if Scandit is initialized
  static bool get isInitialized => _isInitialized;
}

/// Barcode capture listener for handling scan results
class _ScanditBarcodeListener extends BarcodeCaptureListener {
  final Function(String) onBarcodeScanned;
  
  _ScanditBarcodeListener(this.onBarcodeScanned);
  
  @override
  void didScanInSession(BarcodeCapture barcodeCapture, BarcodeCaptureSession session) {
    final recognizedBarcodes = session.newlyRecognizedBarcodes;
    
    if (recognizedBarcodes.isNotEmpty) {
      final barcode = recognizedBarcodes.first;
      final barcodeData = barcode.data;
      
      if (barcodeData != null && barcodeData.isNotEmpty) {
        LoggerService.info('📊 Scandit detected barcode: $barcodeData (${barcode.symbology})');
        
        // Temporarily disable scanning to prevent multiple scans
        barcodeCapture.isEnabled = false;
        
        // Call the callback with the scanned barcode
        onBarcodeScanned(barcodeData);
        
        // Re-enable scanning after a short delay
        Future.delayed(const Duration(seconds: 2), () {
          barcodeCapture.isEnabled = true;
        });
      }
    }
  }
  
  @override
  void didUpdateSession(BarcodeCapture barcodeCapture, BarcodeCaptureSession session) {
    // Handle session updates if needed
  }
}
