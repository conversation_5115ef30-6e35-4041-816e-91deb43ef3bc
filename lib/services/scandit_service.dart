import 'dart:async';
import 'package:flutter/material.dart';
import 'package:scandit_flutter_datacapture_core/scandit_flutter_datacapture_core.dart';
import 'package:scandit_flutter_datacapture_barcode/scandit_flutter_datacapture_barcode.dart';
import 'package:permission_handler/permission_handler.dart';
import 'logger_service.dart';
import '../config/scandit_config.dart';

/// Scandit barcode scanning service for accurate retail product scanning
/// Based on official Scandit Flutter samples
class ScanditService {

  /// Initialize the Scandit SDK
  /// Must be called before using any Scandit functionality
  static Future<void> initialize() async {
    await ScanditFlutterDataCaptureBarcode.initialize();
    LoggerService.info('✅ Scandit SDK initialized');
  }

  /// Create a DataCaptureContext with the license key
  static DataCaptureContext createDataCaptureContext() {
    return DataCaptureContext.forLicenseKey(ScanditConfig.getLicenseKey());
  }

  /// Create barcode capture settings optimized for retail products
  static BarcodeCaptureSettings createBarcodeCaptureSettings() {
    final settings = BarcodeCaptureSettings();

    // Enable common retail barcode symbologies
    settings.enableSymbologies({
      Symbology.ean8,           // EAN-8
      Symbology.ean13Upca,      // EAN-13 & UPC-A (most common for retail)
      Symbology.upce,           // UPC-E
      Symbology.qr,             // QR codes
      Symbology.dataMatrix,     // Data Matrix
      Symbology.code39,         // Code 39
      Symbology.code128,        // Code 128
      Symbology.interleavedTwoOfFive, // ITF
    });

    // Configure Code 39 for variable length (common in retail)
    settings.settingsForSymbology(Symbology.code39).activeSymbolCounts =
      {for (var i = 7; i <= 20; i++) i};

    LoggerService.info('📊 Scandit barcode settings configured for retail products');
    return settings;
  }

  /// Get recommended camera settings for barcode capture
  static CameraSettings getRecommendedCameraSettings() {
    return BarcodeCapture.recommendedCameraSettings;
  }
}
