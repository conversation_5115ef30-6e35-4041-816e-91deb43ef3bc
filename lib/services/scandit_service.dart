import 'dart:async';
import 'package:scandit_flutter_datacapture_core/scandit_flutter_datacapture_core.dart';
import 'package:scandit_flutter_datacapture_barcode/scandit_flutter_datacapture_barcode.dart';
import 'package:scandit_flutter_datacapture_barcode/scandit_flutter_datacapture_spark_scan.dart';
import 'logger_service.dart';
import '../config/scandit_config.dart';

/// Scandit service for retail product barcode scanning
/// Uses Scandit's core BarcodeCapture technology
class ScanditService {

  /// Initialize the Scandit SDK
  /// Must be called before using any Scandit functionality
  static Future<void> initialize() async {
    await ScanditFlutterDataCaptureBarcode.initialize();
    LoggerService.info('✅ Scandit SDK initialized');
  }

  /// Create a DataCaptureContext with the license key
  static DataCaptureContext createDataCaptureContext() {
    return DataCaptureContext.forLicenseKey(ScanditConfig.getLicenseKey());
  }

  /// Create SparkScan settings optimized for retail products
  static SparkScanSettings createSparkScanSettings() {
    final settings = SparkScanSettings();

    // Enable common retail barcode symbologies
    settings.enableSymbologies({
      Symbology.ean8,           // EAN-8
      Symbology.ean13Upca,      // EAN-13 & UPC-A (most common for retail)
      Symbology.upce,           // UPC-E
      Symbology.qr,             // QR codes
      Symbology.dataMatrix,     // Data Matrix
      Symbology.code39,         // Code 39
      Symbology.code128,        // Code 128
      Symbology.interleavedTwoOfFive, // ITF
    });

    // Configure Code 39 for variable length (common in retail)
    settings.settingsForSymbology(Symbology.code39).activeSymbolCounts =
      {for (var i = 7; i <= 20; i++) i};

    LoggerService.info('📊 SparkScan settings configured for retail products');
    return settings;
  }

  /// Create SparkScanView settings for optimal retail scanning UI
  static SparkScanViewSettings createSparkScanViewSettings() {
    final viewSettings = SparkScanViewSettings();

    LoggerService.info('🎨 SparkScan view settings configured for retail UI');
    return viewSettings;
  }
}
