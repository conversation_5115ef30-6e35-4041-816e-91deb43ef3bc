import 'dart:async';
import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:uuid/uuid.dart';

import '../models/ingredient.dart';
import '../models/product.dart';
import '../constants/safety_criteria.dart';
import '../widgets/analysis_loading_overlay.dart';
import 'supabase_service.dart';
import 'supabase_init_service.dart';
import 'logger_service.dart';
import 'image_analysis_service.dart';
import 'web_scraping_service.dart';

/// Enhanced SafeScan service implementing the new Mermaid workflow
/// Follows the exact flow: Camera → Barcode Detection → Database Check → API → Ingredient Analysis → Safety Rating
class EnhancedSafeScanService {
  static final Uuid _uuid = Uuid();

  final SupabaseService supabaseService;
  EnhancedSafeScanService(this.supabaseService);

  /// Main entry point - implements the new Mermaid flow diagram exactly
  /// A["User Taps Scan Product Button"] → B["Open Scan View"] → C["Initialize Camera with Gemini AI"]
  /// → D["Auto-Detect Barcode using Gemini AI"] → ... (follows complete flow)
  static Future<Product?> analyzeProductByBarcode(
    String barcode, {
    String? userId,
    String? imagePath,
    Function(String stage, String detail)? onStageUpdate,
  }) async {
    try {
      // Stage callback helper
      void updateStage(String stage, String detail) {
        onStageUpdate?.call(stage, detail);
      }

      // D: Show 'Checking database...' Loading Message
      updateStage('checking_database', 'Checking database...');
      
      // G: Check Database for Existing Product
      final existingProduct = await _checkDatabaseForProduct(barcode);
      if (existingProduct != null) {
        // D4: Show 'Analyzing ingredients...' Loading Message
        updateStage('analyzing_ingredients', 'Analyzing ingredients...');
        
        // Skip to ingredient analysis for cached products
        final analyzedProduct = await _analyzeIngredients(existingProduct);
        return analyzedProduct;
      }

      // D2: Show 'Retrieving product details...' Loading Message
      updateStage('retrieving_details', 'Retrieving product details...');
      
      // H: Call Open Food Facts API
      Product? product = await _fetchFromOpenFoodFacts(barcode);
      
      if (product == null) {
        // D5: Show 'Analyzing product image...' Loading Message
        updateStage('analyzing_image', 'Analyzing product image...');
        
        // H4-H7: Try Gemini Visual Analysis
        if (imagePath != null) {
          product = await _analyzeWithGemini(barcode, imagePath);
        }
        
        if (product == null) {
          // D6: Show 'Searching online databases...' Loading Message
          updateStage('searching_online', 'Searching online databases...');
          
          // H8-H14: Try Web Scraping
          product = await _scrapeProductData(barcode);
        }
      }

      if (product != null) {
        // D3: Show 'Analyzing ingredients...' Loading Message
        updateStage('analyzing_ingredients', 'Analyzing ingredients...');
        
        // I-P: Analyze ingredients and create final product
        final analyzedProduct = await _analyzeIngredients(product);
        
        // N: Save to database
        await _saveProductToDatabase(analyzedProduct, userId);
        
        return analyzedProduct;
      }

      return null;
    } catch (e) {
      print('Enhanced analysis error: $e');
      return null;
    }
  }

  /// Execute parallel database check and API preparation (E1 in flowchart)
  Future<Map<String, dynamic>> _executeParallelDatabaseAndApiPrep(String barcode) async {
    try {
      final results = await Future.wait([
        _checkDatabaseForProduct(barcode),
        _prepareApiConnection(),
      ], eagerError: false);
      
      return {
        'existingProduct': results[0] as Product?,
        'apiPrepCompleted': results[1] as bool,
      };
    } catch (e) {
      LoggerService.error('[EnhancedSafeScan] Error in parallel operations: $e');
      return {
        'existingProduct': null,
        'apiPrepCompleted': false,
      };
    }
  }

  /// Check Database for Existing Product (G in Mermaid)
  static Future<Product?> _checkDatabaseForProduct(String barcode) async {
    try {
      if (!SupabaseInitService.isInitialized) {
        await SupabaseInitService.initialize();
      }

      final supabase = SupabaseInitService.client;
      
      // Use product_analysis_fullscan table instead of products
      final response = await supabase
          .from('product_analysis_fullscan')
          .select('*')
          .eq('barcode', barcode)
          .maybeSingle();

      if (response != null) {
        LoggerService.info('Found existing product in database for barcode: $barcode');
        return Product.fromDatabaseJson(response);
      }

      return null;
    } catch (e) {
      LoggerService.error('Error checking database for product: $e');
      return null;
    }
  }

  /// Prepare API Connection (G3 in Mermaid)
  Future<bool> _prepareApiConnection() async {
    try {
      // Pre-warm HTTP connections and validate API availability
      await Future.delayed(const Duration(milliseconds: 100)); // Simulate prep work
      return true;
    } catch (e) {
      LoggerService.error('[EnhancedSafeScan] Error preparing API connection: $e');
      return false;
    }
  }

  /// Call Open Food Facts API (H in Mermaid)
  Future<Map<String, dynamic>> _callOpenFoodFactsAPI(String barcode) async {
    try {
      LoggerService.info('[EnhancedSafeScan] Calling Open Food Facts API for barcode: $barcode');
      
      final response = await http.get(
        Uri.parse('https://world.openfoodfacts.org/api/v0/product/$barcode.json'),
        headers: {
          'User-Agent': 'SafeScan - Product Safety App',
        },
      ).timeout(const Duration(seconds: 10));
      
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['status'] == 1 && data['product'] != null) {
          LoggerService.info('[EnhancedSafeScan] Open Food Facts API success');
          return {
            'success': true,
            'product': data['product'],
          };
        }
      }
      
      LoggerService.info('[EnhancedSafeScan] Open Food Facts API failed or no data');
      return {'success': false};
      
    } catch (e) {
      LoggerService.error('[EnhancedSafeScan] Open Food Facts API error: $e');
      return {'success': false, 'error': e.toString()};
    }
  }

  /// Try Image Analysis (H4-H7 in Mermaid)
  Future<Map<String, dynamic>> _tryImageAnalysis(String imagePath) async {
    try {
      LoggerService.info('[EnhancedSafeScan] Analyzing product image');
      
      final imageResults = await ImageAnalysisService.analyzeProductImage(imagePath);
      
      if (imageResults != null && imageResults.isNotEmpty) {
        return {'success': true, 'data': imageResults};
      }
      
      return {'success': false};
    } catch (e) {
      LoggerService.error('[EnhancedSafeScan] Image analysis error: $e');
      return {'success': false, 'error': e.toString()};
    }
  }

  /// Try Web Scraping Approach (H8-H14 in Mermaid)
  Future<Map<String, dynamic>> _tryWebScraping(String barcode) async {
    try {
      LoggerService.info('[EnhancedSafeScan] Attempting web scraping for barcode: $barcode');
      
      final webResults = await WebScrapingService.searchProduct(barcode);
      
      if (webResults.isNotEmpty && webResults['confidence'] >= 0.7) {
        return {'success': true, 'data': webResults['data']};
      }
      
      return {'success': false};
    } catch (e) {
      LoggerService.error('[EnhancedSafeScan] Web scraping error: $e');
      return {'success': false, 'error': e.toString()};
    }
  }

  /// Proceed to ingredient analysis (I in Mermaid)
  Future<Product> _proceedToAnalysis({
    required String barcode,
    required String productName,
    required String brand,
    required List<String> ingredients,
    String? userId,
    required String requestId,
    Function(String stage, String detail)? onStageUpdate,
  }) async {
    // STAGE: Show "Analyzing ingredients..." loading message (D3 in flowchart)
    onStageUpdate?.call(
      AnalysisLoadingOverlay.stageAnalyzingIngredients,
      AnalysisLoadingOverlay.detailAnalyzingIngredients,
    );

    // Analyze Ingredient Coverage
    final ingredientAnalysis = await _analyzeIngredientCoverage(ingredients);
    
    // Generate Safety Report
    final safetyResult = await _generateSimpleSafetyReport(ingredients, ingredientAnalysis);
    
    // Create Final Product Object
    final product = _createProductObject(
      barcode: barcode,
      name: productName,
      brand: brand,
      ingredients: ingredients,
      safetyResult: safetyResult,
      userId: userId,
      requestId: requestId,
    );
    
    // Save Product to Database
    final savedProduct = await _saveProductToDatabase(product, userId);
    
    // Update Ingredients Database
    await _updateIngredientsDatabase(ingredientAnalysis);
    
    LoggerService.info('[EnhancedSafeScan] Enhanced analysis complete for: ${product.name}');
    return product;
  }

  /// Parse ingredients from text
  List<String> _parseIngredients(String ingredientsText) {
    if (ingredientsText.isEmpty) return [];
    
    return ingredientsText
        .split(RegExp(r'[,;]'))
        .map((ingredient) => ingredient.trim())
        .where((ingredient) => ingredient.isNotEmpty)
        .map((ingredient) => ingredient.toLowerCase())
        .toList();
  }

  /// Analyze Ingredient Coverage (I1 in Mermaid)
  Future<Map<String, dynamic>> _analyzeIngredientCoverage(List<String> ingredients) async {
    try {
      LoggerService.info('[EnhancedSafeScan] Analyzing ingredient coverage for ${ingredients.length} ingredients');
      
      final supabase = SupabaseInitService.client;
      
      // Check each ingredient against the database
      final matchedIngredients = <String>[];
      final unknownIngredients = <String>[];
      
      for (final ingredient in ingredients) {
        try {
          final response = await supabase
              .from('ingredients')
              .select('name, safety_level')
              .or('name.ilike.%$ingredient%,common_aliases.cs.{$ingredient}')
              .limit(1)
              .maybeSingle();
          
          if (response != null) {
            matchedIngredients.add(ingredient);
          } else {
            unknownIngredients.add(ingredient);
          }
        } catch (e) {
          LoggerService.error('[EnhancedSafeScan] Error checking ingredient $ingredient: $e');
          unknownIngredients.add(ingredient);
        }
      }
      
      final coveragePercentage = ingredients.isEmpty 
          ? 0.0 
          : (matchedIngredients.length / ingredients.length) * 100;
      
      LoggerService.info('[EnhancedSafeScan] Ingredient coverage: ${coveragePercentage.toStringAsFixed(1)}% (${matchedIngredients.length}/${ingredients.length})');
      
      return {
        'matched_ingredients': matchedIngredients,
        'unknown_ingredients': unknownIngredients,
        'coverage_percentage': coveragePercentage,
        'total_ingredients': ingredients.length,
        'matched_count': matchedIngredients.length,
      };
    } catch (e) {
      LoggerService.error('[EnhancedSafeScan] Error in ingredient coverage analysis: $e');
      return {
        'matched_ingredients': <String>[],
        'unknown_ingredients': ingredients,
        'coverage_percentage': 0.0,
        'total_ingredients': ingredients.length,
        'matched_count': 0,
      };
    }
  }

  /// Generate Simple Safety Report (K1/K2 in Mermaid)
  Future<Map<String, dynamic>> _generateSimpleSafetyReport(
    List<String> ingredients, 
    Map<String, dynamic> ingredientAnalysis,
  ) async {
    try {
      LoggerService.info('[EnhancedSafeScan] Generating safety report');
      
      final coveragePercentage = ingredientAnalysis['coverage_percentage'] as double;
      final matchedIngredients = List<String>.from(ingredientAnalysis['matched_ingredients'] ?? []);
      
      // Check for unsafe ingredients using safety criteria
      final concerningIngredients = <String>[];
      final allConcerns = <String>[];
      
      // Use SafetyCriteria.getDetailedAnalysis to get comprehensive safety information
      final ingredientNames = matchedIngredients.map((ing) => ing.toLowerCase()).toList();
      final safetyAnalysis = SafetyCriteria.getDetailedAnalysis(ingredientNames);
      
      // Extract concerning ingredients based on the detailed analysis
      if (safetyAnalysis['harmfulScore'] != null && safetyAnalysis['harmfulScore'] < 70) {
        // Add harmful ingredients to concerning list
        final harmfulIngredients = SafetyCriteria.harmfulIngredients;
        for (final ingredient in matchedIngredients) {
          if (harmfulIngredients.any((harmful) => ingredient.toLowerCase().contains(harmful))) {
            concerningIngredients.add(ingredient);
            allConcerns.add('Contains potentially harmful ingredient: $ingredient');
          }
        }
      }
      
      // Check for allergens
      if (safetyAnalysis['allergenScore'] != null && safetyAnalysis['allergenScore'] < 70) {
        final allergenicIngredients = SafetyCriteria.allergenicIngredients;
        for (final ingredient in matchedIngredients) {
          if (allergenicIngredients.any((allergen) => ingredient.toLowerCase().contains(allergen))) {
            if (!concerningIngredients.contains(ingredient)) {
              concerningIngredients.add(ingredient);
            }
            allConcerns.add('Contains potential allergen: $ingredient');
          }
        }
      }
      
      // Determine recommendation based on flowchart logic (I2, I3 in Mermaid)
      bool hasUnsafeIngredients = concerningIngredients.isNotEmpty;
      bool hasSufficientCoverage = coveragePercentage >= 50.0;
      
      final recommendation = hasUnsafeIngredients || !hasSufficientCoverage ? 'not_recommend' : 'good';
      final safetyScore = hasUnsafeIngredients ? 30 : (hasSufficientCoverage ? 85 : 60);
      
      LoggerService.info('[EnhancedSafeScan] Safety analysis complete: $recommendation (score: $safetyScore)');
      
      return {
        'recommendation': recommendation,
        'safety_score': safetyScore,
        'safety_rating': recommendation == 'good' ? 'good' : 'poor',
        'concerning_ingredients': concerningIngredients,
        'all_concerns': allConcerns.toSet().toList(),
        'coverage_percentage': coveragePercentage,
        'total_ingredients': ingredients.length,
        'analyzed_ingredients': matchedIngredients.length,
      };
      
    } catch (e) {
      LoggerService.error('[EnhancedSafeScan] Error generating safety report: $e');
      return {
        'recommendation': 'not_recommend',
        'safety_score': 30,
        'safety_rating': 'poor',
        'concerning_ingredients': ingredients,
        'all_concerns': ['Analysis failed - insufficient data'],
        'coverage_percentage': 0.0,
        'total_ingredients': ingredients.length,
        'analyzed_ingredients': 0,
      };
    }
  }

  /// Create Final Product Object (M in Mermaid)
  Product _createProductObject({
    required String barcode,
    required String name,
    required String brand,
    required List<String> ingredients,
    required Map<String, dynamic> safetyResult,
    String? userId,
    required String requestId,
  }) {
    final now = DateTime.now();
    
    return Product(
      id: _uuid.v4(),
      barcode: barcode,
      name: name,
      brand: brand.isNotEmpty ? brand : null,
      safetyScore: safetyResult['safety_score'] as int,
      safetyRating: safetyResult['safety_rating'] as String,
      recommendation: safetyResult['recommendation'] as String,
      concerningIngredients: null, // TODO: Convert strings to Ingredient objects
      allConcerns: List<String>.from(safetyResult['all_concerns'] ?? []),
      ingredientsJson: {
        'ingredients': ingredients,
        'coverage_percentage': safetyResult['coverage_percentage'],
        'analyzed_count': safetyResult['analyzed_ingredients'],
        'total_count': safetyResult['total_ingredients'],
      },
      scannedAt: now,
      createdAt: now,
      updatedAt: now,
      userId: userId,
    );
  }

  /// Save Product to Database (N, N1-N6 in Mermaid)
  static Future<void> _saveProductToDatabase(Product product, String? userId) async {
    try {
      if (!SupabaseInitService.isInitialized) {
        await SupabaseInitService.initialize();
      }

      final supabase = SupabaseInitService.client;
      
      // Save to product_analysis_fullscan table with correct schema
      final productData = {
        'barcode': product.barcode,
        'name': product.name.isNotEmpty ? product.name : 'Unknown Product',
        'safety_score': product.safetyScore ?? 50,
        'safety_rating': product.safetyRating ?? 'unknown',
        'brand': product.brand,
        'image': product.imageUrl,
        'user_id': userId,
        'scanned_at': DateTime.now().toIso8601String(),
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
        'concerns_count': product.concerningIngredients?.length ?? 0,
      };

      // Add JSON fields if available
      if (product.ingredients.isNotEmpty) {
        productData['ingredients_json'] = product.ingredients.map((i) => i.toJson()).toList();
      }

      if (product.alternatives.isNotEmpty) {
        productData['alternatives_json'] = product.alternatives.map((a) => a.toJson()).toList();
      }

      // Save product
      final savedProduct = await supabase
          .from('product_analysis_fullscan')
          .insert(productData)
          .select()
          .single();

      // Log scan result
      await _logScanResult(product.barcode, savedProduct['id'], product.name, userId);
      
      LoggerService.info('Product saved successfully: ${savedProduct['id']}');
    } catch (e) {
      LoggerService.error('Error saving product to database: $e');
    }
  }

  /// Save to Fallback Table (N5 in Mermaid)
  Future<void> _saveToFallbackTable(Product product) async {
    try {
      final supabase = SupabaseInitService.client;
      
      await supabase.from('product_analysis_fallback').insert({
        'barcode': product.barcode,
        'name': product.name,
        'brand': product.brand,
        'safety_score': product.safetyScore,
        'recommendation': product.recommendation,
        'analysis_data': product.ingredientsJson,
        'created_at': DateTime.now().toIso8601String(),
      });
    } catch (e) {
      LoggerService.error('[EnhancedSafeScan] Error saving to fallback table: $e');
      rethrow;
    }
  }

  /// Update Ingredients Database (O in Mermaid)
  Future<void> _updateIngredientsDatabase(Map<String, dynamic> ingredientAnalysis) async {
    try {
      final unknownIngredients = List<String>.from(ingredientAnalysis['unknown_ingredients'] ?? []);
      
      if (unknownIngredients.isNotEmpty) {
        LoggerService.info('[EnhancedSafeScan] Adding ${unknownIngredients.length} unknown ingredients to pending list');
        
        final supabase = SupabaseInitService.client;
        
        for (final ingredient in unknownIngredients) {
          try {
            await supabase.from('pending_ingredients').upsert({
              'name': ingredient,
              'count': 1,
              'status': 'pending',
              'created_at': DateTime.now().toIso8601String(),
            }, onConflict: 'name');
          } catch (e) {
            LoggerService.error('[EnhancedSafeScan] Error adding pending ingredient $ingredient: $e');
          }
        }
      }
    } catch (e) {
      LoggerService.error('[EnhancedSafeScan] Error updating ingredients database: $e');
    }
  }

  static Future<void> _logScanResult(String barcode, String productId, String productName, String? userId) async {
    try {
      final supabase = SupabaseInitService.client;
      
      await supabase.from('scan_results').insert({
        'product_id': productId,
        'barcode': barcode,
        'product_name': productName,
        'scan_date': DateTime.now().toIso8601String(),
        'user_id': userId,
        'confidence_score': 0.95,
        'source': 'mobile_app',
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      });
    } catch (e) {
      LoggerService.warning('Could not log scan result: $e');
    }
  }

  // Stub implementations for missing methods
  static Future<Product> _analyzeIngredients(Product product) async {
    // TODO: Implement ingredient analysis
    return product;
  }

  static Future<Product?> _fetchFromOpenFoodFacts(String barcode) async {
    // TODO: Implement OpenFoodFacts API integration
    return null;
  }

  static Future<Product?> _analyzeWithGemini(String barcode, String? imagePath) async {
    // TODO: Implement Gemini AI analysis
    return null;
  }

  static Future<Product?> _scrapeProductData(String barcode) async {
    // TODO: Implement web scraping
    return null;
  }
}
