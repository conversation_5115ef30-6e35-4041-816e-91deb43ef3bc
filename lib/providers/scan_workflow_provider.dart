import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/product.dart';
import '../services/new_scan_workflow_service.dart';
import '../services/logger_service.dart';

/// Workflow state for the new scan process
class ScanWorkflowState {
  final bool isScanning;
  final String currentStage;
  final String stageDetail;
  final Product? scannedProduct;
  final String? error;
  final double? progress;

  const ScanWorkflowState({
    this.isScanning = false,
    this.currentStage = 'idle',
    this.stageDetail = '',
    this.scannedProduct,
    this.error,
    this.progress,
  });

  ScanWorkflowState copyWith({
    bool? isScanning,
    String? currentStage,
    String? stageDetail,
    Product? scannedProduct,
    String? error,
    double? progress,
  }) {
    return ScanWorkflowState(
      isScanning: isScanning ?? this.isScanning,
      currentStage: currentStage ?? this.currentStage,
      stageDetail: stageDetail ?? this.stageDetail,
      scannedProduct: scannedProduct ?? this.scannedProduct,
      error: error ?? this.error,
      progress: progress ?? this.progress,
    );
  }

  bool get hasError => error != null;
  bool get isComplete => scannedProduct != null && !isScanning;
  bool get isIdle => !isScanning && scannedProduct == null && error == null;
}

/// Scan Workflow Controller implementing the new Mermaid workflow
class ScanWorkflowController extends StateNotifier<ScanWorkflowState> {
  ScanWorkflowController() : super(const ScanWorkflowState());

  /// Execute the complete scan workflow
  Future<void> executeWorkflow({
    required String barcode,
    String? userId,
    String? imagePath,
  }) async {
    try {
      // Reset state and start scanning
      state = state.copyWith(
        isScanning: true,
        currentStage: 'starting',
        stageDetail: 'Starting product analysis...',
        error: null,
        scannedProduct: null,
        progress: 0.0,
      );

      LoggerService.info('Starting scan workflow for barcode: $barcode');

      // Execute the workflow with stage tracking
      final product = await NewScanWorkflowService.executeWorkflow(
        barcode: barcode,
        userId: userId,
        imagePath: imagePath,
        onStageUpdate: _handleStageUpdate,
      );

      if (product != null) {
        // Workflow completed successfully
        state = state.copyWith(
          isScanning: false,
          currentStage: 'complete',
          stageDetail: 'Product analysis complete',
          scannedProduct: product,
          progress: 1.0,
        );
        LoggerService.info('Scan workflow completed successfully');
      } else {
        // Product not found
        state = state.copyWith(
          isScanning: false,
          currentStage: 'not_found',
          stageDetail: 'Product not found in database',
          error: 'Product not found in database',
          progress: 1.0,
        );
        LoggerService.warning('Product not found in database');
      }
    } catch (e, stackTrace) {
      // Workflow failed
      LoggerService.error('Scan workflow failed: $e', stackTrace: stackTrace);
      state = state.copyWith(
        isScanning: false,
        currentStage: 'error',
        stageDetail: 'Analysis failed',
        error: e.toString(),
        progress: 0.0,
      );
    }
  }

  /// Handle stage updates from the workflow service
  void _handleStageUpdate(String stage, String detail) {
    final progress = _calculateProgress(stage);
    
    state = state.copyWith(
      currentStage: stage,
      stageDetail: detail,
      progress: progress,
    );

    LoggerService.info('Workflow stage update: $stage - $detail');
  }

  /// Calculate progress based on workflow stage
  double _calculateProgress(String stage) {
    switch (stage) {
      case 'extracting_barcode':
        return 0.1;
      case 'checking_database':
        return 0.2;
      case 'retrieving_profile':
        return 0.9; // Fast path for existing products
      case 'querying_api':
        return 0.3;
      case 'extracting_details':
        return 0.4;
      case 'matching_ingredients':
        return 0.5;
      case 'processing_ingredients':
        return 0.6;
      case 'analyzing_ingredient':
        return 0.7;
      case 'analyzing_safety':
        return 0.8;
      case 'creating_profile':
        return 0.9;
      case 'saving_profile':
        return 0.95;
      case 'complete':
        return 1.0;
      default:
        return state.progress ?? 0.0;
    }
  }

  /// Reset the workflow state
  void reset() {
    state = const ScanWorkflowState();
  }

  /// Handle camera errors
  void handleCameraError(String error) {
    state = state.copyWith(
      isScanning: false,
      currentStage: 'camera_error',
      stageDetail: 'Camera initialization failed',
      error: error,
    );
  }

  /// Handle barcode detection errors
  void handleBarcodeDetectionError(String error) {
    state = state.copyWith(
      isScanning: false,
      currentStage: 'detection_error',
      stageDetail: 'Barcode detection failed',
      error: error,
    );
  }

  /// Handle API errors
  void handleAPIError(String error) {
    state = state.copyWith(
      isScanning: false,
      currentStage: 'api_error',
      stageDetail: 'API request failed',
      error: error,
    );
  }

  /// Handle safety analysis errors
  void handleSafetyAnalysisError(String error) {
    state = state.copyWith(
      isScanning: false,
      currentStage: 'analysis_error',
      stageDetail: 'Safety analysis failed',
      error: error,
    );
  }

  /// Handle database save errors (non-critical)
  void handleDatabaseSaveError(String error) {
    // Don't change scanning state for save errors
    state = state.copyWith(
      currentStage: 'save_error',
      stageDetail: 'Failed to save to history',
      // Keep the product since analysis was successful
    );
    LoggerService.warning('Database save error: $error');
  }

  /// Retry the current operation
  Future<void> retry({
    required String barcode,
    String? userId,
    String? imagePath,
  }) async {
    LoggerService.info('Retrying scan workflow');
    await executeWorkflow(
      barcode: barcode,
      userId: userId,
      imagePath: imagePath,
    );
  }

  /// Get user-friendly stage description
  String getStageDescription(String stage) {
    switch (stage) {
      case 'extracting_barcode':
        return 'Extracting barcode...';
      case 'checking_database':
        return 'Checking product database...';
      case 'retrieving_profile':
        return 'Loading product profile...';
      case 'querying_api':
        return 'Searching product databases...';
      case 'extracting_details':
        return 'Extracting product details...';
      case 'matching_ingredients':
        return 'Analyzing ingredients...';
      case 'processing_ingredients':
        return 'Processing ingredient safety...';
      case 'analyzing_ingredient':
        return 'Analyzing unknown ingredient...';
      case 'analyzing_safety':
        return 'Calculating safety score...';
      case 'creating_profile':
        return 'Creating product profile...';
      case 'saving_profile':
        return 'Saving to your history...';
      case 'complete':
        return 'Analysis complete!';
      case 'camera_error':
        return 'Camera error occurred';
      case 'detection_error':
        return 'Barcode detection failed';
      case 'api_error':
        return 'Network error occurred';
      case 'analysis_error':
        return 'Analysis failed';
      case 'not_found':
        return 'Product not found';
      default:
        return stage.replaceAll('_', ' ').toUpperCase();
    }
  }

  /// Check if the current stage is an error state
  bool isErrorStage(String stage) {
    return stage.contains('error') || stage == 'not_found';
  }

  /// Check if the workflow can be retried
  bool canRetry() {
    return hasError && !isScanning;
  }

  /// Get appropriate error type for error handling widgets
  String getErrorType() {
    switch (state.currentStage) {
      case 'camera_error':
        return 'camera_error';
      case 'detection_error':
        return 'barcode_detection_error';
      case 'api_error':
        return 'api_error';
      case 'analysis_error':
        return 'safety_analysis_error';
      case 'save_error':
        return 'database_error';
      default:
        return 'unknown_error';
    }
  }

  bool get hasError => state.hasError;
  bool get isScanning => state.isScanning;
  bool get isComplete => state.isComplete;
  bool get isIdle => state.isIdle;
}

/// Provider for the scan workflow controller
final scanWorkflowControllerProvider = StateNotifierProvider<ScanWorkflowController, ScanWorkflowState>((ref) {
  return ScanWorkflowController();
});

/// Provider for the current workflow stage description
final workflowStageDescriptionProvider = Provider<String>((ref) {
  final workflowState = ref.watch(scanWorkflowControllerProvider);
  final controller = ref.read(scanWorkflowControllerProvider.notifier);
  return controller.getStageDescription(workflowState.currentStage);
});

/// Provider for workflow progress percentage
final workflowProgressProvider = Provider<double>((ref) {
  final workflowState = ref.watch(scanWorkflowControllerProvider);
  return workflowState.progress ?? 0.0;
});

/// Provider for checking if workflow can be retried
final canRetryWorkflowProvider = Provider<bool>((ref) {
  final controller = ref.read(scanWorkflowControllerProvider.notifier);
  return controller.canRetry();
});

/// Provider for getting the error type
final workflowErrorTypeProvider = Provider<String>((ref) {
  final controller = ref.read(scanWorkflowControllerProvider.notifier);
  return controller.getErrorType();
});
