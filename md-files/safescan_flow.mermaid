flowchart TD
    A["User Taps Scan Product Button"] --> B["Open Scan View"]
    B --> C["Initialize Camera with Gemini AI"]
    C --> D["Auto-Detect Barcode using Gemini AI"]
    D --> E{"Barcode Detected?"}
    E -- No --> D
    E -- Yes --> F["Extract Barcode Value"]
    F --> G["Check Database for Existing Product Profile"]
    G --> H{"Product Profile Exists?"}
    H -- Yes --> I["Retrieve Existing Product Profile"]
    H -- No --> J["Query Open Food Facts API"]
    J --> K{"Open Food Facts Success?"}
    K -- No --> L["Show 'Product not found' Message"]
    K -- Yes --> M["Extract Product Details & Ingredients List"]
    M --> N["Match Ingredients with Supabase Ingredients Database"]
    N --> O["Process Each Ingredient"]
    O --> P{"Ingredient Found in Supabase DB?"}
    P -- Yes --> Q["Get Safety Rating from DB"]
    P -- No --> R["Send to Gemini AI for Safety Analysis"]
    R --> S["Gemini Analyzes Safety Based on DB Pattern"]
    S --> T["Get Safety Rating from Gemini"]
    T --> U["Store New Ingredient in Supabase DB"]
    Q --> V["Collect All Safety Ratings"]
    U --> V
    V --> W["Combine All Ingredients Safety Data"]
    W --> X["Analyze Overall Product Safety"]
    X --> Y{"Any RED (Unsafe) Ingredients?"}
    Y -- Yes --> Z["Mark Product as NOT RECOMMENDED"]
    Y -- No --> AA{"Only GREEN and YELLOW Ingredients?"}
    AA -- Yes --> BB["Mark Product as GOOD"]
    AA -- No --> Z
    Z --> CC["Create Product Profile with NOT RECOMMENDED"]
    BB --> DD["Create Product Profile with GOOD"]
    CC --> EE["Save Product Profile to Database"]
    DD --> EE
    EE --> FF["Display Detailed Product Info"]
    I --> FF
    FF --> GG["Show Product Safety Status"]
    GG --> HH{"Product Status?"}
    HH -- GOOD --> II["Display Green GOOD Badge"]
    HH -- NOT RECOMMENDED --> JJ["Display Red NOT RECOMMENDED Badge"]
    II --> KK["Show All Ingredients with Safety Colors"]
    JJ --> KK
    KK --> LL["Highlight Concerning Ingredients"]
    LL --> MM["Show Detailed Safety Information"]
    MM --> NN["Display Alternative Product Suggestions"]
    L --> OO["Allow Manual Product Entry"]
    OO --> PP["User Can Try Again"]
    PP --> A
    
    %% Error Handling
    C -- Camera Error --> ER1["Camera Initialization Failed"]
    D -- Gemini AI Error --> ER2["Barcode Detection Failed"]
    J -- API Error --> ER3["Open Food Facts API Failed"]
    R -- Gemini AI Error --> ER4["Safety Analysis Failed"]
    EE -- Database Error --> ER5["Profile Save Failed"]
    
    ER1 --> QQ["Show Error Message"]
    ER2 --> QQ
    ER3 --> L
    ER4 --> RR["Use Default Safety Rating"]
    ER5 --> SS["Continue Without Save"]
    
    QQ --> PP
    RR --> V
    SS --> FF
    
    %% Safety Color Legend
    subgraph Legend["Safety Rating Legend"]
        RED["🔴 RED: Unsafe - Zero Tolerance"]
        YELLOW["🟡 YELLOW: Use with Caution"]
        GREEN["🟢 GREEN: Safe"]
    end
    
    classDef userAction fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef systemProcess fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef decision fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef database fill:#e3f2fd,stroke:#0d47a1,stroke-width:2px
    classDef apiCall fill:#fff9c4,stroke:#f57f17,stroke-width:2px
    classDef goodResult fill:#c8e6c9,stroke:#2e7d32,stroke-width:2px
    classDef badResult fill:#ffcdd2,stroke:#c62828,stroke-width:2px
    classDef aiProcess fill:#e8eaf6,stroke:#3f51b5,stroke-width:2px
    classDef errorHandle fill:#ffebee,stroke:#d32f2f,stroke-width:2px
    classDef safetyRating fill:#f1f8e9,stroke:#388e3c,stroke-width:2px
    
    A:::userAction
    B:::systemProcess
    C:::aiProcess
    D:::aiProcess
    E:::decision
    F:::systemProcess
    G:::database
    H:::decision
    I:::database
    J:::apiCall
    K:::decision
    L:::systemProcess
    M:::systemProcess
    N:::systemProcess
    O:::systemProcess
    P:::decision
    Q:::safetyRating
    R:::aiProcess
    S:::aiProcess
    T:::safetyRating
    U:::database
    V:::systemProcess
    W:::systemProcess
    X:::systemProcess
    Y:::decision
    Z:::badResult
    AA:::decision
    BB:::goodResult
    CC:::systemProcess
    DD:::systemProcess
    EE:::database
    FF:::systemProcess
    GG:::systemProcess
    HH:::decision
    II:::goodResult
    JJ:::badResult
    KK:::systemProcess
    LL:::systemProcess
    MM:::systemProcess
    NN:::systemProcess
    OO:::userAction
    PP:::userAction
    ER1:::errorHandle
    ER2:::errorHandle
    ER3:::errorHandle
    ER4:::errorHandle
    ER5:::errorHandle
    QQ:::errorHandle
    RR:::systemProcess
    SS:::systemProcess
    RED:::badResult
    YELLOW:::safetyRating
    GREEN:::goodResult