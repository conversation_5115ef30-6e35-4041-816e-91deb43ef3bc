{"version": 2, "files": [{"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/model/animatable/animatable_text_frame.dart", "hash": "07c04ddd4a8c72d37ed2182a42af73ff"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/src/exception.dart", "hash": "e625c15c91736f7c029d22eaf13a9599"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_barcode-7.4.1/lib/src/spark/spark_scan_view_state.dart", "hash": "da50b84b973a47d171176e9662ff2c81"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/painting/beveled_rectangle_border.dart", "hash": "d8060c05b658b8065bc0bfdff6e4f229"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/data_table.dart", "hash": "752b2b12f0829a4d0abb699adad87062"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.17/lib/messages.g.dart", "hash": "3e127bbafbce223b6d416d5cca517df7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/LICENSE", "hash": "06d63878dac3459c0e43db2695de6807"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/parser/layer_parser.dart", "hash": "4eb2c3df549c8a74b3b50dd5a2047fb7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/forwarding.dart", "hash": "328ff975234df68963cb19db907493ff"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/foundation/_bitfield_io.dart", "hash": "0ae47d8943764c9c7d362c57d6227526"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/model/animatable/animatable_split_dimension_path_value.dart", "hash": "728b7b4869c9371b23792bee954930b8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/canonicalized_map.dart", "hash": "f5e7b04452b0066dff82aec6597afdc5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher-6.3.2/lib/src/legacy_api.dart", "hash": "197929b9f3eecdb738b1d3e31c7481b8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_barcode-7.4.1/lib/src/ar/barcode_ar_function_names.dart", "hash": "b6c13146a5e359d7bbf2fbc2bdeb1a98"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/cupertino/thumb_painter.dart", "hash": "e37bb4fabbf2e61e9b7fbe06f5770679"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web_socket-1.0.1/lib/src/io_web_socket.dart", "hash": "5b77b184a7c7fe214ed7b0e8dc221381"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3/lib/src/hive_impl.dart", "hash": "17d6409e5c71813bb1715f370eca420a"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/foundation/_platform_io.dart", "hash": "bf6d84f8802d83e64fe83477c83752b4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/parser/keyframes_parser.dart", "hash": "5bf30531bfd77e23cb369f1f9fb3b17c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/app_links_web-1.0.4/LICENSE", "hash": "d229da563da18fe5d58cd95a6467d584"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/future_group.dart", "hash": "fb71dd46672c822515f03f8f0dddbcb8"}, {"path": "/Users/<USER>/AndroidStudioProjects/Safescan/pubspec.yaml", "hash": "9be81a8ac62ab62f497de26c10d9e690"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/cache_store.dart", "hash": "b72b9cd4de477e80296c7f58bc9f5f30"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/src/utils.dart", "hash": "8986177ba204a808c603c35260601cce"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/local/local_file.dart", "hash": "3e30d0b7847f22c4b3674358052de8b5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3/lib/src/object/hive_list.dart", "hash": "be45023218a3803531ceb7521533bf9a"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/foundation/unicode.dart", "hash": "8b525140e1bf7268e1681a62c7640eea"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.0/lib/src/log_level.dart", "hash": "4c243a6ca83ee01bb17db0d0a77c681f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camera_android-0.10.10+4/lib/src/type_conversion.dart", "hash": "e335dd6b7affb42713c782d5a95bca48"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.0/lib/src/filters/development_filter.dart", "hash": "a925c024faf2d8bc047793e5a39b95d7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/src/types/camera_delegate.dart", "hash": "35512e89f2b31322744090b018902bab"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_barcode-7.4.1/lib/src/pick/ui/barcode_pick_view_highlight_style_response.dart", "hash": "323a1e091a9d1a358d15cf38083c3fd1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/lib/src/path_provider_windows_real.dart", "hash": "43f4676f21ce5a48daf4878201eb46bb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/gotrue-2.13.0/lib/src/types/api_version.dart", "hash": "2cae1ba8312f76513c3f85c1dc5455ba"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/snack_bar_theme.dart", "hash": "951bd729c13e8dd03a7f4edd8b10c06d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3/LICENSE", "hash": "4c5a88901110f96f096d0a05cc607301"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/painting/edge_insets.dart", "hash": "4349dd08c33e677b65d9e00f13c35d2e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_barcode-7.4.1/lib/src/ar/barcode_ar_annotation_trigger.dart", "hash": "9c5905af630625dc250dcc77bccdffcc"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/icon_data.dart", "hash": "eb9b3bf513b18ddaf0057f3877439d9b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/state_notifier-1.0.0/LICENSE", "hash": "1a3dbe8e080bf7ea1081e3c716ee17f1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cupertino_icons-1.0.8/LICENSE", "hash": "2d0c70561d7f1d35b4ccc7df9158beed"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/contrast/contrast.dart", "hash": "0c9bd1af5747fd55e7488c731ad32dee"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/lib/src/shared_preferences_foundation.dart", "hash": "db8ef5ac4d806e72f7b356056cb50b1f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/model/animatable/animatable_integer_value.dart", "hash": "8da956e63964f90b0ac81961162b6767"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/parser/repeat_parser.dart", "hash": "772dbd16ccdfc5dddb86b2fa6e2f36c3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/notifier.dart", "hash": "a67d1346ef152a92e983a9d7dc1a96fb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camera_platform_interface-2.10.0/lib/src/platform_interface/camera_platform.dart", "hash": "1e33b022ffb28fad925b0b945b528ec1"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/animated_cross_fade.dart", "hash": "98772211ffa69a8340f8088cd7193398"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/util/input_memory_stream.dart", "hash": "065a3cbd920a9379dae6b8659869637c"}, {"path": "/Users/<USER>/AndroidStudioProjects/Safescan/assets/animations/HomeScreenAnimation.json", "hash": "7f069e3eb6000611db49213963b76cc8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_platform_interface-2.6.2/lib/file_selector_platform_interface.dart", "hash": "eeb75628a0a17d5d8b5dbe0eafc08a29"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.20+2/lib/src/messages.g.dart", "hash": "cb72dc4eda2464d2afd8e3f5f5d2729e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/util/output_stream.dart", "hash": "7a177c5b215118520a084dddcaa026e2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/hct/cam16.dart", "hash": "ca959e5242b0f3616ee4b630b9866a51"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/cupertino/tab_view.dart", "hash": "8b15d222f5742b46bf55a4ef4cbfd6e0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_barcode-7.4.1/lib/src/selection/barcode_selection_strategy.dart", "hash": "d88972c5ab31b39d32b28f5ec304844e"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/overscroll_indicator.dart", "hash": "247fd4320e1e277acc190092bf6d35ae"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_android-6.3.16/lib/url_launcher_android.dart", "hash": "42d0000dd58d923eb70183595232c299"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/priority_queue.dart", "hash": "34a4d340931147322eaddc77fdc65c22"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/src/transaction.dart", "hash": "95701ee376845a2050d29814b7acc7a4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/supabase-2.8.0/lib/src/supabase_query_builder.dart", "hash": "dce9d006537150faffff807d4f732bc2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camera_android-0.10.10+4/lib/src/messages.g.dart", "hash": "f98ec25cd6e6fa03a08ca5da4709ac43"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/services/clipboard.dart", "hash": "61137458bbcab0dfb643d5d50a5ae80f"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/switch_list_tile.dart", "hash": "d942bc7ece253c7918e1f60d35e233b0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_linux-3.2.1/lib/url_launcher_linux.dart", "hash": "9d67bda83980287cc1100fe7fad9e05d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/value/lottie_frame_info.dart", "hash": "d47ef49aee4ac82ccf5300c2263c22cc"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/scroll_metrics.dart", "hash": "6f18c18a1a5649f27b6e0c29dfba4dc9"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/semantics_debugger.dart", "hash": "2c5021ff8faa0330f66b1c501e8d4b22"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/services/live_text.dart", "hash": "7da554c3a69a1c2d019202e3f63331c5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/client.dart", "hash": "b16458199371a46aeb93979e747962a3"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/cupertino/form_section.dart", "hash": "cd995d0f309bf74d0bbe94eb1e4e8e81"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/drag_boundary.dart", "hash": "1e0ea989110b1544dbaf1fdf3d9864cc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/state_notifier_provider.dart", "hash": "332fc1055d849f61ff8cb6ab6a919d1a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/stream_transform-2.1.1/lib/src/scan.dart", "hash": "de155f165219b10ba272bd030794873f"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/slider_value_indicator_shape.dart", "hash": "949350c1ca059ddb517d7f4f80b21ecd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/animation/content/ellipse_content.dart", "hash": "041f51ca26a79c2ad9744f5a9ec9b7ff"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/painting/decoration_image.dart", "hash": "dd510cd97dc23d22aebc7b60affd6329"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/codecs/zip_encoder.dart", "hash": "3f04e896c861aab2ca83e445e65b6d10"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/exception.dart", "hash": "9011b30a404dec657806a780b55d0610"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/do.dart", "hash": "96594345ee8d89e2fd23dbca09121153"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/app_links-6.4.0/lib/app_links.dart", "hash": "394eceb9119b27658a333988de13c1c6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_platform_interface-2.3.2/lib/link.dart", "hash": "c36f00a660d9aa87ebeab8672ccc6b32"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/animation/keyframe/base_keyframe_animation.dart", "hash": "5cc087a3e0221681126498a113d55c21"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/stream_transform-2.1.1/lib/stream_transform.dart", "hash": "2f811178fd6401d1399cf8b09cc1f9f4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/animation/content/fill_content.dart", "hash": "2652f0a42b8ebb2684ad822c4fff1ab0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/src/types/image_options.dart", "hash": "44005c1b9f4a2f37139637ce53b7bcc7"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/gestures/eager.dart", "hash": "07664903d8026f2514b29b786a27f318"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/widgets.dart", "hash": "946e37d543d3912bef54a551fb02ea1d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3/lib/src/util/indexable_skip_list.dart", "hash": "eda351b39b4854648a4d265ed1605fcc"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/painting/image_provider.dart", "hash": "25b96e83b1368abc11d4aeae19e9f398"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/grid_tile_bar.dart", "hash": "a340eddbf129cfd60e2c67db33c6003e"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/checkbox_theme.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.2/LICENSE", "hash": "901fb8012bd0bea60fea67092c26b918"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/codecs/zip/zip_directory.dart", "hash": "980c869bd642f4640bf26cf795553ea7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/common.dart", "hash": "493b51476fc266d10a636f520fff01fc"}, {"path": "/Users/<USER>/AndroidStudioProjects/Safescan/build/app/intermediates/flutter/debug/flutter_assets/NativeAssetsManifest.json", "hash": "f3a664e105b4f792c6c7fe4e4d22c398"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/rendering/view.dart", "hash": "15957b9d3eac4a2e1acaa24a3032afe7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/go_router-16.0.0/lib/src/match.dart", "hash": "4ed364af833bbef55b63c29077f61818"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_platform_interface-4.3.0/lib/src/method_channel/utils/codec.dart", "hash": "020552519d966b598cd3bb17849a3a49"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/supabase-2.8.0/lib/src/auth_http_client.dart", "hash": "eb44e23ea015640ac1f35f732df56dcc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/animation/keyframe/integer_keyframe_animation.dart", "hash": "ba97dbf45e8e88068578329b2f9b6d0f"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/services/_background_isolate_binary_messenger_io.dart", "hash": "991024814d51967a20be5851be93a8e3"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/rendering/sliver_multi_box_adaptor.dart", "hash": "********************************"}, {"path": "/Users/<USER>/AndroidStudioProjects/Safescan/lib/widgets/simple_barcode_scanner_widget.dart", "hash": "dcd2faed63bf14e1cdb84822b84445a4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.3/lib/src/messages.g.dart", "hash": "5b9ec782f9739612abc43813e94f2545"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/util/output_memory_stream.dart", "hash": "6b648a35d809f4d973f4100017b6f9ad"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/pragma.dart", "hash": "871c4029c43c6dcb8ac9ba8f7799d310"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme_tonal_spot.dart", "hash": "75f947f0ba87a0789a3ef91542bbc82c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/stream_notifier.dart", "hash": "e07baf43a89b4a1225ab8dab1161d2be"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/sliver_floating_header.dart", "hash": "5ffb77551727a0b5c646196e7bf1e9bc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher-6.3.2/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6/LICENSE", "hash": "7b710a7321d046e0da399b64da662c0b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/go_router-16.0.0/lib/src/delegate.dart", "hash": "087515340a18e957de353a2f6fa77893"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image_platform_interface-4.1.1/lib/cached_network_image_platform_interface.dart", "hash": "d9ccb5a0c8dcf64361a257c101d0e719"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/codecs/zlib/_huffman_table.dart", "hash": "d4f30e5344c82c8daa98c9d64564f7f7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/framework/always_alive.dart", "hash": "ae4469331dace367e6fb978dd7b7737e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_core-7.4.1/lib/src/scandit_flutter_datacapture_core.dart", "hash": "5644261378a0328b856f06b79d0a42b4"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/rendering/debug_overflow_indicator.dart", "hash": "deedcf7ee9b4e76191202e61654f9dcb"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/text_button.dart", "hash": "dbbc7f46620d816e615bbbe67eb258e7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/result/result.dart", "hash": "08e7cd384cfc0214e088945638139ce9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/lib/src/exception.dart", "hash": "5275d424aba5c931a30e6bd3e467027d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/postgrest-2.4.2/lib/src/constants.dart", "hash": "e1c203e95f1fb4c84e8aa93b6ced31d9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/model/key_path_element.dart", "hash": "a0121a0d8095ac333b2069cba3937461"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/state_notifier_provider/base.dart", "hash": "0ab8c6ae2a539e1eee8cc8e4e7cac2d1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/model/content/blur_effect.dart", "hash": "5a9d157b3f5b7e37fb54a9d5aa97fc89"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/painting/binding.dart", "hash": "530c4f96f1475cc4e4128ffedd705028"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/button_style_button.dart", "hash": "6a7d9ee6c8fae5e9548911da897c6925"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/automatic_keep_alive.dart", "hash": "8e870f9527626d34dc675b9e28edce85"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/utils/misc.dart", "hash": "f0acd9f6739e43dc39c97416eae806d6"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/divider_theme.dart", "hash": "04f538d5fc784c89c867253889767be4"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/foundation/annotations.dart", "hash": "b092b123c7d8046443429a9cd72baa9a"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/gestures/scale.dart", "hash": "abbe93b36782df11e43e348dadf52e94"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/mergeable_material.dart", "hash": "4201a655a36b0362d1b9f946b10b5e5e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_barcode-7.4.1/lib/src/count/barcode_count_view.dart", "hash": "54c93bf4e6a74c9d0b06986a86db37f5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/framework/selector.dart", "hash": "c771f26d18f9897af0e13e3a2c83d5ed"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.10/lib/shared_preferences_android.dart", "hash": "30bffdef523e68fbb858483fd4340392"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/frustum.dart", "hash": "d975e51852aa1802c81c738dcb4c348d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/state_notifier_provider/auto_dispose.dart", "hash": "d2e52f81da2329303a3f9d4b369c3320"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/lib/src/services_impl.dart", "hash": "a6d82f072fbaf76b1276861d20c1b788"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/utils/path_interpolator.dart", "hash": "d1dce8efcb8af20862254aa2f50ff252"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/ray.dart", "hash": "146741f6f87d6612ee7bbf6a6fa9c119"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/popup_menu.dart", "hash": "67d5620f72c33680625822432b60b613"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/union_set.dart", "hash": "0073f703be7f7ddbd7f04d1b740f35c6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camera_android-0.10.10+4/lib/camera_android.dart", "hash": "c7aee55d4bc520e0d56f6043eb7a48c6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/codecs/gzip_encoder.dart", "hash": "1dde9259c663a0918f98357af72267a4"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/desktop_text_selection_toolbar.dart", "hash": "04c960ae6d770135bb0b6acf14b134a4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/map_not_null.dart", "hash": "929b5628541e8ab826e753c9fe90cd30"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/spell_check_suggestions_toolbar_layout_delegate.dart", "hash": "3405e08e614528c3c17afc561d056964"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3/lib/src/binary/frame_helper.dart", "hash": "cb79a30b4326b1cbfb62680949394769"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_platform_interface-2.4.0/lib/src/sqflite_method_channel.dart", "hash": "2c294b86e9cf73bb732d8419ab47f434"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/rendering/sliver_padding.dart", "hash": "ddf1bde8f4b9706d5769690b7819e5d4"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/cupertino/localizations.dart", "hash": "a64e270c19c9e9ed0c5d9a17e0c4a5d0"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/unique_widget.dart", "hash": "11b4d96c7383b017773d65cb2843d887"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/span.dart", "hash": "b7c2cc8260bb9ff9a961390b92e93294"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/streamed_response.dart", "hash": "a004396fa64ff2163b438ad88d1003f4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/state_provider/auto_dispose.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/http.dart", "hash": "d9696ef3a9cefaa6bf238175fe214b0b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/model/marker.dart", "hash": "1b9d5e04fbc50afc449624692cc0d820"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/plugin_platform_interface-2.1.8/lib/plugin_platform_interface.dart", "hash": "8e49d86f5f9c801960f1d579ca210eab"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/lib/src/sql_builder.dart", "hash": "389352f8e1ecdf1332ad5bcb395bf9c1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.12.1/lib/src/lazy_chain.dart", "hash": "7ec268e37049e5c22e226c94df1776b3"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/tab_controller.dart", "hash": "40587a28640d3c90ad2e52fdfbcd7520"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/calendar_date_picker.dart", "hash": "224c14ef0447e287cbae1b7aed416290"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_html-0.1.3+5/LICENSE", "hash": "a086f9770acbfc6a5e421b49411d9415"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_core-7.4.1/lib/src/image_frame_source.dart", "hash": "8af7804a2933377e4e2594c1a9a5b368"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_core-7.4.1/lib/src/context_status.dart", "hash": "19ec03361be134deed43c0b8266edb1e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_web-2.4.3/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_barcode-7.4.1/lib/src/aruco_dictionary.dart", "hash": "6db2cd8648d339ecc1959b4e9aad01ea"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.20+2/lib/src/utils.dart", "hash": "2505bb0ae024bd81d50b29f3fa3d8642"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/rxdart.dart", "hash": "6d2dba952020d690bfc0aaff3adbcd65"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_barcode-7.4.1/lib/src/ar/barcode_ar_highlight.dart", "hash": "782b8cce644e8576f84f9c9f2c010ff8"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/rendering/sliver_grid.dart", "hash": "b61a261e42de1512c8a95fd52ef6540d"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/scrollbar.dart", "hash": "8e7a6f654b6ef374af586747a3ea912b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3/lib/src/box/default_compaction_strategy.dart", "hash": "32ef2d2128b50f494da6ea7571d1f7f4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_core-7.4.1/lib/src/common.dart", "hash": "a0d252f8be0851eb061de6089ad106e7"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/cupertino/date_picker.dart", "hash": "561522058c0ec0f631fe295300d190e6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/animation/content/content.dart", "hash": "ad88c57853f6b1639ff2dd232d1d54fa"}, {"path": "/Users/<USER>/AndroidStudioProjects/Safescan/build/app/intermediates/flutter/debug/flutter_assets/fonts/MaterialIcons-Regular.otf", "hash": "e7069dfd19b331be16bed984668fe080"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web_socket_channel-3.0.3/lib/web_socket_channel.dart", "hash": "77227643b5a655ab1fd24917c5a4675c"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/services/text_input.dart", "hash": "a4c1dffb16d559eb4d22bac89777780e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.0/lib/src/printers/logfmt_printer.dart", "hash": "1812a211ce0ad9a2385a310cea91bc01"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/codecs/bzip2_decoder.dart", "hash": "dc4cfb190342521ea2242f8d6414f2d5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/hash.dart", "hash": "4af79c5c69ccf0cae6ab710dfb84b125"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/src/compat.dart", "hash": "75e9e8da5881b6c2ebedc871d7bbc064"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/stream_channel-2.1.4/lib/stream_channel.dart", "hash": "b399357b285dbe1fc8e858ef3b8d20e1"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/tooltip_theme.dart", "hash": "8fac1e5cad9ef06d9e55e6559c06b990"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.0/lib/src/outputs/advanced_file_output.dart", "hash": "b485ef67fbe801f44114a023c4458b35"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/render_cache/store_drawing.dart", "hash": "6407884d11380a0043364406986589ba"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/services/restoration.dart", "hash": "79d4fba74eb854577c9589fb33994287"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/animation/content/rectangle_content.dart", "hash": "630f0f96c01788493c679f8838ebc642"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_barcode-7.4.1/lib/src/tracked_object.dart", "hash": "c28e1f7f30f158d61cf6172d595f8bcb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_macos-0.2.1+2/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_core-7.4.1/lib/src/frame_data.dart", "hash": "a4ca3fc48e416671347dcfbb76658771"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/codecs/zlib/zlib_encoder_web.dart", "hash": "bcd44ca46262b2c1f9c30b47b7279f74"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/delegate/event_sink.dart", "hash": "acfd72852e16d10d8797be366c796133"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/cupertino/nav_bar.dart", "hash": "8e471191ea3b6cdd6c970bf5be4cc86e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/stream_sink_extensions.dart", "hash": "3a2d505268f5446e5f7694776b69b407"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_linux-0.2.1+2/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/vector_math_64.dart", "hash": "bd1315cfa157d271f8a38242c2abd0d9"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/services/message_codec.dart", "hash": "bf50f61746b9744a0e2d45a88815288f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/lib/src/characters_impl.dart", "hash": "6297da5be01fb7c0d5c4aaffe7a27a50"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/src/types/types.dart", "hash": "3353f65796638e830b18ffdf1a678a3a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/parser/animatable_value_parser.dart", "hash": "1c9bf2f77575a9a21fd19a9441add4d8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/utils/forwarding_sink.dart", "hash": "89e6e01bd627dc1cc46b16ae027742dd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/quad.dart", "hash": "739bb2e85022ddfb653590b93216942a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/codecs/xz_decoder.dart", "hash": "9af7e9029f12f91f914a37459b228013"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/app_links_platform_interface-2.0.2/lib/app_links_method_channel.dart", "hash": "ad6cac082133f54612e91cc36888b468"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/ignore_elements.dart", "hash": "908b86c4378330e5b303026c8c3e29aa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3/lib/src/io/buffered_file_reader.dart", "hash": "4debbc32ca311b2ac8a215350cc72fd8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xdg_directories-1.1.0/lib/xdg_directories.dart", "hash": "737107f1a98a5ff745dd4e3236c5bb7b"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/services/scribe.dart", "hash": "d195153a8c01a0392b38e3b9adc672d8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/forwarding/forwarding_link.dart", "hash": "600a83d8e8dcbc1fde99887eea16f18e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/LICENSE", "hash": "80ea244b42d587d5f7f1f0333873ad34"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/painting/image_decoder.dart", "hash": "eaf5aa7cf4fe19db30724f637b38257a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/options.dart", "hash": "4f4b74de3e0a3af6ba800e2ad046e1c3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/from_callable.dart", "hash": "b05a68b737792aa52eaaa4d3e093bb63"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/animated_icons/data/pause_play.g.dart", "hash": "2ad27cdee5e6fe69626594543bd0e7c4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/src/sqflite_debug.dart", "hash": "a2cdec29e909752629150b24b9b18407"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/interface/directory.dart", "hash": "8f4de032f1e2670ca51ce330a4de91a3"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/scroll_notification_observer.dart", "hash": "a309d8ca64c3efb3ad74b742ffb0e1dd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/hmac.dart", "hash": "2b5fbc54f77ca9c1e5ac90eb3c242554"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/animation/tween_sequence.dart", "hash": "eabd3dc33b1a3a2966fa68f6efeb6bce"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_barcode-7.4.1/lib/src/spark/spark_scan_toast_settings.dart", "hash": "d8cf86661bd1d62de74c9084ff76ba5e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/using.dart", "hash": "f70fdb6ec3125a8d6f6fb5ea4cbac59a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/postgrest-2.4.2/LICENSE", "hash": "bb0a4b2e3d82de4116e8425de9a3927f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences-2.5.3/lib/src/shared_preferences_legacy.dart", "hash": "4144d8b8e1cae585ab9f01406b3e1f75"}, {"path": "/Users/<USER>/AndroidStudioProjects/Safescan/lib/screens/splash_screen.dart", "hash": "ce2e6ab27c13b2fabeb8bfe6051cb101"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/services/platform_views.dart", "hash": "1d3f3077faee6bebdc5279446f541502"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/raw_keyboard_listener.dart", "hash": "1f131d7f971396d52ce5fe78ae6a8a83"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_core-7.4.1/lib/src/font_family.dart", "hash": "0d426537a0de97e14deb8c72fc16dd18"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_barcode-7.4.1/lib/src/pick/ui/barcode_pick_view_highlight_style_custom_view_container.dart", "hash": "2c4ecb324a9bcb6f1f5a16694217104e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/jwt_decode-0.3.1/LICENSE", "hash": "f0c5f055cb4c3d651ff4c27f4967ecea"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/interface/error_codes_dart_io.dart", "hash": "9df03a340058a4e7792cd68745a4320c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_barcode-7.4.1/lib/src/count/barcode_count_capture_list_session.dart", "hash": "98411d46d3c10e9d4349543fe85bdab5"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/floating_action_button.dart", "hash": "55f7619e20765836d6d1c7001cb297fc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.3/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/services/raw_keyboard_macos.dart", "hash": "f7b9c7a2d1589badb0b796029090d0d5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/local/local_link.dart", "hash": "733eb3422250897324028933a5d23753"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/src/chunked_coding/decoder.dart", "hash": "e6069a6342a49cdb410fbccfbe4e8557"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/services/flutter_version.dart", "hash": "ad5b018b42f4cfaf02739e10a48c3ca3"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/semantics/binding.dart", "hash": "d5bcdae8bba4c191294311428a954783"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/cupertino/interface_level.dart", "hash": "1bdb47a9af4b0a5d759937da8ff04db0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme.dart", "hash": "a6adbe3868e017441360895c35fd6aa2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/md5.dart", "hash": "0981c95a357b5cebc932250a5e6c988e"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/gestures/multitap.dart", "hash": "578ff911d6e70b239fd629f5a0206fd8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/supabase-2.8.0/lib/src/supabase_stream_builder.dart", "hash": "a5a11dd802bfd3c28d7ce979590087a5"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/gestures/tap.dart", "hash": "2d638931b01747be8315be89cd473caa"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/painting/borders.dart", "hash": "5de15d7a41897996ef485c087ef4245b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/go_router-16.0.0/lib/src/parser.dart", "hash": "a54725bc16ee2ca993762c441542c1cc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/v4.dart", "hash": "916cd94d810ea5b86f0cdc685dc38001"}, {"path": "/Users/<USER>/AndroidStudioProjects/Safescan/lib/models/user.dart", "hash": "eda96886e91e975877b0c9bfea915543"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/animation/animation_controller.dart", "hash": "01aec7b419ee4a50145b3ccdd2a85fa0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/combined_wrappers/combined_iterable.dart", "hash": "67d16e841606c4e5355211fe15a2dbfd"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/cupertino/list_section.dart", "hash": "1363e5e6d5efab4bae027262eff73765"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/services/undo_manager.dart", "hash": "0821fcdff89c96a505e2d37cf1b52686"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/gotrue-2.13.0/lib/src/types/error_code.dart", "hash": "68b1075f1c2ef93c4a154fb6e009d769"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher-6.3.2/lib/src/url_launcher_string.dart", "hash": "27e6c510107a34001ef90f889281633e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.4/lib/src/utf8.dart", "hash": "329d62f7bbbfaf993dea464039ae886c"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/cupertino/app.dart", "hash": "aae059b82ff751f6e81487ef98668661"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/realtime_client-2.5.1/lib/src/realtime_presence.dart", "hash": "7027327417bb0c4dbbb3d77105e8f267"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/location.dart", "hash": "fb2c02d4f540edce4651227e18a35d19"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/autocomplete.dart", "hash": "4e8a70d478371e0d995f080a6eaa8120"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/lib/src/grapheme_clusters/breaks.dart", "hash": "73189b511058625710f6e09c425c4278"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/memory/utils.dart", "hash": "7a6fe2bde5e3bf653cd473308d8402c5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_platform_interface-4.3.0/lib/src/service_status.dart", "hash": "5072fb1450640d8f46605ff67dafa147"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/model/content/repeater.dart", "hash": "27d396f466672ee540d5eb147026eed3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_barcode-7.4.1/lib/src/symbology.dart", "hash": "30e4d75c9d7641d39b98d44eb20d748b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_barcode-7.4.1/lib/src/spark/spark_scan_view_ui_listener.dart", "hash": "3ae73f46b0cea9ea0766a418f9dad7a3"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/drawer_header.dart", "hash": "f996ce49eab57718350b84e11ea3192d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/model/animatable/animatable_double_value.dart", "hash": "1dc9a65d840b855114017cebedafa4ab"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/stack_trace.dart", "hash": "bd15738d49bec303fe3d234de40503d8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.17/lib/path_provider_android.dart", "hash": "eb368258f0f9fe56110bdc238488af97"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/foundation/timeline.dart", "hash": "2fbba4502156d66db0a739144ccce9a0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/utils/pair.dart", "hash": "cce1919f0a4ac22dcbcf98d1489994f7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.28/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/routes.dart", "hash": "33adcae8de663e2e8f8f410da7fc8023"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/providers/asset_provider.dart", "hash": "f322e7745030f2a8ae5f650453987e1b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.10/lib/src/messages_async.g.dart", "hash": "2bd174cad1b04e4cca9ba7ac37905e5d"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/gestures/monodrag.dart", "hash": "8807672a31b470f53c5fcc2b36dcf509"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/model/animatable/animatable_color_value.dart", "hash": "95ea6bad0f8a8f4e5266c5f80ef873ab"}, {"path": "/Users/<USER>/AndroidStudioProjects/Safescan/.dart_tool/package_config_subset", "hash": "da1cded675884619c09bb2b09da118ee"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/interface/file_system.dart", "hash": "3120b9b427a566f796573ee37167c026"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/v5.dart", "hash": "cc8112e5daca3ae7caf3bd7beda5f39e"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/ink_ripple.dart", "hash": "81fd3ef494f4443fb8565c98ba5a9ba2"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/animated_icons/data/menu_arrow.g.dart", "hash": "b1bb8356cca8b86afca314ab4898a527"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6/lib/platform.dart", "hash": "cbf041463d4a85115a79934eafe8e461"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/menu_button_theme.dart", "hash": "e461dc9f79fcf6a9e4faf12c8182fb47"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_barcode-7.4.1/lib/src/ar/barcode_ar_defaults.dart", "hash": "df72b738c55be62c5586447927a7aa19"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/default_text_editing_shortcuts.dart", "hash": "9a31689295b300aa8ab12d29fb8853ff"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/app_links_platform_interface-2.0.2/LICENSE", "hash": "d229da563da18fe5d58cd95a6467d584"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/model/content/shape_group.dart", "hash": "da87c53da0b86c491d42d7badb0f2131"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/range.dart", "hash": "57de88edbfb0d8951419051441a6de56"}, {"path": "/Users/<USER>/AndroidStudioProjects/Safescan/build/app/intermediates/flutter/debug/flutter_assets/FontManifest.json", "hash": "1b1e7812d9eb9f666db8444d7dde1b20"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/bottom_navigation_bar.dart", "hash": "ccb3c80f13485133893f760c837c8b62"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_barcode-7.4.1/lib/src/symbology_settings.dart", "hash": "b382a901f752f31da38052c6609930df"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/ink_decoration.dart", "hash": "a2ab6e0f334e5a28af29766b82f7f4b0"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/services/text_layout_metrics.dart", "hash": "13be7153ef162d162d922f19eb99f341"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/services/mouse_cursor.dart", "hash": "b0c6844b0af0cd0539060a0bfcbe3713"}, {"path": "/Users/<USER>/AndroidStudioProjects/Safescan/build/app/intermediates/flutter/debug/flutter_assets/assets/animations/HomeScreenAnimation.json", "hash": "7f069e3eb6000611db49213963b76cc8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/lib/image_picker_ios.dart", "hash": "75290287531fff47b4eab2f25f050d57"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/supabase-2.8.0/lib/src/remove_subscription_result.dart", "hash": "efbb4f3ad0fa54d1921883f834b8e702"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/memory/memory_file_stat.dart", "hash": "1b430815bdc7bab3a240f27e745f8977"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/foundation/collections.dart", "hash": "f209fe925dbbe18566facbfe882fdcb0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/sphere.dart", "hash": "63473e31f03ea66a38affa41fd783752"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/src/utils.dart", "hash": "9d122acee9d1f43dcdb2ea88fd1fc95f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_barcode-7.4.1/lib/src/spark/spark_scan_view_capture_mode.dart", "hash": "7b07c839dde5ee4d79565e00ccc93800"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/desktop_text_selection_toolbar_button.dart", "hash": "a46ede2164234d7371852e8f57865dd0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/interface/link.dart", "hash": "1f334b50f4df781bbbfab857581c3540"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/stream_transform-2.1.1/lib/src/where.dart", "hash": "a8ed3dae38fb7fa7baacfa77ac9bd53c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/codecs/zlib/_gzip_decoder.dart", "hash": "f00a5b112709bef9fe0703b0fc91e2d1"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/magnifier.dart", "hash": "52d0e96cbfe8e9c66aa40999df84bfa9"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/cupertino/dialog.dart", "hash": "fdf500742b45dff0abb3db9cbd350fd4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/supabase-2.8.0/lib/src/auth_user.dart", "hash": "efc781329a215c52807919ea30f84df9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/io_client.dart", "hash": "e4823f5eb1dffcf1cf47a9d667c5cb18"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/app_links-6.4.0/lib/src/app_links.dart", "hash": "a25040d3606a48b2d3c6eb6d638761a5"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/banner.dart", "hash": "c9cd996cea2334f644c74ebbdb41f7f5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/framework/container.dart", "hash": "8597f18181783d905e40dc64f0c0555a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3/lib/src/binary/binary_writer_impl.dart", "hash": "7f3d8ecd3382ba1196fa6ede8b4c8fe8"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/scaffold.dart", "hash": "498db9e29a08e6fdc8aee5eeb4d204ce"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/parser/integer_parser.dart", "hash": "4c1da0d429a56461cf9aed6a7435eb8a"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/debug.dart", "hash": "0575a78fbb39a292302737868752da77"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/constants.dart", "hash": "2c6facdb1b63e687304c4b2852f6ef4c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/parser/shape_stroke_parser.dart", "hash": "76ebfe3372d5981f7f37a3be6e7a0b6a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/lib/src/folders.dart", "hash": "4bd805daf5d0a52cb80a5ff67f37d1fd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler-11.4.0/lib/permission_handler.dart", "hash": "ae9b498a0c3fd784a628e57eb92307aa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image-3.4.1/lib/cached_network_image.dart", "hash": "dd2d618db009ed3aa82488ca3b0e1261"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/utils/subscription.dart", "hash": "8ab19033cc6a918c1e4f454495a9ab5f"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/tab_bar_theme.dart", "hash": "a91b4b0d0d10b955e8973126cf288ea4"}, {"path": "/Users/<USER>/AndroidStudioProjects/Safescan/assets/fonts/SpaceMono-Regular.ttf", "hash": "49a79d66bdea2debf1832bf4d7aca127"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/mime-2.0.0/lib/src/default_extension_map.dart", "hash": "fe2df60ed5b05e922df2ee9fef5cf5d9"}, {"path": "/Users/<USER>/AndroidStudioProjects/Safescan/build/app/intermediates/flutter/debug/flutter_assets/assets/animations/HomeScreen-2.json", "hash": "34a147ad9a0287b867e3dc05ab6dd517"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/equality_map.dart", "hash": "700328ab0177ddfd9a003a8c15619c1a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/parser/gradient_color_parser.dart", "hash": "a8edf294139f9276e92d88caf7f9fed7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/delay_when.dart", "hash": "064ceadd31d0b29fc59188b2c4d45db1"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/services.dart", "hash": "0330f85971391a5f5457a20e933fe264"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/backpressure/debounce.dart", "hash": "1aea282ab07e82afe7a564125110e1fc"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/rendering/layer.dart", "hash": "659b88645890c6437ea5ce4928e8871e"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/text_selection_toolbar.dart", "hash": "2553e163ea84c7207282c18b5d9e14c1"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/progress_indicator_theme.dart", "hash": "377731ed35ad8d1d36dcfd532a3d308e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_barcode-7.4.1/lib/src/batch/barcode_batch_basic_overlay.dart", "hash": "9f9ff5ff5589ae204f6706bc2f951760"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/keyboard_listener.dart", "hash": "bd3f0349089d88d3cd79ffed23e9163b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/realtime_client-2.5.1/lib/src/realtime_channel.dart", "hash": "9140902d521b0b83d4f91d6b69041f80"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/lib/src/sqflite_plugin.dart", "hash": "ae3622db94fb8368f3577f6e71f3ea4f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_linux-0.9.3+2/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/parser/moshi/json_reader.dart", "hash": "6f48f9d6262e217c99b09aa2952b753f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/value_stream.dart", "hash": "4ec7181b3b281703a8fddee43b540ee6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_platform_interface-4.3.0/lib/src/permission_handler_platform_interface.dart", "hash": "d3e01a9f299b192bb14b18062c49e562"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/wrappers.dart", "hash": "21e56afda1f096f0425a34987708ed56"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/input_decorator.dart", "hash": "952fb243dbdb00bfe11b0293238b115d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/base_response.dart", "hash": "358495c0e2a26e0203cd810f7ca85795"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/flat_map.dart", "hash": "115640739fe47a728c4b1c3a4b4c3506"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camera_platform_interface-2.10.0/lib/src/types/image_file_format.dart", "hash": "d9a59eac914fdd0b3d23e861817c338e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/lottie_builder.dart", "hash": "3fbe92fb47b22727ef73eeb8b9f8e2fc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.10/lib/src/strings.dart", "hash": "4e96c754178f24bd4f6b2c16e77b3a21"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/animation/keyframe/color_keyframe_animation.dart", "hash": "0fed4fee7907476b42caec3b3d6ac96b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/storage/cache_object.dart", "hash": "470452529b3925fdb9a5865578558e83"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/path_map.dart", "hash": "9d273d5a3c1851b0313cd949e7f84355"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/LICENSE", "hash": "7cd08032583ab0a8eca895b2365a4583"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/retry.dart", "hash": "66c3d8022ecd26ac3b2f30fe28e4c475"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_barcode-7.4.1/lib/scandit_flutter_datacapture_barcode_selection.dart", "hash": "415e74ca3714c0069a1545921b2497b4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/jwt_decode-0.3.1/lib/jwt_decode.dart", "hash": "1d1f26cc381fb61a757dfd1095055fc7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/framework/listen.dart", "hash": "4990e198f887619ece65c59a3de67869"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/lib/src/path_provider_linux.dart", "hash": "8ac537f4af05ad812e8cd29f077aee24"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/storage/cache_info_repositories/cache_info_repositories.dart", "hash": "8cf88d8eac6c9b46a6fb3877f9fc35d2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logging-1.3.0/lib/logging.dart", "hash": "60fd6d17602ae0c1d18e791d6b1b79cf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/zip.dart", "hash": "636229be247a1ecd50a669eb2dc73206"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/interface/error_codes.dart", "hash": "3e82e75a5b4bf22939d1937d2195a16e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sprintf-7.0.0/lib/sprintf.dart", "hash": "9c00cbf52bb0297fccad0b5c5b54d4e7"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/undo_history.dart", "hash": "73089c9737db54a05691e09bc9fc1bcd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camera_platform_interface-2.10.0/lib/src/types/types.dart", "hash": "3da7325abd4e1b53a6419f92dc270757"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/value/keyframe.dart", "hash": "1b050cfa02a3c3112c8e76d5f03fa3d5"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/safe_area.dart", "hash": "7088cc45b21c93be6b42dc748fc3a29a"}, {"path": "/Users/<USER>/AndroidStudioProjects/Safescan/.dart_tool/flutter_build/24601892b32eefa3e669ab6ab2e21a45/native_assets.json", "hash": "f3a664e105b4f792c6c7fe4e4d22c398"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/gestures/gesture_settings.dart", "hash": "b5bd9d15c10929b4a63ea0df649e2d52"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/gotrue-2.13.0/lib/src/types/auth_exception.dart", "hash": "f8a9bb529be5b8acad3ba73ed5a28d48"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3/lib/src/object/hive_object_internal.dart", "hash": "1d6b06c440ce770d590ccc694f67e7de"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/src/types/multi_image_picker_options.dart", "hash": "5ad1b4844df9d51e4c957f292d696471"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/gotrue-2.13.0/lib/src/helper.dart", "hash": "acdda7b4ad22613fa353e52cfec026f6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_barcode-7.4.1/lib/src/spark/spark_scan_feedback_delegate.dart", "hash": "609cef8e4e6a260eb60536299a5dfbbd"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/gestures/lsq_solver.dart", "hash": "d0ab7f5e11e48788c09b0d28a0376d80"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vm_service-15.0.0/LICENSE", "hash": "5bd4f0c87c75d94b51576389aeaef297"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.0/lib/src/logger.dart", "hash": "0abc184f4138b805c17d7e37d675520a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cupertino_icons-1.0.8/assets/CupertinoIcons.ttf", "hash": "b93248a553f9e8bc17f1065929d5934b"}, {"path": "/Users/<USER>/AndroidStudioProjects/Safescan/lib/constants/app_theme.dart", "hash": "b9be716c933a69e8841d5859d2dedec1"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/card_theme.dart", "hash": "5d8e29422039d9dcce6908b427814d80"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_macos-0.9.4+3/lib/src/messages.g.dart", "hash": "3ff09a7edec90fdf07e59bc3514ea474"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/delegate/future.dart", "hash": "443fe4357544b85c13ef051cf37a602f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/supabase-2.8.0/lib/src/supabase_client_options.dart", "hash": "689983e57a716d8f40a51b9476e497d7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image_web-1.3.1/LICENSE", "hash": "6e15c47981e2e43ee17849a222e33b76"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_barcode-7.4.1/lib/src/selection/barcode_selection_function_names.dart", "hash": "848d2205ce19e64f80e3b9ae6290d644"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/icon_button.dart", "hash": "5d99a505ddc69d5accc4e5a83f5cfa4d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.12.1/lib/src/trace.dart", "hash": "dcb1bf21d8afb364e20a47f106496780"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3/lib/src/object/hive_object.dart", "hash": "08b848f81523e9f11afbad3153f6dac8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/provider.dart", "hash": "d5b1d01f918c452585a990bba4c2b919"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/autofill.dart", "hash": "3623c605586d2e37af23d6b746721bd7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/parser/rounded_corners_parser.dart", "hash": "7961b4ec8abcaa3c18fa3a389bacedaa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/location_mixin.dart", "hash": "6326660aedecbaed7a342070ba74de13"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_linux-2.4.1/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/retry-3.1.2/lib/retry.dart", "hash": "c1170f540fa3fb08890fb4abea0f4d82"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/services/service_extensions.dart", "hash": "eb115c2e8f0ff170bf26a44efd1b5c05"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/scroll_notification.dart", "hash": "269af8ca7030ccfd9c868fe9af8a6b0a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/framework/element.dart", "hash": "d414c1f995780a939e1d357efa0400a1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/boolean_selector-2.1.2/LICENSE", "hash": "83228a1ae32476770262d4ff2ac6f984"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/stream_transform-2.1.1/lib/src/rate_limit.dart", "hash": "8fed4025dd6b2e77558d840be04059f2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_barcode-7.4.1/lib/src/selection/barcode_selection.dart", "hash": "d03f6b003644f310c38f85615097e10b"}, {"path": "/Users/<USER>/AndroidStudioProjects/Safescan/lib/services/supabase_init_service.dart", "hash": "88505e7cc44b1a7d15a4f6245cc2d1ef"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/supabase-2.8.0/lib/src/supabase_event_types.dart", "hash": "72375a3736da7b6d2c22a78830ea9b26"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_barcode-7.4.1/lib/src/pick/barcode_pick_defaults.dart", "hash": "b8946340d232c1377ba3d5032679f9f1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/LICENSE", "hash": "86d3f3a95c324c9479bd8986968f4327"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/style/windows.dart", "hash": "0d86d4ba2e01e5e62f80fcf3e872f561"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/primary_scroll_controller.dart", "hash": "58707cf455f97f907192b4ff92d36711"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/router.dart", "hash": "a89f6417642d57961ee87743be4a6a2b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/src/http_date.dart", "hash": "fb76e9ed5173ac1ae6a6f43288581808"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/src/media_type.dart", "hash": "101ff6d49da9d3040faf0722153efee7"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/bottom_sheet.dart", "hash": "c442be28b905f64b74f6e9f8e5903820"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/async_notifier/base.dart", "hash": "737fc999d5d26218c34c7423fe061f1e"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/autocomplete.dart", "hash": "aff0bd5981a82f881b4ac72a321ee9c6"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/system_context_menu.dart", "hash": "5666a74f3f21ee2fa9b0b2aa37360700"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme_vibrant.dart", "hash": "5b04f80518a8417cb87a0aec07dacf4f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/parser/blur_effect_parser.dart", "hash": "a84a7175d396b8e0a51f360162527052"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/cupertino/radio.dart", "hash": "9802442b82d3be84abecae8d0a2c7bd6"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/services/keyboard_inserted_content.dart", "hash": "5da306e7f2542e5fb61efff6b4824912"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/gestures/multidrag.dart", "hash": "f56109c40e6fe9e53f9c6ad021d25ff5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/test_api-0.7.4/LICENSE", "hash": "3323850953be5c35d320c2035aad1a87"}, {"path": "/Users/<USER>/AndroidStudioProjects/Safescan/build/app/intermediates/flutter/debug/flutter_assets/packages/material_design_icons_flutter/lib/fonts/materialdesignicons-webfont.ttf", "hash": "4192c96835815b160f03a005cabed284"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/foundation/serialization.dart", "hash": "f20071b459b9bbb98083efedeaf02777"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/quantize/src/point_provider_lab.dart", "hash": "6566a35ff0dea9376debf257bdb08fba"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_barcode-7.4.1/lib/scandit_flutter_datacapture_barcode_batch.dart", "hash": "93d11aefea7dd39c5278e37df788b1fc"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/divider.dart", "hash": "6189af9ddf633811ffb6414cb9d3f744"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/lib/src/enums.dart", "hash": "f4b67c136a2189470329fd33ebe57cb3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/provider-6.1.5/LICENSE", "hash": "753206f0b81e6116b384683823069537"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/lib/src/eager_span_scanner.dart", "hash": "bdc22e9e77382045196b5aafd42b5e55"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/physics/spring_simulation.dart", "hash": "2458910beb2b4f3b177a7db027cf7d34"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/animation/tween.dart", "hash": "73f043194b9c158454e55b3cafbdb395"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/lib/sqflite_darwin.dart", "hash": "b1cb91ea7a56d612d5792dbfe439f2d8"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/app.dart", "hash": "ca378f8a4dc93cea9ab759f410dcfdb6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker-10.0.9/LICENSE", "hash": "f721b495d225cd93026aaeb2f6e41bcc"}, {"path": "/Users/<USER>/AndroidStudioProjects/Safescan/build/app/intermediates/flutter/debug/flutter_assets/assets/animations/Data-not-found.json", "hash": "1a06f1ad989fe14d258d811468ae2d5e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web_socket_channel-3.0.3/LICENSE", "hash": "e539018b40753112ede3ab43f1ee9052"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/postgrest-2.4.2/lib/src/response_postgrest_builder.dart", "hash": "e9d8335614b00b20658c73e6c981e073"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/obb3.dart", "hash": "5ca0b5786bf63efd4fc72fcecfe1b36c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_barcode-7.4.1/lib/src/symbology_description.dart", "hash": "a52ee057e71900adee1f24727c3cbdb8"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/curves.dart", "hash": "4aeb4635d84df42e6f220aba366af7d9"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/gestures/drag_details.dart", "hash": "f350db07fdddbcfd71c7972bf3d13488"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/colors.dart", "hash": "c517fb54b3d66b22988ad7c8d07c6f53"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/nested_scroll_view.dart", "hash": "d3b40ca9660164ac83b714d6e2df3843"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/progress_indicator.dart", "hash": "4f3e0e3af33c5bdfbf1d32adeba91652"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/rendering/custom_layout.dart", "hash": "dc552952c58db02409090792aeebbdd8"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/foundation/stack_frame.dart", "hash": "eb89408ce23b2abcd324ea5afb05a1ea"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/model/animatable/animatable_text_properties.dart", "hash": "062f6dcdbe2fa453f9e9a77ac739fb52"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/stream_notifier/base.dart", "hash": "62e6826075b4df67271133a79fc4d046"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/v1.dart", "hash": "a22d810ba989505f23b6be0562a04911"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/gestures/recognizer.dart", "hash": "990244fbee5d6f551e98a4bcce092389"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.2/lib/src/utils.dart", "hash": "e85b4f3cf370581b3ef11497a9a5bce3"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/page_view.dart", "hash": "7150d31ecb453ea0d7516ebd2a56ff84"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/painting/continuous_rectangle_border.dart", "hash": "93d025adfc0409629c51036cb0fdc085"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/colors.dart", "hash": "5ed8acdae7dd3501b64b0ff3e33c1f45"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.10/lib/src/shared_preferences_async_android.dart", "hash": "5cfe2d9d61584eae2e9c8e81be1dd9c5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/switch_map.dart", "hash": "56bb06e8c5f7f7892ae9eb352dd91f9e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/framework/auto_dispose.dart", "hash": "ef220252cc1911073575cfbf66f4c8d1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme_fidelity.dart", "hash": "553c5e7dc9700c1fa053cd78c1dcd60a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/src/sqflite_database_factory.dart", "hash": "b2b96fda3b5d147408ecb71c2bbe73a7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/stream_queue.dart", "hash": "cf0f2c674cec774d8fc0990ee818316f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/lib/path_provider_linux.dart", "hash": "b48ba72a2d5d084d297c3d78e351036e"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/services/deferred_component.dart", "hash": "53b9028402187f878713225b48bdd5bb"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/badge.dart", "hash": "cd7cadd0efa83f26d401a14e53964fd4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_barcode-7.4.1/lib/src/capture/barcode_capture_overlay.dart", "hash": "eb791af180bbe719ef8c8efedf42887b"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/table.dart", "hash": "9b98b196040f00fd2fbaf5f7a2309e6b"}, {"path": "/Users/<USER>/AndroidStudioProjects/Safescan/assets/images/splash-icon.png", "hash": "81a18fd6e285e18c4c1c21e67c088a90"}, {"path": "/Users/<USER>/AndroidStudioProjects/Safescan/lib/models/alternative.dart", "hash": "0b7f3158edfdfc3637f1de9b5b617774"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/performance_overlay.dart", "hash": "c5e44030289c2c25b26c5b3aa843b3cc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_android-2.4.1/lib/sqflite_android.dart", "hash": "3d09396dae741c535c293314adc09565"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3/lib/src/crypto/crc32.dart", "hash": "21913fbf147ca790e444082cf32a7c84"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/lib/shared_preferences_foundation.dart", "hash": "b72ebe27944e3a75601e56579bb92907"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/sha512.dart", "hash": "e4973bdb8ceac8b88cdefee5f56f0fa0"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/foundation/object.dart", "hash": "daa0c9b859ed1959e6085188a703f387"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/drag_target.dart", "hash": "********************************"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/rendering/sliver_group.dart", "hash": "630fe5f86ee37699c534f9c91f21f03c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/src/path_utils.dart", "hash": "e335de991d295627ccaabe152db13f68"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.12.1/lib/src/stack_zone_specification.dart", "hash": "d0268b4d80612385359eadd2d6ddb257"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/lib/src/sqflite_android.dart", "hash": "23149dd1dabb201f41ccacb25e322741"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/lib/src/guid.dart", "hash": "55bb53dd4f9ed89c9ff88c204b59293c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/frame_rate.dart", "hash": "63f4045e237e838712fd0eed65c098c8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/byte_collector.dart", "hash": "3aaf04a3a450c1b6a144f84f3c778573"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/flutter_cache_manager.dart", "hash": "4d339d186836b857e23b70679d7544c4"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/app.dart", "hash": "dec43cdc695f6ef4f0a33ae459c0e58c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_barcode-7.4.1/lib/src/count/barcode_count_status_item.dart", "hash": "ed905c1c9b4e3c8c31d9b1610a6ca06a"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/radio_theme.dart", "hash": "3f2a39352a1c6067566f8119aa021772"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/animation/keyframe/gradient_color_keyframe_animation.dart", "hash": "633ca0d413e225ff978cdd8a5ace2b3b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camera_platform_interface-2.10.0/lib/src/types/image_format_group.dart", "hash": "64f7d6a0aea16e1f13c99592ba8e0e08"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_barcode-7.4.1/lib/src/count/barcode_count_status_result.dart", "hash": "a3203c100dee7b37fbe49c6b81509201"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camera_android-0.10.10+4/lib/src/utils.dart", "hash": "5c5843a87ea5da2360ac9c495dbacad2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/backpressure/pairwise.dart", "hash": "e8e03ace330da6d410583717e7e5f681"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xdg_directories-1.1.0/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/tooltip_visibility.dart", "hash": "ee2f417f35b5caa4a784b24c1bc32026"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/painting/paint_utilities.dart", "hash": "853b1406f2756bef671f6d57135606f9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.0/lib/src/outputs/file_output.dart", "hash": "7dbee69bb2d6088496e7d7bbdde1ccc8"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/services/mouse_tracking.dart", "hash": "5da121a0d3087e7cf021bfcdeb247b77"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/util/crc32.dart", "hash": "da0c331fea0ba64b7b8ec585ca0414c5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/stream_channel-2.1.4/lib/src/stream_channel_transformer.dart", "hash": "bf9deb76f520208269fedb1ee05992bc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/LICENSE", "hash": "39062f759b587cf2d49199959513204a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3/lib/src/backend/vm/read_write_sync.dart", "hash": "e3f76b424ad53ae6f603bf9a0662e148"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/stream_provider/base.dart", "hash": "8e16702463aaa9f1da9da189aabae66c"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/foundation/consolidate_response.dart", "hash": "04451542afc67a74282bd56d7ee454f5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/notifier/family.dart", "hash": "c32553850c6990014c017cc3b3024df3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_windows-2.4.1/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_barcode-7.4.1/lib/src/spark/spark_scan_view.dart", "hash": "12886357f49363b98de2f0b1072e0d8c"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/foundation/binding.dart", "hash": "e40877daa15509fcbd3e465d246dbc09"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/scroll_simulation.dart", "hash": "b29e302994b1b0ea5029734406101b8e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/gotrue-2.13.0/lib/src/types/types.dart", "hash": "bbdbb4b4999e23de3fa42c20168445d8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/start_with_error.dart", "hash": "47cb151906114ae0161078bb7968ffc9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/storage_client-2.4.0/lib/src/constants.dart", "hash": "ef8c48edb17b5f0eeef949cd2034e9fd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_core-7.4.1/lib/src/widget_to_base64_converter.dart", "hash": "336eb0ea573c2b25b3822593260e5653"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/painting.dart", "hash": "4bd60bd8ede4b9dad954493d26d3e586"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/gotrue-2.13.0/LICENSE", "hash": "bb0a4b2e3d82de4116e8425de9a3927f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/common.dart", "hash": "1ab2b4b439160093cb35c9b0c739bc0b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/memory/clock.dart", "hash": "d52c28f679ecf880a21c3ba08df59267"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/render_cache/store.dart", "hash": "5fcfadd2e0f0bfc232012aa24559ee80"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/storage/file_system/file_system_web.dart", "hash": "71b9fd89c14d2a8a39275d81a2500c5d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/union_set_controller.dart", "hash": "f301af2d0392296f456363085becbf47"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.0/lib/src/printers/hybrid_printer.dart", "hash": "c7ea8e1b642822fe4d241be13ab160fd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.2/lib/clock.dart", "hash": "2c91507ecca892cf65c6eaf3fbe0a7e6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/delegate/sink.dart", "hash": "87e6007f2e4468fd84513f05cafcca2d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/postgrest-2.4.2/lib/src/types.dart", "hash": "1dfcbe2dd30eeefb96f2e09791107834"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/standard_component_type.dart", "hash": "09973ba0a94d2d819052c0544dcdce70"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/lazy_stream.dart", "hash": "1649ee82914f6ad1fd46de466dc03378"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/physics/friction_simulation.dart", "hash": "732535ba697d95c80d1215c0879477f1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/parser/gradient_fill_parser.dart", "hash": "16696d5fe3a1cd6459240e537beb0d13"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_barcode-7.4.1/lib/src/batch/barcode_batch_session.dart", "hash": "63ebdeec283cc53c907b9c8cb0e7b86a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sprintf-7.0.0/LICENSE", "hash": "f26476a70de962928321bf9e80f9029e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dio_web_adapter-2.1.1/LICENSE", "hash": "3cc5c8282a1f382c0ea02231eacd2962"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/animated_icons/data/view_list.g.dart", "hash": "e5b4b18b359c9703926f723a1b8dd4ac"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/orientation_builder.dart", "hash": "177fda15fc10ed4219e7a5573576cd96"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/LICENSE", "hash": "c17706815151969aa7de6328178cc8bd"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/outlined_button_theme.dart", "hash": "8ece5be4aa5c8fa615288c4c8c5277a2"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/animated_icons/data/event_add.g.dart", "hash": "7bd8137185bc07516a1869d2065efe0d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/gtk-2.1.0/lib/src/gtk_application_notifier.dart", "hash": "65e06c048a62b579abfb7ec2a42d3b86"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/end_with_many.dart", "hash": "902509bf876a10a7b6e534a1d24fb476"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/gestures/debug.dart", "hash": "dbb0bb20c79bcea9397c34e3620c56c3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/transformers.dart", "hash": "5d2971806de340d9e970e21af445505b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/codecs/zlib/_gzip_encoder.dart", "hash": "21693dd162df8a9a6807a23224f6fa61"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/delegate/stream.dart", "hash": "809f1f0bbe7ee77e69f003952a5525d5"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/transitions.dart", "hash": "22ad3e3602e0fc7a63682e56a5aeaac0"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/animated_icons/data/menu_close.g.dart", "hash": "ef5fc00d685cd2a36c4de80e1c7e3a8f"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/sliver_prototype_extent_list.dart", "hash": "9645e1d88d63387bb98a35849f4cbe53"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/action_buttons.dart", "hash": "aed826e965e4aa2fdb3466d39e33d824"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camera_platform_interface-2.10.0/lib/src/types/camera_exception.dart", "hash": "52a3ddfb21477258870d933fed27f1a7"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/scroll_context.dart", "hash": "98f725d06ba20a1032cb8770d00d7fca"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/model/layer/solid_layer.dart", "hash": "b8bab95ba9fffc4994e558c438e16ecc"}, {"path": "/Users/<USER>/AndroidStudioProjects/Safescan/assets/images/icon.png", "hash": "93860b45993acbc1e373efcb6259c852"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/lib/src/utils.dart", "hash": "fab8d6d1b0e81315a3d78131394d31e6"}, {"path": "/Users/<USER>/AndroidStudioProjects/Safescan/assets/animations/Gradient-VOice.json", "hash": "8ae9610de6b555ae3d766578171b297b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3/lib/src/crypto/hive_aes_cipher.dart", "hash": "69a68782431189a163d7031587f20438"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camera_platform_interface-2.10.0/lib/src/events/camera_event.dart", "hash": "c51bc3a7c3a644c817818bb129a1a2ae"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/codecs/zlib/_zlib_encoder.dart", "hash": "4e116d0c1f3080cec1492036827c7fcb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/codecs/zlib/_inflate_buffer_io.dart", "hash": "fb84e07b5ecc0f027a487403597a6af7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.20+2/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_barcode-7.4.1/lib/src/ar/barcode_ar_info_annotation_width_preset.dart", "hash": "7bfaad0f2e8b7ea9a469f73cb6e01469"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/search_view_theme.dart", "hash": "4d673eddc0bd2289539b66a92faae868"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/lib/sqflite.dart", "hash": "f7be2d6ca06ea6e4deeee0e441b71d6a"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/app_bar_theme.dart", "hash": "62a38b21e9ef4b8a8d5ae1db1c355bd1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3/lib/src/util/delegating_list_view_mixin.dart", "hash": "c17576f1b73a93c4effae038a1e2a23f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_testing-3.0.1/LICENSE", "hash": "f721b495d225cd93026aaeb2f6e41bcc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/span_exception.dart", "hash": "c39101179f8bdf0b2116c1f40a3acc25"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camera_platform_interface-2.10.0/lib/src/types/flash_mode.dart", "hash": "9faa11bfe2a421b6bcb9cea335eb31a3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/material_color_utilities.dart", "hash": "11df661a909009a918e6eec82d13e3ff"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/restartable_timer.dart", "hash": "89cdb68e09dda63e2a16d00b994387c2"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/color_filter.dart", "hash": "bc3c12f9555c86aa11866996e60c0ec9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/supabase-2.8.0/lib/src/constants.dart", "hash": "5ac8c199b63cc4831717466efa17af2f"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/icons.dart", "hash": "32b222420709e8e40d12f6ea9fc0041e"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/cupertino/list_tile.dart", "hash": "837da7ede58523b5aff0ccbb40da75ba"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/foundation/_capabilities_io.dart", "hash": "faf4d014b3617ede3150f80eba25e3b4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/backpressure/sample.dart", "hash": "98ad95f9d48fa93a9cdc4a8fa0f69c73"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3/lib/src/io/frame_io_helper.dart", "hash": "bd9ef30d8168e87f9e540db76f4426db"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/painting/box_border.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/LICENSE", "hash": "7b710a7321d046e0da399b64da662c0b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_barcode-7.4.1/lib/src/batch/barcode_batch_function_names.dart", "hash": "79021f66b06aa46a19881a2c6b290f96"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/sqlite_api.dart", "hash": "5494fe877262550facf407b379edae59"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/supabase-2.8.0/lib/src/supabase_stream_filter_builder.dart", "hash": "d55d6d385053098ce33791ee334b1885"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_android-0.8.12+23/lib/src/messages.g.dart", "hash": "f1c7d23cd6db9504510e67e2957b4aef"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/snack_bar.dart", "hash": "5c5a8f737a2cec1d969f4a9f8dc80a8d"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/radio.dart", "hash": "9b1cee1f8aa8b638cad928232383b02b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/codecs/lzma/lzma_decoder.dart", "hash": "a3a00f8aba49a8beb05a311ce1eb7397"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/supabase-2.8.0/lib/src/version.dart", "hash": "d24e968ac0cd99885f59eb0c1e5430c3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/quantize/quantizer_map.dart", "hash": "b6bcae6974bafba60ad95f20c12c72b9"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/grid_tile.dart", "hash": "9c169d41e4740bbc21d0ce33bc753119"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camera_platform_interface-2.10.0/lib/camera_platform_interface.dart", "hash": "3bb038b816dd04a5e56dc3c549a1905a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/yet_another_json_isolate-2.1.0/lib/src/_isolates_io.dart", "hash": "d8f2dfdd83e48a949ee5dd4f5585ee36"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_platform_interface-2.3.2/lib/method_channel_url_launcher.dart", "hash": "351ed98071b53d3c2e98d376f2a65a74"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/characters.dart", "hash": "43268fa3ac45f3c527c72fc3822b9cb2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/quantize/quantizer.dart", "hash": "db799bf48af97b7c0edc93ad96b4a6da"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/typed_stream_transformer.dart", "hash": "991902b33f1d81c417b707a41341ed59"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/postgrest-2.4.2/lib/src/postgrest_filter_builder.dart", "hash": "93b38a69cfd8e20a00a85fc371a97fcd"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/search.dart", "hash": "66a927b3f610db5ff8c77a6ba3ccee0b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/combined_wrappers/combined_iterator.dart", "hash": "6c54f90e0db5f42a13be6b3efeb4a04d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/storage_client-2.4.0/LICENSE", "hash": "bb0a4b2e3d82de4116e8425de9a3927f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/lib/src/get_application_id.dart", "hash": "32f5f78e5648f98d8b602c6233aa4fc5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image-3.4.1/LICENSE", "hash": "6e15c47981e2e43ee17849a222e33b76"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/path.dart", "hash": "157d1983388ff7abc75e862b5231aa28"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/painting/rounded_rectangle_border.dart", "hash": "0bc495ddf9b02a06a5fc6934847e8708"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3/lib/src/box/box_base_impl.dart", "hash": "********************************"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/range_slider.dart", "hash": "2e0b7bb9c12ed9f989240a20a878badc"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/services/system_chrome.dart", "hash": "40d43557904504dbd816a205b73461b4"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/shortcuts.dart", "hash": "721fe68e34a4747334faa11e91f93523"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/mime-2.0.0/lib/src/mime_type.dart", "hash": "e26cb5bf5970055a9bd1828b524cb213"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/sliver_resizing_header.dart", "hash": "eca5aa939aa9722ead4b6c347fb4d11a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/go_router-16.0.0/lib/src/misc/extensions.dart", "hash": "033cc457821088f152cc31f4439f9f0d"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/slotted_render_object_widget.dart", "hash": "74708cb40b7b102b8e65ae54a0b644be"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/timestamp.dart", "hash": "bca54a6e3c0b80a2300ab9ae4e9db4e9"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/animation/curves.dart", "hash": "74a89d22aa9211b486963d7cae895aab"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/supabase-2.8.0/lib/src/supabase_client.dart", "hash": "87a085a408e226a614d0cdd4a7cc4b30"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/painting/star_border.dart", "hash": "e324dd19cc02a1bf47bf7cc545dcca79"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/model/animatable/base_animatable_value.dart", "hash": "4394f27ae74af6f34c3ea687775b4a6a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/constants.dart", "hash": "3b481084198e4581293dd9ddddb9afb4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/memory.dart", "hash": "4ce56dab766f683c213da41402d17049"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/magnifier.dart", "hash": "4da5ad5941f2d5b6b3fbb3f7ea217b41"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/compat/file_fetcher.dart", "hash": "808718676ca459f8941aa519a73bbff2"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/ticker_provider.dart", "hash": "10cf10518abe4a916f2cb9ed7c4b635f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/lib/src/charcode.dart", "hash": "b80f25d51570eededff370f0c2b94c38"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/vector3.dart", "hash": "478e1071c9f577b6cabb8d72c36de077"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/theme.dart", "hash": "d5363426c1acae1c7410b4096cefd94d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3/lib/src/backend/vm/storage_backend_vm.dart", "hash": "29255b18bbbac9298fb8d4964f6610eb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/framework/ref.dart", "hash": "452cd5bd89dd73f555cc1ef42032e1f8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/matcher-0.12.17/LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/animation/content/repeater_content.dart", "hash": "857411d62d9ca42263948397c275bf50"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/parser/path_keyframe_parser.dart", "hash": "3d2ebeed7e9072052e61a4b647292b95"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/lib/method_channel_shared_preferences.dart", "hash": "513d6195384503beeb7f3750e426f7bb"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/scheduler.dart", "hash": "95d8d1f6a859205f5203384e2d38173a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/postgrest-2.4.2/lib/src/raw_postgrest_builder.dart", "hash": "3d010466f9c164cbcc8fb5e9044ecd45"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_core-7.4.1/lib/src/function_names.dart", "hash": "38e4ecdcc5f2d01e445646264763543c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/codecs/bzip2/bz2_bit_reader.dart", "hash": "68cac9c19e7454fb503c31e3042bbcc0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/gotrue-2.13.0/lib/src/types/fetch_options.dart", "hash": "fb66893c06c81c29cce385330bbd2b05"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/equality.dart", "hash": "46e577ec532e21029e9cee153d7ca434"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/gtk-2.1.0/lib/src/libgtk.dart", "hash": "8f92aefe740231438bdabf80e375164f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/lib/path_provider_platform_interface.dart", "hash": "09b3f3b1ef14ce885c016f2eba98f3da"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/lib/src/sqflite_darwin.dart", "hash": "644e5e32abaad61eb192128f412424ed"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter_tools/lib/src/build_system/targets/icon_tree_shaker.dart", "hash": "0763a220fcb5274b6c228b8b440ddb2a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/storage/cache_info_repositories/non_storing_object_provider.dart", "hash": "21cb059be81989938ccfbda405ae9a65"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camera_platform_interface-2.10.0/lib/src/types/media_settings.dart", "hash": "6a2886b3913c86d327624dfdccda7c04"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/widget_span.dart", "hash": "84e117adf104c68b0d8d94031212b328"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/result/file_response.dart", "hash": "8a4e81e8fccc01dc69bbc847b75a31b5"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/expansible.dart", "hash": "43bc92e2816a78f5d5987930bc3e804d"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/page.dart", "hash": "de67603c6b6c6f55fcd5f8b06423d29a"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/tween_animation_builder.dart", "hash": "107c33a245427bf0f05e21c250653dc6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/value/lottie_relative_point_value_callback.dart", "hash": "17d06ffe51d21c6b5b877c2794514de7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/model/animatable/animatable_scale_value.dart", "hash": "797a14e567561e548460caebe5f3cade"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/model/layer/composition_layer.dart", "hash": "20568527ffba536b0c08edb57870b83e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/lib/src/grapheme_clusters/table.dart", "hash": "1f437276972808bf4cf722440da1b231"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/animated_icons.dart", "hash": "78ce7527fa364df47ba0e611f4531c2c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/subjects/publish_subject.dart", "hash": "b2fa768bd42261fab936524dd6f1c8ae"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/codecs/zlib/_zlib_decoder_web.dart", "hash": "07076a20e944f2e53a72ab9539fd8327"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/text_selection.dart", "hash": "138038335aa2c209f231b2694d5aae3f"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/cupertino/text_selection_toolbar_button.dart", "hash": "9a67635cfd2e047d996c4840d4cb18ea"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/animation/content/key_path_element_content.dart", "hash": "1ba5fb0d0c40a35086c30af30f8ba4dc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shimmer-3.0.0/LICENSE", "hash": "87ee25bbef5b7cb7dcb056c3ec20f243"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_windows-0.2.1/LICENSE", "hash": "a086f9770acbfc6a5e421b49411d9415"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_barcode-7.4.1/lib/src/capture/barcode_capture_feedback.dart", "hash": "b76b3e6fe63346eba3e3f0f6cb39c4b2"}, {"path": "/Users/<USER>/AndroidStudioProjects/Safescan/assets/animations/HomeScreen-2.json", "hash": "34a147ad9a0287b867e3dc05ab6dd517"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/forwarding/forwarding_file_system_entity.dart", "hash": "67918403456e9e1c17b3375ea708292c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/LICENSE", "hash": "80ea244b42d587d5f7f1f0333873ad34"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/service_extensions.dart", "hash": "7abc7e5212374d29bfe5372de563f53c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/parser/moshi/json_scope.dart", "hash": "edebf267db1a6d3bf4d3e9c833232394"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_barcode-7.4.1/lib/src/barcode_function_names.dart", "hash": "362ad645b45df620263165840c90df4f"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/slider_theme.dart", "hash": "86d7d305c24e6073b89718914fcd3ee0"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/services/raw_keyboard.dart", "hash": "02dabe6a8cd832d69b4864626329ef30"}, {"path": "/Users/<USER>/AndroidStudioProjects/Safescan/lib/providers/product_provider.dart", "hash": "0b57d836289a8096433160c2acc42614"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/interface/file_system_entity.dart", "hash": "04e7480fb89755fcc5f64f7d80ca610f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/where_type.dart", "hash": "93f43c6a287e8cd98477a02e6aa0da8d"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/actions.dart", "hash": "1c7764fa08241a44711301c74fb658df"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/postgrest-2.4.2/lib/src/postgrest_builder.dart", "hash": "7b92a40ac587515d2d3703502bea806b"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/sliver.dart", "hash": "dc037755b1140b31ffc8295fb9570cff"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/min.dart", "hash": "ecfd8a09746c8bbb7b51d4741fb4645e"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/filled_button_theme.dart", "hash": "52beedf1f39de08817236aaa2a8d28c5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.12.1/lib/src/lazy_trace.dart", "hash": "d75954340a0c7770eb9a149f7994598e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/functions_client-2.4.3/lib/functions_client.dart", "hash": "f3527b080b3bdd6efbc79feb787a1840"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/rendering/table_border.dart", "hash": "bbc7eccdbd8472a2180e0dffce323bb9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/codecs/zlib/_gzip_encoder_io.dart", "hash": "8c7bf64921c74572e55b1c01b3df25f3"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/services/raw_keyboard_ios.dart", "hash": "1303bc77ad63625069f2d23afc73f523"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/model/content/gradient_type.dart", "hash": "ce4ac4da96580f8ad23d3df1b6eb5d7a"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/scroll_configuration.dart", "hash": "c9ab6d9cf33f78fef3ff4ad99fc73390"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.0/lib/src/log_printer.dart", "hash": "4576043706f693ac8efde372e73b23de"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/navigation_bar.dart", "hash": "9b52b890a7d94fe05f5f3ab8b7324b35"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_barcode-7.4.1/lib/src/count/requests/barcode_count_status_provider_request.dart", "hash": "922676a1c1b661b9aea7803664c246d9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/go_router-16.0.0/lib/src/configuration.dart", "hash": "0f86f73c30e6322060a071461bc7c6d1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/animation/content/content_group.dart", "hash": "def9fa9a7768b138f3a34577ef9c5d1a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/digest_sink.dart", "hash": "038a6fc8c86b9aab7ef668688a077234"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/async.dart", "hash": "3f9362642d37e0d97860181e8a1dd598"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3/lib/src/adapters/big_int_adapter.dart", "hash": "f962a26b7944264455f9d479c898f535"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/interval.dart", "hash": "b2a2c73b0b7b528180181e9e4e3b4e92"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/checkbox.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/animation/keyframe/transform_keyframe_animation.dart", "hash": "********************************"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/media_query.dart", "hash": "98cd866294c42f2faff3451e5ca74bfa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camera_android-0.10.10+4/lib/src/android_camera.dart", "hash": "b21b1707866219d985e9795621c76e10"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/animation/keyframe/keyframe_animation.dart", "hash": "93455becdf11516b209dc8b65b7d313b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_macos-0.9.4+3/lib/file_selector_macos.dart", "hash": "20f3c0d39cbc5c2fdb223745edcecdec"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/utils.dart", "hash": "8a7e3b181572ed50e923e5dc05a7533d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/synchronized-3.4.0/lib/src/multi_lock.dart", "hash": "2ac6fe0e9a4d7b15855dabd7468cc320"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/services/raw_keyboard_android.dart", "hash": "c9111e47389ee4b70aab720435a2a2df"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/rendering/sliver_fixed_extent_list.dart", "hash": "2adcbf9fb509dd8fe8864a702db29043"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/rendering/list_wheel_viewport.dart", "hash": "2baf11d03f1f50ccef5294c1fe810e25"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/adaptive_text_selection_toolbar.dart", "hash": "9ec81b597c30280806033b70e953b14c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camera_platform_interface-2.10.0/lib/src/types/camera_image_data.dart", "hash": "12cb420815f15492715656fe5a77f4a5"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/navigation_drawer.dart", "hash": "7755bff1bceea0db42330320ad10baad"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/value/lottie_relative_integer_value_callback.dart", "hash": "e3a90f22a9b881a357f08d1f2df44863"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/model/layer/shape_layer.dart", "hash": "8ef72c6ebcd297f702a8ae4a9606fb1e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_barcode-7.4.1/lib/scandit_flutter_datacapture_barcode.dart", "hash": "a18dceebb0df36b71006e212b763d96b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/unmodifiable_wrappers.dart", "hash": "ea7c9cbd710872ba6d1b93050936bea7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/app_links_platform_interface-2.0.2/lib/app_links_platform_interface.dart", "hash": "1650dcb22227d2d7520c279f9b934775"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/parser/shape_group_parser.dart", "hash": "f14d189a84fe580484446f7b62ba3d7d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/LICENSE", "hash": "1a3dbe8e080bf7ea1081e3c716ee17f1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/go_router-16.0.0/lib/src/pages/cupertino.dart", "hash": "671e5f26fbf94b9d5a70b14c8c494760"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/mime-2.0.0/LICENSE", "hash": "39062f759b587cf2d49199959513204a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_macos-3.2.2/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_core-7.4.1/lib/src/map_helper.dart", "hash": "67748418161ddb95ef1f13beaf36c8d7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_barcode-7.4.1/lib/scandit_flutter_datacapture_barcode_capture.dart", "hash": "0f312275c81e4a3534ab03fcf7942524"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3/lib/src/binary/frame.dart", "hash": "e28d4397780eecba27eaced013118898"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/stream_subscription_transformer.dart", "hash": "9422bcb42f545a3d7fad54a0559effc2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/src/chunked_coding/encoder.dart", "hash": "dbf4f1e95289bc83e42f6b35d9f19ebe"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/value_delegate.dart", "hash": "798fbeb1139d9a2a1dd96afcac7ce6ce"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/octo_image-2.1.0/lib/src/image_transformers.dart", "hash": "10404d098f485bca549850751b3e93b9"}, {"path": "/Users/<USER>/AndroidStudioProjects/Safescan/assets/images/SafeScan-logo.png", "hash": "c5ca992a99cf4b124dff541ffa75ac79"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/animation/content/base_stroke_content.dart", "hash": "b4bf5d9b21f50c42d9e061b86b6c37d8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/src/scan.dart", "hash": "acfc0a55deec22276e085dae6197833a"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/cupertino/icons.dart", "hash": "790dc5e1e0b058d13efbd42a3f46498e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/src/open_options.dart", "hash": "296e60aee7732b001a79f3216058a381"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/result/capture_sink.dart", "hash": "7c57a9163e2c905ac90a6616e117766f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fixnum-1.1.1/lib/src/int32.dart", "hash": "f6b2a03b8f3554a6b37f151f6a561fe9"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/services/spell_check.dart", "hash": "e3d917994e875601c2dadaf62de546f2"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/scheduler/debug.dart", "hash": "d72a4ddaf6162d8b897954e02b4a2a4c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/result/capture_transformer.dart", "hash": "e82a9b67ba33ae635b9b083ef147fb9b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/file.dart", "hash": "dcef90946d14527736cde04a54d334db"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/sink_base.dart", "hash": "8fec1bb0c768b230066dba96aac40ff5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/enums.dart", "hash": "b49758f50c20a4f98a48e3af42de35d7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/go_router-16.0.0/lib/src/path_utils.dart", "hash": "977c776bf5d295caaf8483b69f7a4b57"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/cupertino/desktop_text_selection_toolbar.dart", "hash": "e8aae4779eccfdedd9c4b8cbce4ab952"}, {"path": "/Users/<USER>/AndroidStudioProjects/Safescan/lib/widgets/enhanced_safety_display_widget.dart", "hash": "758ca7bee6d7a8b18af0a8abfc286a01"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/foundation/observer_list.dart", "hash": "8ae04de7c196b60c50174800d036642f"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/gestures/resampler.dart", "hash": "cad4582fa75bf25d887c787f8bb92d04"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/archive.dart", "hash": "8b7c7d224502ad3f94501844a82ba5df"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6/lib/src/interface/platform.dart", "hash": "d2bab4c7d26ccfe4608fe8b47dd3b75c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/app_links_linux-1.0.3/lib/app_links_linux.dart", "hash": "b1a4d4681448eee5c5baf6f4ef00f61d"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/basic.dart", "hash": "e5ebffb07608ee2f93a7aa4c23848564"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/material_localizations.dart", "hash": "063f2360bd47faba2c178ce7da715d92"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/platform_selectable_region_context_menu.dart", "hash": "aef544fef0ced7679e0edaf5f8d036b7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/gotrue-2.13.0/lib/src/types/auth_response.dart", "hash": "78d6e76da49fba9747e5bf4b14505235"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/icon_theme.dart", "hash": "03d585dfc6055d74a4668e69263afa5a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/animation/keyframe/split_dimension_path_keyframe_animation.dart", "hash": "dfc19600bfb0f7a36895cf98069c234e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/storage/cache_info_repositories/helper_methods.dart", "hash": "0d0350902fa7b7c829baf0666f1a74dd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/gtk-2.1.0/lib/gtk.dart", "hash": "1eb05ccbc3ef77ddc0580581ec612f3a"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/services/hardware_keyboard.dart", "hash": "a32174b6de983c1652638940e75aae6a"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/date_picker.dart", "hash": "15ee790ce6b1c0a29d38af8094ad1722"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/editable_text.dart", "hash": "20b03effe92fdb82cb2b1efcf637be3e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/lib/src/shared_preferences_async_foundation.dart", "hash": "282aeeb78f4a92064354b5fe98161484"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.2/lib/src/generated/unicode_glyph_set.dart", "hash": "cdb411d670a094822c46ead81fc1c4f7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/model/animatable/animatable_path_value.dart", "hash": "4004507094e70f0c86453716e29e0d98"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_core-7.4.1/lib/src/feedback.dart", "hash": "f57ac95ce14107fc46b1a64fb0839e5b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/animation/keyframe/double_keyframe_animation.dart", "hash": "f8ef3201607ea9e3f87bf407562b9e55"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_barcode-7.4.1/lib/src/count/barcode_count_function_names.dart", "hash": "1cffd89ead279aaba15a97925c597452"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/services/raw_keyboard_web.dart", "hash": "547eac441130505674f44bf786aee606"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "/Users/<USER>/AndroidStudioProjects/Safescan/lib/router.dart", "hash": "52a5dd786610cad8d1d8bddd6cffe94d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/gotrue-2.13.0/lib/src/types/auth_state.dart", "hash": "78611633461a88c4f4bbb031e6633964"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/painting/flutter_logo.dart", "hash": "044d6bef26a97ada1d56ff6fe9b7cc14"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_core-7.4.1/lib/src/data_capture_context.dart", "hash": "9c951dd94100ddcea409e2bf190afd1b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_barcode-7.4.1/lib/src/tracked_barcode.dart", "hash": "1a98e191753463c080de80eb67b71369"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/default_if_empty.dart", "hash": "527ad391c229e34074a6d5c1aa656133"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/painting/text_span.dart", "hash": "6fc640633e357a75291efec1c68b02ce"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/cupertino/tab_scaffold.dart", "hash": "9434ff8aa06e13d5981ed6ec15eceb64"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/util/input_file_stream.dart", "hash": "764833a66ea26768c45a68b689fafe19"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/desktop_text_selection.dart", "hash": "dd3402d5403be91584a0203364565b1b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/codecs/zlib/gzip_flag.dart", "hash": "05b64f898b2f893af5ac79020dedb851"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cross_file-0.3.4+2/lib/cross_file.dart", "hash": "b5c8f4dba868efb80ed69fcd5a7d3f07"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/concat_eager.dart", "hash": "28788651dbafca42ae0d6023352274f3"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/cupertino/debug.dart", "hash": "51fa10cf30bde630913ff4c6e40723ba"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3/lib/src/backend/vm/backend_manager.dart", "hash": "c1320c369344322829e5b7c8d63e0d58"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/cupertino/text_selection_toolbar.dart", "hash": "8dedd49e916a59b6940a666481d82e10"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/flexible_space_bar.dart", "hash": "9d6f9dd391f828bccdbb47c5072c04c1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/stream_sink_transformer/typed.dart", "hash": "35c9371cbb421753e99a2ca329107309"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/painting/stadium_border.dart", "hash": "85814d14dae3bc1d159edd0a4bef48e4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_core-7.4.1/lib/src/viewfinder.dart", "hash": "e6463f56c7344b44c47b4980873c2817"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6/lib/src/testing/fake_platform.dart", "hash": "f1a57183b9d9b863c00fcad39308d4c1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/storage/file_system/file_system_io.dart", "hash": "7caf4c65583e594208feee7e60872fea"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/value_listenable_builder.dart", "hash": "68c724edcc385ae2764308632abb76b4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/realtime_client-2.5.1/lib/src/constants.dart", "hash": "c282e9d03d7d1814a160ff8f7d424731"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/cupertino/text_field.dart", "hash": "29d1f8b59096b4d11d693c4102a08499"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/scroll_controller.dart", "hash": "ec48414c6983150c30241ba7128634fa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/util/file_content.dart", "hash": "416416560c9553de5c741586424c78b0"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/input_date_picker_form_field.dart", "hash": "f357bc5433a3205fc48000ad8c569c5b"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/binding.dart", "hash": "a594e2e46c047f44912e93f2e38f4a47"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/max.dart", "hash": "82294310993896043f681e7fd66c4e56"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/services/autofill.dart", "hash": "4fa52a6cb3ac24b95e99a20d034f43c3"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/platform_view.dart", "hash": "72804f9d34b9a247c43d6cc575527370"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/octo_image-2.1.0/lib/src/errors.dart", "hash": "2a7dd605fd24026f238835990b2af51c"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/animation/listener_helpers.dart", "hash": "72bbc3da5da130fb11bb5fc65614653c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/parser/font_parser.dart", "hash": "1adb04613efa068a53fb08a65a700beb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/vector4.dart", "hash": "299bd3979d7999412945ac4e3199cdcf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_barcode-7.4.1/lib/src/batch/barcode_batch_defaults.dart", "hash": "cc5c890cc0cf92ac70fab0dedbfc4cf8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/equatable-2.0.7/LICENSE", "hash": "612951585458204d3e3aa22ecf313e49"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/animation/content/drawing_content.dart", "hash": "2502fa3b126f6546e9bff4b83b025404"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/text_selection_theme.dart", "hash": "166478d231aa67eb8e47a7b559955e6b"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/foundation/capabilities.dart", "hash": "5fe5b5ed3ec92338a01f24258b6070a3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/parser/shape_trim_path_parser.dart", "hash": "6aeb52077d8aa178d748a0ba818ac436"}, {"path": "/Users/<USER>/AndroidStudioProjects/Safescan/lib/models/ingredient.dart", "hash": "1138034b2eda342097afec1b8f1800db"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3/lib/src/object/hive_collection.dart", "hash": "f083ee7c0f8875e81b5fd6e33fde3ed5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/distinct_unique.dart", "hash": "32a430474c588e6a5dfb093a222e9f48"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/interface.dart", "hash": "5145b27b3db429f9f1da26cfe563bd02"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/realtime_client-2.5.1/lib/src/push.dart", "hash": "9bb04363a51bfbd39db5fbf1faf7e873"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/parser/rectangle_shape_parser.dart", "hash": "a1e6c126cee5e5afb37cb05ea89a16ae"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/rendering/rotated_box.dart", "hash": "********************************"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/banner.dart", "hash": "674ba42fbba2c018f6a1a5efd50ab83e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/digest.dart", "hash": "d623b1e2af43bcd9cde14c8c8b966a8b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/codecs/zlib/_gzip_encoder_web.dart", "hash": "bfe4571999417357305efc638e123bc3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fixnum-1.1.1/LICENSE", "hash": "3c68a7c20b2296875f67e431093dd99e"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/button_bar_theme.dart", "hash": "0f717ff4ecfdaa0347894abbedd5d1e9"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/widget_inspector.dart", "hash": "0bda807c0c8098d0ca933cde19f49516"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/matrix3.dart", "hash": "447b270ddd29fa75f44c389fee5cadd1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/lib/src/constant.dart", "hash": "8d5660686b2687f3947b822758c82942"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/yet_another_json_isolate-2.1.0/LICENSE", "hash": "f29a26e7da1ef36001a9e33e4871938f"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/image_filter.dart", "hash": "6c0e97a3b04c9819fe935659014f92e8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/src/env_utils.dart", "hash": "d75f62f03297d8fada84de77f3e92373"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/src/platform/platform.dart", "hash": "17488cbfc8b9ee2e6e5ba0229d7c21a1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/gotrue-2.13.0/lib/src/gotrue_admin_api.dart", "hash": "998bf77057b62fdeb017e4d9cd169d7d"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/widget_preview.dart", "hash": "3208b2267d4d1b0d118b8fcdd774b753"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/stream_notifier/auto_dispose.dart", "hash": "19ad3f559f8a8ac66bbf9a697588b5f2"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/constants.dart", "hash": "83df4f6e4084a06a4f98c27a524cc505"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/lib/src/exception_impl.dart", "hash": "7b3fbf91245e315040bd120bc9bf51ea"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker-1.1.2/LICENSE", "hash": "619f69d64af6f097877e92ac5f67f329"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/animation/keyframe/path_keyframe_animation.dart", "hash": "8ab4b145ca7d35a396d18faa50aa3fac"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/focus_manager.dart", "hash": "84589f907e3e4d8fc72e5c786a0530f2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/archive/encryption_type.dart", "hash": "990c197185c1e608b8f489b1c0effa42"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/subjects/behavior_subject.dart", "hash": "86d361932e590380696b3189090d1034"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/animation/content/merge_paths_content.dart", "hash": "b3aa8a65d3870d83ead6275685be11a8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_core-7.4.1/lib/src/zoom_gesture.dart", "hash": "9e9e8fa90b17f2e8f81520b71299747f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_barcode-7.4.1/lib/src/pick/ui/barcode_pick_status_icon_settings.dart", "hash": "36fec50cf3b4201718514a5894c83bde"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/notification_listener.dart", "hash": "d3b949a1e7578291493af5fd28846314"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/animation/keyframe/path_keyframe.dart", "hash": "e266dc0bf2da69800d80b0b9c7548c51"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/meta-1.16.0/lib/meta.dart", "hash": "aaace37762c25bcd679c2ab09129db12"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/local/local_file_system.dart", "hash": "06c73ad137e5db31d7e6ba4258ac13c7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/future_provider/base.dart", "hash": "d7d24730943cbf47d39aa11425ebf344"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.4.0/lib/typed_data.dart", "hash": "b9abba31a48a9c2caee10ef52c5c1d0e"}, {"path": "/Users/<USER>/AndroidStudioProjects/Safescan/lib/screens/home_screen.dart", "hash": "918e660916f43738bc58c9aecd54f56c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/codecs/zlib_encoder.dart", "hash": "2fb9bf1a094efc775572581a6247aaba"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/framework.dart", "hash": "f9963c0de15655f08d11298175dd45fc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_windows-2.4.1/lib/shared_preferences_windows.dart", "hash": "2bc47cc0ce47761990162c3f08072016"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/time_interval.dart", "hash": "17db414327e1f763806c112c6f664ca8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/parser/animatable_path_value_parser.dart", "hash": "c508a02f316a5279af3120b60916a1c1"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/dismissible.dart", "hash": "c98d71a32518e80bc7cf24b1da6c9c57"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/supabase_flutter-2.9.1/lib/src/supabase.dart", "hash": "aef61de7172a736ad01076a13accd479"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/v8generic.dart", "hash": "00a661dfeb90c5dba43ec7e638141966"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/reorderable_list.dart", "hash": "67241b28b6ab2188280fb614f1607b2d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/utils/error_and_stacktrace.dart", "hash": "491a33282b614f40bd0fbd3f3b3d45f1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/codecs/zlib/inflate_buffer.dart", "hash": "5658bef229cf316c21e441181b7ea6be"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3/lib/src/crypto/aes_engine.dart", "hash": "be8db0f0d8f9d7aef0bc2cb469f73907"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/painting/image_cache.dart", "hash": "4a2215ab704d09e97121c1bb71942b3f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/dynamiccolor/material_dynamic_colors.dart", "hash": "81bf43e01741bf8b9df15ec37ffbc9ea"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_core-7.4.1/lib/src/battery_saving_mode.dart", "hash": "53d13d9701d5df593d0856b4d1f981fe"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/src/mixin/factory.dart", "hash": "63fa9307c55c93f4fde0e682e7da6503"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/navigation_drawer_theme.dart", "hash": "f6d18a38c0986111a3d297424ed6fbcb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/lib/src/relative_span_scanner.dart", "hash": "b9c13cdd078c3b28c3392f0d6d5d647b"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/cupertino/context_menu_action.dart", "hash": "84f94e87e444ce4ebc562b2707348a8f"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/services/binding.dart", "hash": "61cf3ac380d43d042f8d9b7e7f6a11e6"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/single_child_scroll_view.dart", "hash": "6e22c7f1454c97560ef83096561678dc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_dotenv-5.2.1/lib/src/errors.dart", "hash": "246aa94bb2d86ce16c59b69b13d42904"}, {"path": "/Users/<USER>/AndroidStudioProjects/Safescan/.env", "hash": "b7061eebc0a36a26b1f3d3b4dff0ed72"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/services/haptic_feedback.dart", "hash": "9ea1746a0f17f049b99a29f2f74e62ee"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/ink_highlight.dart", "hash": "a9e3af96f170745db1c281777cb6bda9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/v7.dart", "hash": "eaeef30b0e3cd638d4dad2b0f4db8417"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/internal_style.dart", "hash": "974d0c452808a1c68d61285d0bd16b28"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/painting/decoration.dart", "hash": "ae85856265742b6237ed0cb67c4364af"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/services/asset_bundle.dart", "hash": "ef24f0630061f35a282b177d372c00d1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/take_until.dart", "hash": "85fcef4d360ca759563bbfbe7c8d5e8d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camera_platform_interface-2.10.0/lib/src/method_channel/type_conversion.dart", "hash": "cd53e402b561c57f3a01c952fde4a349"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/rendering/tweens.dart", "hash": "29befe23f841cf5dd2dc7df24c13d88d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/io.dart", "hash": "119ed2f372555dcadabe631a960de161"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/providers/load_fonts.dart", "hash": "14621d1e349bf4f710e8190bd6286d23"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/app_bar.dart", "hash": "7706f479f74f6076ef8113576fe54749"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/animation/animation.dart", "hash": "c8564aa311746f4047cd02e26ff4df75"}, {"path": "/Users/<USER>/AndroidStudioProjects/Safescan/build/app/intermediates/flutter/debug/flutter_assets/NOTICES.Z", "hash": "229538aa217b36bee39e68df4f354399"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/boundary_characters.dart", "hash": "9d1525a634d27c83e1637a512a198b4f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/codecs/zip/zip_file.dart", "hash": "e380149475f6c11ddc46dd1844ac0123"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/provider/base.dart", "hash": "34d65aad713399e0e95c6d52aea92d88"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/never.dart", "hash": "238c701652f1f5ad9ba928994a96e608"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logging-1.3.0/lib/src/logger.dart", "hash": "49b829330c9d1fa06c2856f5f2266921"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/scroll_physics.dart", "hash": "f26f519ea124441ec71b37df7cfa1ee9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lints-6.0.0/LICENSE", "hash": "4cb782b79f6fc5792728e331e81a3558"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/model/content/shape_stroke.dart", "hash": "a9643ec0a1c73bc0f0efba95d7b21120"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/decorated_sliver.dart", "hash": "4b50828d394e7fe1a1198468175270d9"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/sliver_layout_builder.dart", "hash": "0c520a6b1ab38e0f294c3ddbc2ec9737"}, {"path": "/Users/<USER>/AndroidStudioProjects/Safescan/lib/screens/profile_screen.dart", "hash": "12f4713c95aaf246e4f39a005ecc816d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/supabase-2.8.0/lib/src/counter.dart", "hash": "7aec87ae03cb8479f958c018827260b1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/LICENSE", "hash": "1bc3a9b4f64729d01f8d74a883befce2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/LICENSE", "hash": "80ea244b42d587d5f7f1f0333873ad34"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/color_scheme.dart", "hash": "7bbb6aab4e83fc272886a39c92157201"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/spacer.dart", "hash": "d2372e0fb5a584dcd1304d52e64d3f17"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/about.dart", "hash": "4bf9cb0fbb8b0236f0f9e554c7207a4c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/utils.dart", "hash": "05778db9e882b22da2f13083c9f28e0d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/providers/load_image.dart", "hash": "ececeff65394898218c6da5c69ad2ae5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/stream_zip.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/quantize/quantizer_wsmeans.dart", "hash": "6c6dfd5ba4546c1f32201555d6cff215"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/model/animatable/animatable_transform.dart", "hash": "dfaa4f29ee22beaf508b7969f91f4f49"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/utils/collection_extensions.dart", "hash": "874c21db82e74ec1d570b48ffb1bad17"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/rendering/performance_overlay.dart", "hash": "3d892f04e5e34b591f8afa5dcbcee96d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/animation/keyframe/text_keyframe_animation.dart", "hash": "ebbd10af4abca80f369b8e3cd105fd6f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/octo_image-2.1.0/lib/src/image/fade_widget.dart", "hash": "e1d33f6f03e359759c131d64cf62c84f"}, {"path": "/Users/<USER>/AndroidStudioProjects/Safescan/build/app/intermediates/flutter/debug/flutter_assets/assets/animations/SafeScan-blue.json", "hash": "dac327f30b2d735620a38f6fa10ca9cb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/v8.dart", "hash": "e3d03ffb9ffa123af98df771a98759c0"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/expansion_tile.dart", "hash": "d9511b6618e15c2df1d5d0ad39256ed1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme_rainbow.dart", "hash": "0bc80db5885f9d8ecc0f80ddab6fe8b4"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/tabs.dart", "hash": "ac902f2f74549f89e0be0f739d94f7f6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider-2.1.5/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/animated_icons/data/menu_home.g.dart", "hash": "edbd68eb36df4f06299204439c771edd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_core-7.4.1/lib/src/image_buffer.dart", "hash": "77e9882747fd0f2c987c33c5a51ab509"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/parser/path_parser.dart", "hash": "25c9959e9365f1b5634aae74cd849bf4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_barcode-7.4.1/lib/src/batch/barcode_batch_advanced_overlay_widget.dart", "hash": "2e1239e6af1e1156a672d3963706ace7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/framework/async_selector.dart", "hash": "c050fb9d5c851547735cf2c46d8b6288"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_platform_interface-2.3.2/lib/src/types.dart", "hash": "83bb9dfd0d336db35e2f8d73c2bdda85"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/snapshot_widget.dart", "hash": "075310a7fe661b71e9a583aab7ed4869"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/lottie_drawable.dart", "hash": "f125df76f41836325003606fb5dcebda"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.0/lib/src/filters/production_filter.dart", "hash": "d455a0ea71515758776153cc65cb1978"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_barcode-7.4.1/lib/src/composite_type_description.dart", "hash": "56a5808e7ae09302f02b3fee307375c8"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/menu_anchor.dart", "hash": "ceafe3fee68e6597afe301af3cc318c6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/src/types/camera_device.dart", "hash": "5de9b4234c869bfb7f58138e26207e64"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/performance_tracker.dart", "hash": "cf323f5116372ed3694b4b75af1a5444"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/storage/cache_info_repositories/cache_info_repository.dart", "hash": "d423d24bacc39262f492386b09a7ee7b"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/rendering/image.dart", "hash": "4eede9144b4c0e4b14bd426654183174"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/src/chunked_coding.dart", "hash": "5f5c07df31f7d37780708976065ac8d3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/util/file_handle.dart", "hash": "8401902afa693a8aad93afc52185e846"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_design_icons_flutter-7.0.7296/LICENSE", "hash": "0d2e75837433e7f55da5ce1145a85597"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme_neutral.dart", "hash": "3ee18da390e16ca65f2ef168adb8a1ef"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/path_set.dart", "hash": "1b20a6e406ca8e79675b2ebd9b362d10"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/scroll_delegate.dart", "hash": "e78589269f033237f43ffdc87adc47a9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme_fruit_salad.dart", "hash": "3c8d2d2b73f69d670141d376642e5252"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/services/text_formatter.dart", "hash": "b139a58dace0b9d9a07a3523ed72ced5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/util/_cast.dart", "hash": "c5d37a6208e5c133ef7bf15eccb9cd49"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/sequence_equal.dart", "hash": "e9e452fa340b489a49dba00eabefa3ba"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/utils/gamma_evaluator.dart", "hash": "bd8eab639a83cc07176ba0dd7c33f9a2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/src/types/retrieve_type.dart", "hash": "550bfd92eddfc12d28a028ef44f9cedd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/composition.dart", "hash": "c6d1fd51973a4c95dc60c84309b3695e"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/services/predictive_back_event.dart", "hash": "16859f5e798cf33fc3c76a7a3dca05d7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/functions_client-2.4.3/lib/src/constants.dart", "hash": "31189b2965c0fd42d6b7a5523e2d419a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/render_lottie.dart", "hash": "1f1d9ad31b94bddede62ba7d141256f9"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/search_bar_theme.dart", "hash": "055a5c4a10cb9bc9f1e77c2c00e4ef9a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/subjects/replay_subject.dart", "hash": "7929b4d3e79087536edc9cd260f8d4c7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/charcode.dart", "hash": "b2015570257a2a6579f231937e7dea0e"}, {"path": "/Users/<USER>/AndroidStudioProjects/Safescan/lib/screens/auth/register_screen.dart", "hash": "3ab094c31d571d9faa73d2aa17c7767b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camera_platform_interface-2.10.0/lib/src/types/focus_mode.dart", "hash": "abb2e92bce56af1b33c6ff3bee1ef235"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/sliver_fill.dart", "hash": "6987c3474a94dd1c4ff8f8540212f16b"}, {"path": "/Users/<USER>/AndroidStudioProjects/Safescan/build/app/intermediates/flutter/debug/flutter_assets/assets/fonts/SpaceMono-Regular.ttf", "hash": "49a79d66bdea2debf1832bf4d7aca127"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/parser/font_character_parser.dart", "hash": "3f70d92a42028d0b5cd83ad6bbb3ac9e"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/material_state_mixin.dart", "hash": "62cbf59e5c816c224ef5eaf803fc877b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/async_memoizer.dart", "hash": "abcb2d6facc18b2af070cb86cbb1c764"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/lib/sqlite_api.dart", "hash": "9442b7f8efe89c42cd235c4047480ce4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/forwarding/forwarding_directory.dart", "hash": "18b0559a8cbfb3b3a3d34bbbea4669c7"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/elevation_overlay.dart", "hash": "ea5bbc17f187d311ef6dcfa764927c9d"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/rendering/editable.dart", "hash": "eaed941ddb98b44c090d06e0be0a7562"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/utils.dart", "hash": "ebf21341320c02b09bfd8dcbfc683398"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/rendering/layout_helper.dart", "hash": "1fd7c932679011d491315ff136d13822"}, {"path": "/Users/<USER>/AndroidStudioProjects/Safescan/build/app/intermediates/flutter/debug/flutter_assets/shaders/ink_sparkle.frag", "hash": "8af2b6b4632d42b4e1701ecda1a3448a"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/services/browser_context_menu.dart", "hash": "db4a14227247e2524e46f6b0dd9da267"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sprintf-7.0.0/lib/src/sprintf_impl.dart", "hash": "2e7ac5275644c470359f8b69c555bfd1"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/menu_bar_theme.dart", "hash": "e4a748e0ab7265def948ce2f5dbce86e"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/services/keyboard_key.g.dart", "hash": "4f9995e04ebf5827d1352afca6adda26"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3/lib/src/box/box_base.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/storage/cache_info_repositories/json_cache_info_repository.dart", "hash": "f8113503c91cd843d744fa61b0b15ba6"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/animated_icons/data/close_menu.g.dart", "hash": "a0816d2682f6a93a6bf602f6be7cebe1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/result/error.dart", "hash": "a10eafbc71350955a16e4e787402780b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_barcode-7.4.1/lib/src/spark/spark_scan_settings.dart", "hash": "52848fbc6b778be2e213a4882f99cf5d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/state_controller.dart", "hash": "30b3454341d40c187ec21020db3a495b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/stream_sink_transformer/stream_transformer_wrapper.dart", "hash": "04d38c19b0c3dba61b730122d76ec4d4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.0/lib/src/printers/pretty_printer.dart", "hash": "bf2bc3af52875d3e5715ed2dff220c07"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/gestures/force_press.dart", "hash": "d3de616e525e730c7b7e3beb57930993"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/listenable.dart", "hash": "a5bfe2d6591e761bf3c5dc0cd4ded99a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/LICENSE", "hash": "9633ac2bb6bd16fe5066b9905b6f0d1c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/codecs/lzma/range_decoder.dart", "hash": "29597979eb10c767329562fc72cde9df"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/backpressure/buffer.dart", "hash": "15563ca80dc06490676ca80b6b98718f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/go_router-16.0.0/lib/go_router.dart", "hash": "94124aa8c115b3bc8553ba80c419ceeb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/codecs/gzip_decoder.dart", "hash": "bdcea16bdc5a229dc7f7cb80d9b38797"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/parser/moshi/charcode.dart", "hash": "b27709ee8e2ff325996e7f0828e04c7d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.0/lib/src/log_filter.dart", "hash": "32581c4e1ac594b374549efd0b5f46c2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_core-7.4.1/lib/src/logo_style.dart", "hash": "e5332716f31bc87ac4a1285996cff244"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/response.dart", "hash": "efbedb75be354b65520bce3f0855b8db"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/_web_image_io.dart", "hash": "e88b0574946e5926fde7dd4de1ef3b0d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/model/content/shape_path.dart", "hash": "d19745e36be0d4b4491e57dae257b150"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/utils/dash_path.dart", "hash": "27bf637010810e54bb977ad1946bc0f0"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/foundation/_timeline_io.dart", "hash": "90f70ffdd26c85d735fbedd47d5ad80b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/single_subscription_transformer.dart", "hash": "789cc727406d0343a1dddb5018570adf"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/floating_action_button_theme.dart", "hash": "08c3fd9ed1607d3a707ffe9b3532218a"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/desktop_text_selection_toolbar_layout_delegate.dart", "hash": "c679063104d2f24639459c8ab3eed77a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/parsing.dart", "hash": "16d4d82628956a3b88ae5de8480aae49"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/focus_scope.dart", "hash": "fddd73db94bb2fa3a0974bed845f32a8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/memory/memory_file_system_entity.dart", "hash": "c69896f9c186aab01f7d11624f5c7d4a"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/container.dart", "hash": "f663757bacdc28f2692b30a293d75146"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3/lib/src/registry/type_adapter.dart", "hash": "ed743446165700520a88ccc56514877d"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/foundation/debug.dart", "hash": "1286926784ce0908d414d696a6321e9f"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/platform_menu_bar.dart", "hash": "44d59e37041b6305018f70012fef7d52"}, {"path": "/Users/<USER>/AndroidStudioProjects/Safescan/build/app/intermediates/flutter/debug/flutter_assets/assets/images/adaptive-icon.png", "hash": "330ee2d0bca582f846798f5ba8c3fb16"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/navigator_pop_handler.dart", "hash": "0d1b13fd16692571d5725f164d0964ef"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/rendering/debug.dart", "hash": "9f05403438068337dd8f3433d2757535"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/navigation_rail.dart", "hash": "2936420e0c8ddba21d283d969f5147d6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3/lib/src/object/hive_collection_mixin.dart", "hash": "3acf14588aeccbac8c5d9e50e5db9edb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_platform_interface-4.3.0/lib/src/permissions.dart", "hash": "1addb41b1ec88a6b5674bd3486e9e225"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/services/system_navigator.dart", "hash": "0db5f597f1cc6570937e6c88511af3a9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/config/_config_io.dart", "hash": "7d313ac68ec3f410b31e39f450fdaf0f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hive_flutter-1.1.0/LICENSE", "hash": "4329bcdd1ac50446158359963f9d3403"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/codecs/zlib/_zlib_encoder_io.dart", "hash": "e6c8acbe554eee8c1a1ee7afafa19513"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/localizations.dart", "hash": "9c051d9a4098051ba8258eae9aae3195"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_barcode-7.4.1/lib/src/ar/barcode_ar_info_annotation_body_component.dart", "hash": "9302d4e4825928024f0f8d426c8c4823"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/parser/animatable_transform_parser.dart", "hash": "9ab9fa30a0210a75f908f9e87a4a5b23"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_barcode-7.4.1/lib/src/pick/barcode_pick_state.dart", "hash": "5c31e605cf601abe88b684990533c6bb"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/painting/strut_style.dart", "hash": "ee62fb3be5d885d65054fac4b84cac6c"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/colors.dart", "hash": "f59aed120736d81640750c612c8cfe5c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/go_router-16.0.0/lib/src/logging.dart", "hash": "5872689884d3985685f0239a1f89f71f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/provider/auto_dispose.dart", "hash": "a3250d5fb60cc8b17997c886a67be737"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/typed/stream_subscription.dart", "hash": "63190b810e77cfebf3de760baaf59832"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/util/aes.dart", "hash": "a8949b668376a6f80053e689503f40c7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/util/ram_file_handle.dart", "hash": "cc9eeca84124af552ad4fe888cb66f9d"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/carousel.dart", "hash": "7270419a025fdbf7840e542397db0c5b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/with_latest_from.dart", "hash": "eab456316eb52f6f668d1dd2800c1085"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/button_theme.dart", "hash": "7b0e6dd1794be4b575ecf8af6475f0e7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/value/lottie_relative_double_value_callback.dart", "hash": "20557a2aae15fe56bdc21e851da0d315"}, {"path": "/Users/<USER>/AndroidStudioProjects/Safescan/lib/routes/app_router.dart", "hash": "2d01a0bce80f8e619c1abe90886d44ed"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/lottie_image_asset.dart", "hash": "42675b8009a88ba7debefece2ea4af34"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hive_flutter-1.1.0/lib/hive_flutter.dart", "hash": "ed6800e3fdfd2d724c29415c77a47dc4"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/cupertino/form_row.dart", "hash": "5f64d37da991459694bce5c39f474e5f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/take_while_inclusive.dart", "hash": "389552e6852c3214ca6857ddadb7cd0b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/async_notifier/family.dart", "hash": "18d9d372c2f7421114cc2a2df21d8206"}, {"path": "/Users/<USER>/AndroidStudioProjects/Safescan/assets/animations/RippleLoadingScreen.json", "hash": "9f3a71f9bc8304dab18ad509c2a5cb85"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/framework/scheduler.dart", "hash": "1ac1f41185397129f7ea925130f188f2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3/lib/src/box/box_impl.dart", "hash": "********************************"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/physics/utils.dart", "hash": "727e4f662a828d4611c731f330a3d79a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/start_with.dart", "hash": "46c6500112cb203a46608825824d4d52"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_for_web-3.0.6/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_barcode-7.4.1/lib/src/capture/barcode_capture_function_names.dart", "hash": "0ec1cb6d5ae36c61b34e51423905fe45"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/scrollbar_theme.dart", "hash": "b3019bcd49ebc4edd28b985af11a4292"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/cupertino/constants.dart", "hash": "92e6028556e74c1dc297e332b473f78e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/parser/shape_data_parser.dart", "hash": "4e1818450974d32fe59917356c2fbdea"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_barcode-7.4.1/lib/src/count/barcode_count.dart", "hash": "60fd3d5394f755ff06fc5f7a48faabda"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/synchronized-3.4.0/LICENSE", "hash": "8f29b74ba6fa81721ca1cd98cd39ae4d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/backpressure/throttle.dart", "hash": "12faaaa2952e6917c271f5dbe9cd6bab"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_core-7.4.1/lib/src/scandit_icon.dart", "hash": "b8a2c2403b6646ef34c401e824a9e9e0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/go_router-16.0.0/lib/src/misc/errors.dart", "hash": "8cbd679f40c3f8e0bd00dbbd6bfb8f79"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/gestures/pointer_router.dart", "hash": "8c1a2c1feaeb22027ba291f1d38c4890"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_barcode-7.4.1/lib/src/batch/barcode_batch_advanced_overlay.dart", "hash": "ef7adde4021acd6f538a2d35bc7a2b31"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/realtime_client-2.5.1/lib/realtime_client.dart", "hash": "c7af42a6d167113f703f45edbfd83639"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sprintf-7.0.0/lib/src/formatters/float_formatter.dart", "hash": "9193766efadfc3e7be3c7794210972ce"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_barcode-7.4.1/lib/src/aruco_marker.dart", "hash": "9c6cafb3add6f930fd5026399b56089e"}, {"path": "/Users/<USER>/AndroidStudioProjects/Safescan/lib/screens/search_screen.dart", "hash": "3a59d3cc6cfb79435adcfb381e084f52"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/codecs/zip/zip_file_header.dart", "hash": "c104b5911865a0de1f2005e74b5b3a8e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3/lib/src/hive.dart", "hash": "3e6bacd9c2e1cc522a82a8b3a3c7f713"}, {"path": "/Users/<USER>/AndroidStudioProjects/Safescan/lib/main.dart", "hash": "5f94564a2e683057910969f71b6b7a89"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_barcode-7.4.1/lib/src/count/barcode_count_status.dart", "hash": "ae7b4c784c7c1cdb2a5a2c6c233ba1ac"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/pop_scope.dart", "hash": "0ff55be19444856c892e701c475b20f6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/utils/utils.dart", "hash": "f45f2ce376864eec92f00cdcdf395727"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_platform_interface-2.6.2/lib/src/types/x_type_group.dart", "hash": "826066d6663c91c94cee09406ded70be"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/cupertino/page_scaffold.dart", "hash": "805f831d339e4ab9e6b172b2bf845809"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/synchronized-3.4.0/lib/src/lock_extension.dart", "hash": "92197f660f809dbb94c7d3d67b9f24e0"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/rendering/shifted_box.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/raw_lottie.dart", "hash": "88aacb1042b1e746527adc2b242bfa62"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/gestures.dart", "hash": "55324926e0669ca7d823f6e2308d4a90"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/realtime_client-2.5.1/lib/src/types.dart", "hash": "1a7fa5acec7ef90ae422f324dbdc194f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_spinkit-5.2.1/LICENSE", "hash": "4a6a9b806b898dc68a1f890ad1564a10"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/foundation/memory_allocations.dart", "hash": "c7c757e0bcbf3ae68b5c4a97007ec0b9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/quaternion.dart", "hash": "698a6fc4361dd42bae9034c9c2b6cf7b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/aabb2.dart", "hash": "f8fb1733ad7ae37b3d994f6f94750146"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.12.1/lib/src/utils.dart", "hash": "d84ae47a3c688bd889f442426f39be3e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6/lib/src/interface/local_platform.dart", "hash": "9cc2170ec43e47681be6cb2a313ba1b5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/codecs/zlib/_zlib_encoder_web.dart", "hash": "35048df4754d9702c3b749726a30a723"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_core-7.4.1/lib/src/direction.dart", "hash": "eaabf41eb701f161c1a09fc60f00fcbd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/common/env.dart", "hash": "f23b1cec674b4863aec7961f4a2ae758"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/realtime_client-2.5.1/lib/src/message.dart", "hash": "40caf5f2fc3a8845d468edb7a25a1a17"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/LICENSE", "hash": "5bd4f0c87c75d94b51576389aeaef297"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/utilities.dart", "hash": "********************************"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/text_selection_toolbar_layout_delegate.dart", "hash": "82604e7dbb83dc8f66f5ec9d0962378b"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/scrollbar.dart", "hash": "a2d1c7bec7b52901761f3d52a1ac02d5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/start_with_many.dart", "hash": "1f04f05279660e26d85fff2f5dfec4c3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/subscription_stream.dart", "hash": "45f0e675fa74d765bee71cf2c553bd58"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_barcode-7.4.1/lib/src/selection/barcode_selection_defaults.dart", "hash": "6f2a95a4d6dd702ed35689005c104d48"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/painting/box_shadow.dart", "hash": "********************************"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/foundation/key.dart", "hash": "3ee6304161ca2993b303a8074557fe66"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/services/text_boundary.dart", "hash": "501bafdb6d3784f18f395d40dfa73cd2"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/two_dimensional_scroll_view.dart", "hash": "28e91fd9077820e2cb2eb981471636ca"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/util/adler32.dart", "hash": "23941d12ba026bec23a4801d44728d64"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.0/lib/logger.dart", "hash": "610f4d6fd60c125e08d766985d536d52"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/reorderable_list.dart", "hash": "a101af17dcc01da8f97ef55242f0f167"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/foundation/constants.dart", "hash": "c7cc72c1e40d30770550bfc16b13ef40"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/parser/merge_paths_parser.dart", "hash": "7ca288fa506b6c8ae5bed46eb3d0f072"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/result/release_sink.dart", "hash": "e2f7d6fbeb362176a24cb422a6dd8193"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/connectable_stream.dart", "hash": "fcbda87916b8b2c4f7b88460ac9bb415"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/viewport.dart", "hash": "68eb8647107febe1419211e153b27a54"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/parser/content_model_parser.dart", "hash": "0a56b87777b62a6ecc745dd109a9d457"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_barcode-7.4.1/lib/src/selection/barcode_selection_feedback.dart", "hash": "55fba88b8a30bc85503d446dc2272bd8"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/physics/tolerance.dart", "hash": "43ef2382f5e86c859817da872279301e"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/painting/gradient.dart", "hash": "2bc2f148be8fffe5f3a6a53fe8bc8333"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/cache_managers/default_cache_manager.dart", "hash": "8ad6f50f623fbd97c2aa23d86d3c22ad"}, {"path": "/Users/<USER>/AndroidStudioProjects/Safescan/build/app/intermediates/flutter/debug/flutter_assets/assets/animations/Safescan-Green.json", "hash": "98c2450113e39fdd31f46c96a27a41d7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/storage_client-2.4.0/lib/src/storage_bucket_api.dart", "hash": "6b7deed47015fe6424b4b01c081751f0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/mime-2.0.0/lib/src/mime_multipart_transformer.dart", "hash": "531d1d96bce7aa59a6109c02ac538cb0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/providers/network_provider.dart", "hash": "2e82b723d16996f4726a42e2c32bfd84"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/parser/json_utils.dart", "hash": "30357f617a6bbdb4352a3064a35fd042"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/memory/memory_link.dart", "hash": "92be3b74ebf2b10ee5852ddbbc825971"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.17/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web_socket-1.0.1/lib/src/web_socket.dart", "hash": "4a734a1799a783075eeefe2296a393bf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/stream_channel-2.1.4/lib/src/guarantee_channel.dart", "hash": "68928ed10e0a0df76e1e28584b7256c1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/LICENSE", "hash": "3b954371d922e30c595d3f72f54bb6e4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/equality_set.dart", "hash": "4b5d82ddeb09bc46ae0e980616ce0109"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/delegate/stream_subscription.dart", "hash": "e2d2090c2a39f7902893e64150fe82b9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/storage_client-2.4.0/lib/src/version.dart", "hash": "c02f72380d6bb0a8033ef2c18426923c"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/dual_transition_builder.dart", "hash": "c06267b6c315a5e40f28feb6019de223"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/util/crc64.dart", "hash": "5ea15c157f8f55f47d832255a3b73cc0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_linux-2.4.1/lib/shared_preferences_linux.dart", "hash": "492280af61b4bca29e21d28db0c2be1c"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/animated_size.dart", "hash": "91d8303ca1ccc72eccc1ae636c7825ed"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_barcode-7.4.1/lib/src/scandit_flutter_datacapture_barcode.dart", "hash": "b5cbc11d7a0ea7c176065a141d85145b"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/animated_icons/data/list_view.g.dart", "hash": "f8275b74f8f83272b8a8d1a79d5b2253"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/codecs/tar_decoder.dart", "hash": "46621d446bf233db720f709328a20791"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/parser/shape_fill_parser.dart", "hash": "33f7cd000f689ee5f61165de9364b2f5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/stream_sink_completer.dart", "hash": "2430a12d4750c3c76ef07d29bb6f6691"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/vector2.dart", "hash": "6a0fa6360b3aca8deb85dc7d88176eb8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/archive/archive_file.dart", "hash": "01fee2f811b7ea870c1d9f2ceb72061b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/forwarding/forwarding_file_system.dart", "hash": "c23a0415bdaf55efdf69ac495da2aa9b"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/adapter.dart", "hash": "e05529d31a09e4c86cde70483824fa10"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_macos-0.2.1+2/lib/image_picker_macos.dart", "hash": "0f0fc7bc5c7c2c1581ed2ed245baf136"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/animation/keyframe/drop_shadow_keyframe_animation.dart", "hash": "07e8f3178f316303548c0825175ccc04"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/material_state.dart", "hash": "245a31a30063b63cbfd631fdc2ddf0d8"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/rendering/object.dart", "hash": "ff7346c41b21457ac3ed3c623e6d9d26"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_dotenv-5.2.1/lib/src/dotenv.dart", "hash": "fff38fbee67e2f21ac8b871431f3f5aa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/animation/content/gradient_stroke_content.dart", "hash": "958d65a52616c7c736aa8fd4e061dede"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/utils/notification.dart", "hash": "b9609815ffdb79eb76f2cd3a77a60906"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/gotrue-2.13.0/lib/src/types/user.dart", "hash": "a317d31f20d4b805246240b51c161d56"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_android-6.3.16/lib/src/messages.g.dart", "hash": "07d545e5e568302b0453b8848be6a678"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/scheduler/priority.dart", "hash": "ac172606bd706d958c4fe83218c60125"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/codecs/zlib/_gzip_decoder_web.dart", "hash": "4e6086182f043506862ee6a35d360bae"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_barcode-7.4.1/lib/src/selection/barcode_selection_basic_overlay.dart", "hash": "1d233a81a36b01fab62a6264c8624825"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/foundation/service_extensions.dart", "hash": "920b63c794849c8a7a0f03f23314bbb1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3/lib/src/backend/storage_backend.dart", "hash": "60a867309ff4891239672ceeb021e4b5"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/rendering/texture.dart", "hash": "7c07d5cc739ae29abcfbf6343ae84fdf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/quantize/quantizer_wu.dart", "hash": "c0da8171c63f0ab4e822dd094fc2c595"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences-2.5.3/lib/shared_preferences.dart", "hash": "698b47b813b0194cf3adacff5906a585"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/painting/geometry.dart", "hash": "9e353a749332f6cfdbe6f0d07ff17f5f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/hash_sink.dart", "hash": "ec5409b8e30f22b65a7eee1b00a12d06"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/realtime_client-2.5.1/lib/src/version.dart", "hash": "7ab8567e38ece2870c93c382e7227c2a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_linux-0.9.3+2/lib/src/messages.g.dart", "hash": "d631809a6f4e20b7aa9ea7e17a6581de"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/go_router-16.0.0/lib/src/route.dart", "hash": "10f5d74dc11bcc034d27aaee1c7b30b8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler-11.4.0/LICENSE", "hash": "a086f9770acbfc6a5e421b49411d9415"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/algorithms.dart", "hash": "0976264b99a1702a5d74e9acb841b775"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/gtk-2.1.0/lib/src/gtk_application.dart", "hash": "0855bca32d598f18148a3d123c552084"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/temperature/temperature_cache.dart", "hash": "a6350a577e531a76d89b24942fca3073"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/dynamiccolor/src/tone_delta_pair.dart", "hash": "f5b38c21bf580c89610a8b58c65aae00"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/stream_group.dart", "hash": "d16df8af6c029bc5e12bedcb2d9ed464"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/src/platform/platform_io.dart", "hash": "bb7e4bee2f9cca7b7e771e5ff413108d"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/ink_well.dart", "hash": "38df6f8cafb853c1acf0f6e6a4b4950c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_riverpod-2.6.1/lib/src/internals.dart", "hash": "6683b2c06b0ec964286b1a54f7e2803f"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/drawer.dart", "hash": "f26e2cb53d8dd9caaaabeda19e5a2de3"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/back_button.dart", "hash": "035b8d3642fa73c21eafbee7851cc85d"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/gestures/pointer_signal_resolver.dart", "hash": "28d3a26c44687480bac3f72c07233bf6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/functions_client-2.4.3/LICENSE", "hash": "bb0a4b2e3d82de4116e8425de9a3927f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/utils/string_utils.dart", "hash": "603b7b0647b2f77517d6e5cf1d073e5a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/local/local_directory.dart", "hash": "62da8696885bd25977675ac4f7f1aef9"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/modal_barrier.dart", "hash": "830b9f37313c1b493247c6e7f5f79481"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/util/input_stream.dart", "hash": "f4de2ef5869ce0806f1e4f449d80e390"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/animation/content/polystar_content.dart", "hash": "a78b5071c880a010954671320d7afc13"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/list_extensions.dart", "hash": "9f8b50d98e75350b41d40fee06a9d7ed"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/providers/lottie_provider.dart", "hash": "51d2e44af27529faded3d70850339c02"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/layout_builder.dart", "hash": "f5dab330de9938d8ad99263892810f3d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.0/LICENSE", "hash": "7e84737d10b2b52a7f7813a508a126d5"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/foundation/licenses.dart", "hash": "c0cf85f80b79542d2b0e1a00547d7310"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/scrollable.dart", "hash": "c8260e372a7e6f788963210c83c55256"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/card.dart", "hash": "90d9d45eef80ac53b194a71da4e10975"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/notifier/auto_dispose_family.dart", "hash": "c8b1bc30159a132cab814de0d71e0462"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_barcode-7.4.1/lib/src/selection/barcode_selection_session.dart", "hash": "cc5c43a181340c11b97aa4c6cf33cfef"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_barcode-7.4.1/lib/src/spark/spark_scan_defaults.dart", "hash": "f2e9aea06dd00c7efbc92d89fa688163"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/cupertino/text_selection.dart", "hash": "0c46b12a4e0301a199ef98521f0ed3ab"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/gotrue-2.13.0/lib/src/constants.dart", "hash": "90630fbff8ee015acc6316798477dcc3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/src/chunked_coding/charcodes.dart", "hash": "a1e4de51bdb32e327bf559008433ab46"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/sqflite_logger.dart", "hash": "6745a4321f65340dc91faae80415984b"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/context_menu_controller.dart", "hash": "c3ccb5b6cd3df44e6587a4f04dd6a4e7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_barcode-7.4.1/lib/src/selection/barcode_selection_freeze_behaviour.dart", "hash": "1fc80748b4ffe3130b1c243f5aba823e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/codecs/zlib/_zlib_decoder.dart", "hash": "0d832054a2b5c2f650f959ee311fad4a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_windows-0.9.3+4/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3/lib/src/box/default_key_comparator.dart", "hash": "********************************"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/menu_style.dart", "hash": "e79db1a382e61436ed81f9f47dc06d7a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/memory/memory_file.dart", "hash": "d9343422c8a6829bd05698de67232591"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/toggle_buttons_theme.dart", "hash": "262d1d2b1931deb30855b704092d3cb4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.4.0/lib/typed_buffers.dart", "hash": "4b495ff6681b3a7dda3f098bf9ecc77d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/state_provider.dart", "hash": "d28de61067df9bc3d509be84deec1140"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/matrix2.dart", "hash": "7f164e577cfcf8c8295947195cde2a7c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_core-7.4.1/lib/src/text_alignment.dart", "hash": "12b6e2bb403ab8305e444259b30e4a32"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/elevated_button.dart", "hash": "c2dcf2bcdc85d007f9729621d13cccf4"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/checkbox_list_tile.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_android-12.1.0/LICENSE", "hash": "a086f9770acbfc6a5e421b49411d9415"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/toggle_buttons.dart", "hash": "64a2ea17e8058aec30096102af030f98"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/materialize.dart", "hash": "7787d9ce2aed834062cd38b022824d31"}, {"path": "/Users/<USER>/AndroidStudioProjects/Safescan/build/app/intermediates/flutter/debug/flutter_assets/assets/images/SafeScan-logo.png", "hash": "c5ca992a99cf4b124dff541ffa75ac79"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/stream_transform-2.1.1/lib/src/merge.dart", "hash": "b011c1c4f56d867e644def305d52ba88"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/crypto.dart", "hash": "3b0b3a91aa8c0be99a4bb314280a8f9b"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/expansion_tile_theme.dart", "hash": "045c779ec8564825d7f11fbbd6fb2fa1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/dynamiccolor/dynamic_color.dart", "hash": "7ffb6e525c28a185f737e3e6f198f694"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/merge.dart", "hash": "c2b88768bdc9704848019fd9df8c2546"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_platform_interface-4.3.0/LICENSE", "hash": "a086f9770acbfc6a5e421b49411d9415"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/toggleable.dart", "hash": "33ce088a133276cbfd4a33ec49bdcb62"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/utils.dart", "hash": "4be4ccf244c18d2246d210e716283c2c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.2/LICENSE", "hash": "175792518e4ac015ab6696d16c4f607e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.0/lib/src/outputs/memory_output.dart", "hash": "54d0bd1fab938813ce3076758ba7a1cc"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/drawer_theme.dart", "hash": "62b4a318d3ec0d03d3dc78b84cf0458a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3/lib/src/annotations/hive_type.dart", "hash": "b26d0a2e3e209b52ffb697f829ec46cc"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/flutter_logo.dart", "hash": "985cf5499dc6e521191985f55245a22c"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/tap_region.dart", "hash": "96b4be28e9cb48156c65de35d7ccefba"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.0/lib/src/ansi_color.dart", "hash": "2008a57b1ec04a349e6e8c7563f41418"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camera_android-0.10.10+4/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/web/web_helper.dart", "hash": "60db0a181494c7db06a18464e2d6e796"}, {"path": "/Users/<USER>/AndroidStudioProjects/Safescan/build/app/intermediates/flutter/debug/flutter_assets/packages/cupertino_icons/assets/CupertinoIcons.ttf", "hash": "b93248a553f9e8bc17f1065929d5934b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3/lib/src/util/extensions.dart", "hash": "a9e0df3a9079b0f6b5041cf4d901f932"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.0/lib/src/outputs/stream_output.dart", "hash": "b0ad7758ab1a2dc1b0b8bd30c1978d47"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.0/lib/src/date_time_format.dart", "hash": "a2aff0416ed5e953933c559720b669a0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/model/font.dart", "hash": "03f165dc45374d5f683116494f6868de"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.2/lib/src/clock.dart", "hash": "84ad21db5ba97deb809b65697546e39c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/src/logger/sqflite_logger.dart", "hash": "ab42e582c15854ab187bc7a07fb8baa5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/src/types/media_selection_type.dart", "hash": "dd685f95d5588b8d81d3913338ab9cd2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/synchronized-3.4.0/lib/src/basic_lock.dart", "hash": "25057894002e0442750b744411e90b9c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/supabase_flutter-2.9.1/lib/src/local_storage_stub.dart", "hash": "5a9658f1b0b2c6364b554de0a0e2d6ab"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/list_wheel_scroll_view.dart", "hash": "f500fac00bc25f66e6f49f5ca6de723a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/codecs/tar/tar_file.dart", "hash": "ebedf04f089c991b3403af4cd67cd66c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/mime-2.0.0/lib/src/mime_shared.dart", "hash": "c2f30f0829e63ccf0449de5982e324b4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/synchronized-3.4.0/lib/src/reentrant_lock.dart", "hash": "7cff949e3b7ac960b63441117b2f6734"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_windows-3.1.4/lib/src/messages.g.dart", "hash": "bee9a89328e73d06f9b915e157deffe1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/utils/color_utils.dart", "hash": "0938e0447f447ceb7d16477a0213ce2c"}, {"path": "/Users/<USER>/AndroidStudioProjects/Safescan/lib/config/scandit_config.dart", "hash": "9a71908755f297f3dd093f3c2c0ba767"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/model/content/merge_paths.dart", "hash": "1eaa494279d671436819228ef3238a15"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_riverpod-2.6.1/lib/src/consumer.dart", "hash": "f28a95b717859fa14ea8344e766e7fb0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/score/score.dart", "hash": "58b9bc8a40fd3e2f7d9d380d0c2d420f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/src/value_utils.dart", "hash": "c112ad2acb33c46fcd09f4f2b7d2675e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/util/file_access.dart", "hash": "ca4057bd5bba32dea4d553dfb7eb2f8b"}, {"path": "/Users/<USER>/AndroidStudioProjects/Safescan/lib/services/gemini_service.dart", "hash": "e0105a62b5e2b7236933b2b44cfc7795"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/postgrest-2.4.2/lib/src/postgrest_transform_builder.dart", "hash": "06dc981b022ce640af67a2a005abd614"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3/lib/src/box/lazy_box_impl.dart", "hash": "********************************"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/foundation/diagnostics.dart", "hash": "5d7b0ee48c302285b90443514166c2d2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/cache_managers/cache_managers.dart", "hash": "d4a2cc62bec6dff9fcdc94bc868ea014"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/group_by.dart", "hash": "6c3232594edbc47bd6ec36d04c194a9a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/LICENSE", "hash": "d2e1c26363672670d1aa5cc58334a83b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/web/file_service.dart", "hash": "857464ce6f576c4020591d501ebcaaa7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/stream_extensions.dart", "hash": "903d8536aa6c9e6926e96e9a2b449824"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/providers/memory_provider.dart", "hash": "4bc3266b4c67e2f92256f98b879f5f3a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/lib/src/string_scanner.dart", "hash": "f158ffadca730ab601c60307ba31a5e4"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/segmented_button.dart", "hash": "ad631d7cd122efc4862c1c084fbde716"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.4.0/lib/src/typed_buffer.dart", "hash": "f64837679a1abb526e942b166db5c244"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/text_editing_intents.dart", "hash": "47ccb32c843b4075a001e612853b2a31"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/badge_theme.dart", "hash": "e1a148a465b713a6366d5a22a1425926"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.2/lib/src/stopwatch.dart", "hash": "f38a99a51f4062e7861bb366f85265d5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/lib/src/line_scanner.dart", "hash": "168bedc5b96bb6fea46c5b5aa43addd1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/lib/path_provider_windows.dart", "hash": "38dc31b8820f5fd36eedbf7d9c1bf8d9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher-6.3.2/lib/src/url_launcher_uri.dart", "hash": "3cb04add978cf19afa2d0c281e4c80b2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_core-7.4.1/lib/src/control.dart", "hash": "3d905b7f7fa3a9f90a2d4676663a88a4"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/context_menu_button_item.dart", "hash": "5061e0737e2db44e82d8a8c12f328a48"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/util/output_file_stream.dart", "hash": "d48cdb6d5fe440b51fad4b4832a9df57"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/parser/offset_parser.dart", "hash": "cd5f258543231f623e78a29df6b9c779"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/rendering/sliver_persistent_header.dart", "hash": "ffa4f7b2d5b1caccc05cf4b64021ae5e"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/rendering/platform_view.dart", "hash": "a8513860b3b4c160b57ca6264bc0acf8"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/page_transitions_theme.dart", "hash": "91f73f40856927e688e1707a923db3e2"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/shared_app_data.dart", "hash": "feacc941aea1ec8b3a30601915b7d353"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/lottie.dart", "hash": "c68dfebd72dbd4781697f82eb6dd64d1"}, {"path": "/Users/<USER>/AndroidStudioProjects/Safescan/lib/models/product.dart", "hash": "2c36e7c00161d0930560092fbac7f42a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/parser/color_parser.dart", "hash": "5bc86982e66a2449f8bbefcf1e5aeeb6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/model/layer/image_layer.dart", "hash": "4ecec88e88af9747d67cc451c4bfb004"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/value/drop_shadow.dart", "hash": "0bf9c74af76453af61710464f29a7992"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/foundation/assertions.dart", "hash": "d77516b410bc8410c6128cb39240acdb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/hct/viewing_conditions.dart", "hash": "cb0d5b80330326e301ab4d49952b2f34"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/stream_channel-2.1.4/lib/src/multi_channel.dart", "hash": "51eb44211d0dcb7fd159fd3232f15135"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/stream_notifier/auto_dispose_family.dart", "hash": "0dd5377006ddfc0b63c193276ef02d43"}, {"path": "/Users/<USER>/AndroidStudioProjects/Safescan/build/app/intermediates/flutter/debug/flutter_assets/assets/animations/Cat-Loading-screen.json", "hash": "e662a121ea9c665091b1c324330bd7a0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/rendering/decorated_sliver.dart", "hash": "cd7f8dc942f5138db121aabbaba920ac"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/framework/foundation.dart", "hash": "f594087d1804ddc538f758c0059eb6da"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3/lib/src/crypto/aes_cbc_pkcs7.dart", "hash": "93042b4972c8255fa75112f440f77aea"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_linux-3.2.1/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/icon_theme_data.dart", "hash": "ae1f6fe977a287d316ee841eadf00c2b"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/search_anchor.dart", "hash": "873f01c9dae2d98c8df6fc08ca543aca"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/services/font_loader.dart", "hash": "a29f0df228136549b7364fcae4093031"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/rendering/mouse_tracker.dart", "hash": "0c402ad9ba3f3e4d7f45f24b27447ec2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/gotrue-2.13.0/lib/src/gotrue_admin_mfa_api.dart", "hash": "edddef46b98a557e12ff8602070087a7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/streamed_request.dart", "hash": "c738f304008379170f7306e4368d29dd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/realtime_client-2.5.1/LICENSE", "hash": "bb0a4b2e3d82de4116e8425de9a3927f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/codecs/zlib/_zlib_decoder_io.dart", "hash": "91da029800f179c428186ec4fb7b4df7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/painting/placeholder_span.dart", "hash": "d2386b256656121d501a16234b008e2b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/src/method_channel/method_channel_image_picker.dart", "hash": "13b37731f32d54d63ecb4079379f025b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/vector.dart", "hash": "6a67d38bafe568f1b4047286d586fbbc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_barcode-7.4.1/lib/src/barcode_filter_settings.dart", "hash": "93fb92e1124da6d2f1d0e90a97da4111"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/services/asset_manifest.dart", "hash": "a2587417bcfd04b614cac5d749f65180"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/parser/mask_parser.dart", "hash": "e30701b360c0fd76c2369ab45f5fd74f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.2/lib/src/generated/ascii_glyph_set.dart", "hash": "7050c8c94b55eb51260ca54708b460fa"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/gestures/events.dart", "hash": "89aeee125822690cbd46b2ff43c76ec1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/empty_unmodifiable_set.dart", "hash": "0949b8197a6069783a78f4bb0a373fb0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/uuid_value.dart", "hash": "6edd9b910f41e28e574e1c5308ef8b74"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/util/encryption.dart", "hash": "1116d5040770ee0a85a84b5c86425796"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/postgrest-2.4.2/lib/postgrest.dart", "hash": "9ba56cacf16559e8e09529f6bd70c965"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/inherited_theme.dart", "hash": "7ebcf3ce26dea573af17627d822e9759"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/src/authentication_challenge.dart", "hash": "395f07418a28b12b0ed665f32270d702"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/synchronized-3.4.0/lib/synchronized.dart", "hash": "044e7c8ac3258945fe17e90e1a4fff51"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/model/content/rectangle_shape.dart", "hash": "b66987d8571ff0c84021547d0adec218"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/stream_transform-2.1.1/lib/src/aggregate_sample.dart", "hash": "cd164203dbc14d5701b8940f084fd58c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/backpressure/backpressure.dart", "hash": "33135edc8fab501ab562a08b8322d832"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/cupertino/bottom_tab_bar.dart", "hash": "019f7b771f1865632d5a36c9e74296db"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/cupertino/spell_check_suggestions_toolbar.dart", "hash": "e4c4603e78131a8bc950a8029d624a76"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/date_picker_theme.dart", "hash": "3ab9652d1101aac3b5d74a4495d860ef"}, {"path": "/Users/<USER>/AndroidStudioProjects/Safescan/build/app/intermediates/flutter/debug/flutter_assets/vm_snapshot_data", "hash": "85aa53b038be894edc8ed4b952643c56"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/go_router-16.0.0/lib/src/router.dart", "hash": "d4b70b211f5016be44df4f0b02b8bbad"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/model/content/rounded_corners.dart", "hash": "8fb817c0342edc2711e60ba19ee8c808"}, {"path": "/Users/<USER>/AndroidStudioProjects/Safescan/assets/animations/Cat-Loading-screen.json", "hash": "e662a121ea9c665091b1c324330bd7a0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_barcode-7.4.1/lib/src/ar/barcode_ar_annotation.dart", "hash": "11aec03d6db9684bc7bf4b14eb3a5f0e"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/view.dart", "hash": "e758d8d6b65597325bd35b5dc769c7a2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/notifier/auto_dispose.dart", "hash": "a57c7d0bb0b0f3ff52fd48c953453bd4"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/services/debug.dart", "hash": "17fec0de01669e6234ccb93fc1d171f2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.4.0/LICENSE", "hash": "39062f759b587cf2d49199959513204a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/combine_latest.dart", "hash": "0c4028018783c732ca451e7fff693d3a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.0/lib/src/log_event.dart", "hash": "30c8223ffe2768eb8917d150bb063a8f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/postgrest-2.4.2/lib/src/postgrest_rpc_builder.dart", "hash": "0859effc50e798bf1540e5df20f60b34"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/parser/moshi/json_utf8_reader.dart", "hash": "f68d66ed39c676b33504c905aa29e1dd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/utils/mean_calculator.dart", "hash": "3389b22847c4800924db8c1e8b03d07d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/functions_client-2.4.3/lib/src/types.dart", "hash": "1475e9cffe1e352e6062d79162e348c8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/codecs/bzip2/bzip2.dart", "hash": "71965b290e7074ee69604217832a69aa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/app_links_linux-1.0.3/LICENSE", "hash": "d229da563da18fe5d58cd95a6467d584"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/web/mime_converter.dart", "hash": "601a4561a6a4b9a0f99cdc39dbb67c0a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/supabase-2.8.0/lib/src/supabase_realtime_error.dart", "hash": "58289ba4c41b10dde16a7384094a72a5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/combined_wrappers/combined_map.dart", "hash": "13c9680b76d03cbd8c23463259d8deb1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/codecs/zlib/_zlib_decoder_base.dart", "hash": "2a811cc0f24a2d57992cb0da1ecd1ec3"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/cupertino/theme.dart", "hash": "a02235e1a98989d6740067da46b4f73d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/lib/src/factory_impl.dart", "hash": "65614758273c0b82b4ce22b3728be36c"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/size_changed_layout_notifier.dart", "hash": "8a39bdc324d0ff25097784bd98333c08"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/framework/value_provider.dart", "hash": "d5a669dc5155cedc975db1022a570128"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/services/raw_keyboard_linux.dart", "hash": "2936a409e1029ec52f7c0003f4db18c4"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/user_accounts_drawer_header.dart", "hash": "bda2eeb24233fd6f95dc5061b8bf3dd5"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/input_border.dart", "hash": "2aec07fe4a1cd25aa500e5e22f365800"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/dropdown_menu.dart", "hash": "d110c5e3ee26058a3e9b4bba6440f15f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.20+2/lib/src/avfoundation_camera.dart", "hash": "e933d39f9e651fb383e5329860541a6a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.20+2/lib/src/type_conversion.dart", "hash": "d85a8df814eee3799472722f2c419a19"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/octo_image-2.1.0/lib/src/image/image_handler.dart", "hash": "a9ad1aa35c1b9117f15a379ef03480dd"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/cupertino/checkbox.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/parser/drop_shadow_effect_parser.dart", "hash": "a9ad1e26a7d401af99c2f8c02d36faa9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/local.dart", "hash": "e81341d4c5ee8dc65f89ae4145cf2107"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/parser/value_parser.dart", "hash": "85add34e1fc5c1afb33844aa64f5fd83"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/lib/src/span_scanner.dart", "hash": "87bcefcfff19652ad296ec7005799840"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/painting/notched_shapes.dart", "hash": "7821d01f98c559fcbec46a41b4df7ebf"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/bottom_sheet_theme.dart", "hash": "be66f00d2c9bb816f4236dd0f92bff55"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/ink_sparkle.dart", "hash": "204fb623e2b782051e9bcb6e320e97c0"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/filter_chip.dart", "hash": "0e13760edcb9f90f659ba77c144a3461"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/parser/animatable_text_properties_parser.dart", "hash": "0d84c00378e11a93d43922fe3687f9bd"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/icon_button_theme.dart", "hash": "ac317f8ed3b04bec644817e6f60a28d7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/supabase_flutter-2.9.1/lib/src/local_storage.dart", "hash": "57ab34f4ddce5511cc4d4ba6c9cbd0c3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_barcode-7.4.1/lib/src/ar/barcode_ar_info_annotation_anchor.dart", "hash": "f2922afab9d9c5c9d8ac10d08165fe62"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/utils/collection.dart", "hash": "5657a0d9394b698e2cf190aab3a342f6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/result.dart", "hash": "1325fce32c39a3792e3eeab612f942f1"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/foundation/synchronous_future.dart", "hash": "fb23ec509c4792802accd10fa7c8a6b0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/gotrue-2.13.0/lib/src/gotrue_mfa_api.dart", "hash": "05269ce84f08dad9ec26fb12a3bd34ac"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/notifier/base.dart", "hash": "a4eb00bf15ad2af7e8ef8480d7f26a29"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/dialog_theme.dart", "hash": "8383986e94be1a258a59af29b9217876"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/bin/cache/engine.stamp", "hash": "1378b09660e3aa2b28b9145b83771ecb"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/animation/animations.dart", "hash": "57d74766f36a3d72789bc7466ae44dba"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_barcode-7.4.1/lib/src/capture/barcode_capture_defaults.dart", "hash": "c81b48f30b5ee0fde2f8e3274597ea31"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/fork_join.dart", "hash": "848f74750b2ecf581969c7a0dd4c2c36"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/rendering/error.dart", "hash": "6cae6900e82c94905cc2aaefd806f8eb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/src/types/picked_file/io.dart", "hash": "2c21734ae994817f0963bcea30513c02"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/interactive_viewer.dart", "hash": "bb7bcb463df2ae0f5f952d439fdb384e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image_platform_interface-4.1.1/LICENSE", "hash": "6e15c47981e2e43ee17849a222e33b76"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/text_selection_toolbar_anchors.dart", "hash": "3fa7a3bafbab98c305119475eb004a06"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/model/font_character.dart", "hash": "70c7266d04a84716c1693094479cb3d6"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/visibility.dart", "hash": "94dab76e00a7b1155b15796b87ebe506"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fl_chart-1.0.0/LICENSE", "hash": "b3896c42c38a76b4ed9d478ca19593e4"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/data_table_source.dart", "hash": "094b2c03ad4e0ef5bc1144e281142b2e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/LICENSE", "hash": "22aea0b7487320a5aeef22c3f2dfc977"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/LICENSE", "hash": "57d76440fc5c9183c79d1747d18d2410"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_barcode-7.4.1/lib/src/count/requests/barcode_count_status_provider_result.dart", "hash": "0df8ee61719b54100fb071febcd2401d"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/text_theme.dart", "hash": "f60846aa76dab98607aa06c9bd6cf1dd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/src/sql_command.dart", "hash": "4e7b4cf98b7ea45960f7d79fffac5705"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/navigation_toolbar.dart", "hash": "5be90cbe4bbf72b0264413e4ccb5c275"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/memory/memory_directory.dart", "hash": "96ef4798e4cf4560148762dd71bd180a"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/painting/inline_span.dart", "hash": "e3127548d819af5ec9ecb10b5732b28e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/style/url.dart", "hash": "13c8dcc201f970674db72fbbd0505581"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/will_pop_scope.dart", "hash": "777aca422776ac8e4455ccc7958f7972"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image-3.4.1/lib/src/image_provider/_image_loader.dart", "hash": "af9339e8836ca91cbc9c8fd6b2de7cc6"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/cupertino.dart", "hash": "9b83fabf1193bf4967b740dd7a2c8852"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/foundation/persistent_hash_map.dart", "hash": "7e0e723348daf7abfd74287e07b76dd8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/model/animatable/animatable_value.dart", "hash": "889985aaf970868d7f519b6cfebb62d3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/aabb3.dart", "hash": "257ca4608e7d75f1db8d4c3ab710ac70"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/lottie_delegates.dart", "hash": "baae81f86bb603c2e7ea68e91e8108d4"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/foundation/change_notifier.dart", "hash": "ce666dc6b4d730d3cb07e6bfc64a8825"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/parsed_path.dart", "hash": "cb454929d7810d3ee5aa5fc28283d3fd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/stream_notifier/family.dart", "hash": "751c8376ab9bb4a866f5db6d7e6b864b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/run_guarded.dart", "hash": "ddefd207562d7e33dc44d433e0848e1d"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/services/platform_channel.dart", "hash": "78a0faeef5f0e801943acdca3f98393d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_platform_interface-2.4.0/lib/sqflite_platform_interface.dart", "hash": "beea47c079349d8e03b64a5a9dcbc7df"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/rendering/wrap.dart", "hash": "b656f459fa4dd04f817455858d3dd20f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/cancelable_operation.dart", "hash": "57ef1f2eff2168c2e2ba1c3e4e60e05a"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/animated_icons/data/add_event.g.dart", "hash": "a79a6f9bb06c7d6dc5fb74ac53dce31b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/queue_list.dart", "hash": "02139a0e85c6b42bceaf3377d2aee3de"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camera-0.10.6/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/cache_managers/image_cache_manager.dart", "hash": "69c7e246c8fb227cdabc8a3d9a8316dc"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/two_dimensional_viewport.dart", "hash": "7bdfcadf7dd131e95092d30909e5b11f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/async_notifier/auto_dispose.dart", "hash": "7c89e8d3e17b2ff04570b741ce311e44"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.12.1/LICENSE", "hash": "3c68a7c20b2296875f67e431093dd99e"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/cupertino/icon_theme_data.dart", "hash": "eca4f0ff81b2d3a801b6c61d80bc211c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.2/lib/term_glyph.dart", "hash": "1adcc56e3affffb23739c7c9d8a5fca0"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/painting/image_stream.dart", "hash": "8f1d7bd8be5bc9a71d3131f835abdb80"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/stream_channel-2.1.4/lib/src/json_document_transformer.dart", "hash": "eade7bbc9bacbd78204c7ffdde55ddbd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/switch_if_empty.dart", "hash": "f1236a5e582b3794b3fb2302d7297451"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/foundation/bitfield.dart", "hash": "d33374c0857b9ee8927c22a5d269de9b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/memory/common.dart", "hash": "33f949ceca0aa8895b2fa0ae289f42d0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/framework.dart", "hash": "d856ca958740bf8a240738ad9e9e69c2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.3/lib/url_launcher_ios.dart", "hash": "11803ff481a58d66000cbea8c68e2af4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/stream_transform-2.1.1/lib/src/combine_latest.dart", "hash": "abf3bd2ed039bc6a844d547e8039eae9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/octo_image-2.1.0/lib/src/progress_indicators.dart", "hash": "c2c2286fb7180be54cc4fd8b03ba9dea"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/stream_channel-2.1.4/lib/src/stream_channel_controller.dart", "hash": "7eb05cf7f12f7aa86e040f415a5a43be"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/cupertino/desktop_text_selection.dart", "hash": "05d4aeae6031730c6aa412a128f67448"}, {"path": "/Users/<USER>/AndroidStudioProjects/Safescan/lib/services/scandit_service.dart", "hash": "a16e168641570d33220a0597bfec75bc"}, {"path": "/Users/<USER>/AndroidStudioProjects/Safescan/.dart_tool/flutter_build/24601892b32eefa3e669ab6ab2e21a45/app.dill", "hash": "db20151ebaa49130ffe42dc5ba09775a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.12.1/lib/stack_trace.dart", "hash": "9a478fed4f2f15993c892e33f6fd766b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/src/platform_interface/image_picker_platform.dart", "hash": "38982dc702bc4583fd29314508a32c17"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/shadows.dart", "hash": "36fc598c656490ab430ca1be5fb909e8"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/motion.dart", "hash": "505f6c9750f9390c9e9e4d881092cef4"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/services/flavor.dart", "hash": "912b76b3e4d1ccf340ee3d2e911dfd28"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web_socket_channel-3.0.3/lib/src/channel.dart", "hash": "926894fd05495a4558aaf9e8f1da1451"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_platform_interface-2.3.2/lib/src/url_launcher_platform.dart", "hash": "0321281951240b7522f9b85dc24cb938"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/switch_latest.dart", "hash": "a52ae2e097914c25b04abb01abf02183"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/text_button_theme.dart", "hash": "becd419f96efe14f36f18a8c8adc82cd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/memory.dart", "hash": "647e49fd7e2b6707e82858420b630c46"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_barcode-7.4.1/lib/src/ar/barcode_ar_info_annotation_header.dart", "hash": "a4dd95d973194c54b470d05bdc3ab34e"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/image.dart", "hash": "caad40ad1936874ea93473b300bb909c"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/painting/basic_types.dart", "hash": "2346472ec1cfdb77f3b27d3b7af72d4c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/repeat.dart", "hash": "57ef315bc7f35da7e489915ef8572118"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_barcode-7.4.1/lib/src/spark/spark_scan_barcode_feedback.dart", "hash": "5a861293846ec4a297da430027386139"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.4/lib/ffi.dart", "hash": "ae66b0cbdfe2e2a5a99c5dfa48fd5399"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/parser/gradient_stroke_parser.dart", "hash": "f4f1774c407ce9fe4a3414e8dc307c9d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/stream_channel-2.1.4/lib/src/delegating_stream_channel.dart", "hash": "d731e1b690975788d014e6df127b2a9a"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/spell_check_suggestions_toolbar.dart", "hash": "12120b49ba363d4c964cf1d043a0aa1b"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/cupertino/magnifier.dart", "hash": "b56cf23d49289ed9b2579fdc74f99c98"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_barcode-7.4.1/lib/src/count/barcode_count_feedback.dart", "hash": "df050cb0f5de2bea00b4e4ede5d29ac0"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/cupertino/scrollbar.dart", "hash": "85cf42bafb7c0646bd7a99379649da29"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/subjects/subject.dart", "hash": "4b93fc559e6626b4d42e924b10c58678"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/animated_icons/data/search_ellipsis.g.dart", "hash": "c761b80666ae3a0a349cef1131f4413d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/stream_transform-2.1.1/LICENSE", "hash": "901fb8012bd0bea60fea67092c26b918"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/cupertino/sheet.dart", "hash": "e88cac3fc4dc6a17d2bd13549d433704"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/src/mixin/platform.dart", "hash": "b92ed901e8df2fde6d4739ed5e59051d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_core-7.4.1/lib/src/focus_gesture.dart", "hash": "a81085a1abb7eb5514185ee92a68872b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/providers/file_provider_io.dart", "hash": "6580f06935d6af08024c86e003771b71"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/selectable_region.dart", "hash": "81036c1ed2827ac1db9fee5a900f568d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/stream_sink_transformer.dart", "hash": "8117e1fa6d39c6beca7169c752319c20"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/lib/characters.dart", "hash": "fa2a57b3b873fb7db4b8b961735e4ca3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/supabase_flutter-2.9.1/lib/src/supabase_auth.dart", "hash": "f42f5bfec6a8df4d4c596878ba24c548"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/gestures/arena.dart", "hash": "5486e2ea9b0b005e5d5295e6c41ad3c2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.20+2/lib/camera_avfoundation.dart", "hash": "9d49f62361b0d2204c18bdeb0a34169c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/synchronized-3.4.0/lib/src/utils.dart", "hash": "1eb2fe31f2f21cce619f672c25b1e43f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/lib/src/compat.dart", "hash": "8a31d0709de6865d3f49374ab6bb274a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_riverpod-2.6.1/lib/src/change_notifier_provider/auto_dispose.dart", "hash": "39d249bfedd0655b147701ff81de4fa1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/utils.dart", "hash": "caf148b76c44a3f0f1bd6055ddbb8f5e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/go_router-16.0.0/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/request.dart", "hash": "817e03d87771f133aacbdef89c1e6fc9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_barcode-7.4.1/lib/src/barcode_filter_highlight_settings.dart", "hash": "be1a307ee07feafca32252998ce0e2cf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/animation/content/stroke_content.dart", "hash": "043080f7839c5cda7f2a22b8836ccc68"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/web/queue_item.dart", "hash": "937dad14a7958c57948525533b199296"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/image_icon.dart", "hash": "2610f7ca2c31b37ad050671aafbccdd9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/utils/utils.dart", "hash": "04f2a3236f9f0080d5571041a0cf3567"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/list_tile.dart", "hash": "8b20b418804c1d6e59afdfcae6e84728"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fake_async-1.3.3/LICENSE", "hash": "175792518e4ac015ab6696d16c4f607e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/lib/shared_preferences_platform_interface.dart", "hash": "59bb1cba1648db956dccb835713d77d8"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/cupertino/refresh.dart", "hash": "7d5bd66d61c58afe63c6d33ee0e421c1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/src/case_insensitive_map.dart", "hash": "5893c7d3910e8924bd2dccc8837775c7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/src/dev_utils.dart", "hash": "667c5e3e357a840e3d3a6137458c0c34"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/model/content/gradient_color.dart", "hash": "195d6ee9c761ba87090433a29244541a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logging-1.3.0/LICENSE", "hash": "d26b134ce6925adbbb07c08b02583fb8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/sqflite.dart", "hash": "5c96fe82a9bf2dc00db9d93c2c0a41a9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/stream_transform-2.1.1/lib/src/tap.dart", "hash": "2174cee3aa85b6a1cb77f1e9f1f54f7b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/dislike/dislike_analyzer.dart", "hash": "d7eb1678ec74acd9857a4193fd62ed5b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/hct/src/hct_solver.dart", "hash": "b972c32590c642256132827def0b9923"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/model/key_path.dart", "hash": "731df522db8656a51cdc9fe1a0806c2a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.12.1/lib/src/chain.dart", "hash": "1112185143b6fe11ce84e1f3653b2b6b"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/painting/oval_border.dart", "hash": "c8a14f8ecb364849dcdd8c67e1299fb3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_core-7.4.1/lib/src/scan_intention.dart", "hash": "2850d04e3461332c0ebb41b678fd471b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_platform_interface-2.6.2/lib/src/platform_interface/file_selector_interface.dart", "hash": "5937c2b1cbdf77126bc2dd93570d3c98"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/preferred_size.dart", "hash": "dd518cb667f5a97b3456d53571512bba"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_barcode-7.4.1/lib/src/barcode.dart", "hash": "c85328f576ac3b778a32f230257375a5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/memory/style.dart", "hash": "825ec1b2847bd00ad5cd840c7ddc4d6f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/source_span.dart", "hash": "9f2eb24284aeaa1bacc5629ddb55b287"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/scroll_aware_image_provider.dart", "hash": "d390b15ecef4289db88a4545e359bc8a"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/services/process_text.dart", "hash": "94235ba74c3f3ad26e22c4b40538ce07"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/path_exception.dart", "hash": "b062a8e2dade00779072d1c37846d161"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fixnum-1.1.1/lib/src/int64.dart", "hash": "da07db909ae6174095f95d5ee019d46c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/src/factory.dart", "hash": "a79e2b9a182eb762fadaab05e9269edc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences-2.5.3/lib/src/shared_preferences_async.dart", "hash": "255fd9cb9db57da2261cb7553da325ab"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/model/content/mask.dart", "hash": "5c2579dd97660d3d1470e231de82684c"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/ink_splash.dart", "hash": "31b0d2bf647a0ce615f4937dd5307b1c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camera_platform_interface-2.10.0/lib/src/method_channel/method_channel_camera.dart", "hash": "19c026f7ed2f8c625f1592c425545aa0"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/fade_in_image.dart", "hash": "b692d4a68a086507a66243761c3d21a6"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/implicit_animations.dart", "hash": "c9105f08cb965dfc79cdbe39f062d6c2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3/lib/src/box/keystore.dart", "hash": "********************************"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/widget_state.dart", "hash": "3c24303086312d7181ffa10d0521029a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/validation.dart", "hash": "af69b927cad3da3ff26f5e278d151304"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/app_lifecycle_listener.dart", "hash": "f77f6a903d346f842a7fe474e427d6a9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/model/content/shape_data.dart", "hash": "4a6e6f31c32134d9b30b77ef0bf465c0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/backpressure/window.dart", "hash": "b081e406a9e3448ff172ab7d21f31f7a"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/physics/clamped_simulation.dart", "hash": "5979a1b66500c09f65550fab874ee847"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_barcode-7.4.1/lib/src/spark/spark_scan_function_names.dart", "hash": "c4dd7203e00e5ceb81ee6a12cb02eabf"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/foundation.dart", "hash": "b4a0affbd6f723dd36a2cc709535c192"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/painting/box_decoration.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/result/release_transformer.dart", "hash": "45a20da2b86984fa0b29030dd190c75d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/meta-1.16.0/LICENSE", "hash": "83228a1ae32476770262d4ff2ac6f984"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/octo_image-2.1.0/lib/src/octo_set.dart", "hash": "0d750078c87ce8f99c60c3c76305c11a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/rng.dart", "hash": "d42791632fba8e51a8bc7535cee2d397"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/sha256.dart", "hash": "1b2339e719143f3b365a03c739ab3916"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web_socket-1.0.1/lib/web_socket.dart", "hash": "ae79620b36c1d588cb91f194bde8610c"}, {"path": "/Users/<USER>/AndroidStudioProjects/Safescan/lib/screens/login_screen.dart", "hash": "18372a965aae6a3f6a022ada0f25cc1f"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/animated_icons/animated_icons_data.dart", "hash": "ac08cb84358e3b08fc1edebf575d7f19"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_platform_interface-4.3.0/lib/src/permission_status.dart", "hash": "e644eae6cf851b3c46f83af266811a6e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/supabase_flutter-2.9.1/lib/src/version.dart", "hash": "ff39618f58f3c71efabf0640d52544db"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3/lib/src/box_collection/box_collection.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web_socket-1.0.1/lib/src/utils.dart", "hash": "95a1512a0fffd6fe7b489b94b4ac91e1"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/predictive_back_page_transitions_builder.dart", "hash": "cb745b78bdb964c02c1c4a843b9c1e7d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/render_cache.dart", "hash": "3004486dfb484efd2fc6ed74467c9ac8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image-3.4.1/lib/src/image_provider/cached_network_image_provider.dart", "hash": "8a55a3a014cc2ba2dea85787efc98ee4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/src/types/picked_file/picked_file.dart", "hash": "90a070dfee5777a4bca169be4bda3bb1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/stream_transform-2.1.1/lib/src/take_until.dart", "hash": "6c873115296f0c3c72777a00c58437c1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_android-2.4.1/LICENSE", "hash": "80ea244b42d587d5f7f1f0333873ad34"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/time_picker_theme.dart", "hash": "b269f9d6378b540b7d581db466ad98d3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_linux-0.9.3+2/lib/file_selector_linux.dart", "hash": "25c44b3908d2602e0df540ca5b17da27"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3/lib/src/object/hive_list_impl.dart", "hash": "6f02ecb5b09b8edd2a435707a8516cef"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_platform_interface-2.4.0/lib/src/sqflite_import.dart", "hash": "afa8ae229bc41c02a6cd9dcbe10a81e8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/src/batch.dart", "hash": "d88008fc349dd84def0654263c6d16be"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/state_provider/base.dart", "hash": "63b92eb56c14d5474db11677f1800c83"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/bottom_app_bar.dart", "hash": "fa60d1a6f81796232bc16dae4ed5f4ac"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/stream_channel-2.1.4/lib/src/disconnector.dart", "hash": "7732b755777af7281039ae8c5cb3eb78"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/page_storage.dart", "hash": "e5a3ca065f292c0f0b0cca0a55df41aa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/gtk-2.1.0/LICENSE", "hash": "9741c346eef56131163e13b9db1241b3"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/raw_menu_anchor.dart", "hash": "a749880c7b2c93609c79f05151beda3b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/memory/node.dart", "hash": "2f9772d14db922d3a41fb27f6b6382fd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/gtk-2.1.0/lib/src/libgtk.g.dart", "hash": "de89a41a7a225cbea0748a62fc498c57"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/stream_provider/auto_dispose.dart", "hash": "9eb3cf0f33c573aa9e8424441db78539"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/animation/keyframe/value_callback_keyframe_animation.dart", "hash": "3ef3078b8550ff75faef90ea2f22258c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_macos-0.9.4+3/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/forwarding/forwarding_file.dart", "hash": "58edba46526a108c44da7a0d3ef3a6aa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_barcode-7.4.1/lib/src/barcode_plugin_events.dart", "hash": "358137c32e29b5a6928a14a30482166e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_barcode-7.4.1/lib/src/count/barcode_count_settings.dart", "hash": "a64a509e5a68d54b6e3ad58118513135"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/restoration_properties.dart", "hash": "a8fdf31698b305c9fdad63aa7a990766"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/physics.dart", "hash": "6e29d5e69c5745a45214fe14da377c1a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_flutter_testing-3.0.9/LICENSE", "hash": "f721b495d225cd93026aaeb2f6e41bcc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fixnum-1.1.1/lib/src/intx.dart", "hash": "c3e3bdde1f486b799e08a1ed1b99c76a"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/cupertino/picker.dart", "hash": "4d8781c671b7df5aadf2331931458cfb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/yet_another_json_isolate-2.1.0/lib/yet_another_json_isolate.dart", "hash": "94e3929911d925c563bbe80fe4e2e231"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3/lib/src/box/change_notifier.dart", "hash": "********************************"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/services/system_sound.dart", "hash": "39f5f34a4d3615c180c9de1bf4e8dde8"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/gestures/constants.dart", "hash": "be94b8f65e9d89867287dabe5ea1dff1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/codecs/zlib/_zlib_encoder_base.dart", "hash": "43e54e569a35d13a3e93f4b586998401"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/expand_icon.dart", "hash": "d6008bafffb5b2e7bf16e59a9d3ad934"}, {"path": "/Users/<USER>/AndroidStudioProjects/Safescan/lib/widgets/loading_overlay.dart", "hash": "1507924bb681d3a42a09867099c934c8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/lib/src/extensions.dart", "hash": "38e17b28106d00f831c56d4e78ca7421"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/services/text_editing_delta.dart", "hash": "270de9c98f9c1284da0a6af9176ee1f9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.4/lib/src/allocation.dart", "hash": "9d62f4f58e8d63a8e106a1158eb13a02"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/model/layer/text_layer.dart", "hash": "c3987aab9db9a1ea0f29084ffb89ee31"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.0/lib/src/printers/simple_printer.dart", "hash": "178f62efb676bb0f4293df1f3f7beef7"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/action_chip.dart", "hash": "c7d65c476f653e952aedcb0cbcab3c73"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/LICENSE", "hash": "d53c45c14285d5ae1612c4146c90050b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camera_platform_interface-2.10.0/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/util/_file_handle_io.dart", "hash": "1c0ff741d83da7eddfbe5008868c0705"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_dotenv-5.2.1/LICENSE", "hash": "b93fe5bcbea09a4ba86ec85c6bb8baaa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/services/binary_messenger.dart", "hash": "056355e344c26558a3591f2f8574e4e5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_barcode-7.4.1/lib/src/pick/ui/barcode_pick_status_icon_style.dart", "hash": "0640e141f040ef1dd07f1f7d594e7b4c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/multipart_file_io.dart", "hash": "8830333c78de58ad9df05d396b651ef7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_core-7.4.1/lib/src/open_source_software_license_info.dart", "hash": "e548b1bd3023fe165a00d5254abed385"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/animated_icons/data/ellipsis_search.g.dart", "hash": "7018ea64a9aab18f27a10711285d7573"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/collection.dart", "hash": "4ba0a4163d73b3df00db62013fb0604e"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/slider.dart", "hash": "48a02b5ec3a8c6127b28927b5960d076"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/memory/operations.dart", "hash": "5dbef5156368d0f25b59750608e025a6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/end_with.dart", "hash": "f95bd67282cf610843bb37b5784f3eae"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/lib/src/characters.dart", "hash": "99b4d15f76889687c07a41b43911cc39"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.0/lib/src/outputs/console_output.dart", "hash": "3430401759c3faf2891f666c719a4c18"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences-2.5.3/lib/src/shared_preferences_devtools_extension_data.dart", "hash": "3f47c1f73c7a4541f98163b83d056456"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/LICENSE", "hash": "a086f9770acbfc6a5e421b49411d9415"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camera_platform_interface-2.10.0/lib/src/events/device_event.dart", "hash": "aa3c9a130d6e7ac3aaa10045c29669b9"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/gesture_detector.dart", "hash": "2354ff7691e352dd0fe56e0a46338db9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camera_platform_interface-2.10.0/lib/src/types/resolution_preset.dart", "hash": "c9007c82f08cf346a9388897f3ca87c0"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/foundation/platform.dart", "hash": "dd109d67b92b9fbe6e0051f0c890c903"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/src/types/lost_data_response.dart", "hash": "064f79178a908761de1a6b8334a36b6f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/style/posix.dart", "hash": "5e054086533f32f7181757a17890ae56"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/util/abstract_file_handle.dart", "hash": "576eef6e1ad485270c8041e5ff3a39c5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/dynamiccolor/src/contrast_curve.dart", "hash": "9a12cf2a3549924510006db4651a1743"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/blend/blend.dart", "hash": "f487ad099842793e5deeebcc3a8048cb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/util/_crc64_io.dart", "hash": "8c90185708fa02620fdeca18f01e0d30"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/model/content/content_model.dart", "hash": "de5acbfbfc9b6eef88829a6a283439a8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_platform_interface-2.4.0/LICENSE", "hash": "80ea244b42d587d5f7f1f0333873ad34"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/semantics/debug.dart", "hash": "3fd33becc9141d8a690c4205c72c5d40"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_barcode-7.4.1/lib/src/capture/barcode_capture_settings.dart", "hash": "cb81a533653efb794d9f8a5a5d48007a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/result/result.dart", "hash": "c6e362e3e6b16241c22db67cbbd6b85b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_barcode-7.4.1/lib/src/spark/spark_scan_view_settings.dart", "hash": "5bce7b099da9b84fd82ed0357dd8a7df"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/parser/moshi/buffer.dart", "hash": "b24f550d7bf4a7cb4d00086899634cc3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_barcode-7.4.1/lib/src/spark/spark_scan_listener.dart", "hash": "3c89c240f839052378f96f33b087d1f6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.4/LICENSE", "hash": "d2e1c26363672670d1aa5cc58334a83b"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/services/message_codecs.dart", "hash": "256d1c386e48e198e2e0a04345221477"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/src/database.dart", "hash": "66f280c66f95d03902082cdd2b4255e1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/gotrue-2.13.0/lib/src/gotrue_client.dart", "hash": "330900c4505e702bfa4e481a71ff1cca"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/supabase-2.8.0/lib/src/supabase_query_schema.dart", "hash": "5485a9524496542c704a8aa0f766b843"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_linux-0.2.1+2/lib/image_picker_linux.dart", "hash": "1936d57a483f9894c5b5c3087b446809"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/text.dart", "hash": "7217dd37b49bab8e0319d4fb26d14d8e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/model/content/circle_shape.dart", "hash": "1a4ebfbe7175b903deb3dc588705ff8e"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/gestures/velocity_tracker.dart", "hash": "be0a77cf3f0463f3dacd09ec596d9002"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/bottom_app_bar_theme.dart", "hash": "ff2b2e7159e19374f968cf529da25c01"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/octo_image-2.1.0/LICENSE", "hash": "bb500500256905950683ee38c95fb238"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_android-0.8.12+23/lib/image_picker_android.dart", "hash": "007c2b99a7ab8b0ea0ed298ac83d52b1"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/painting/fractional_offset.dart", "hash": "e7b2de136a99cf5253477d4fb4138394"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/lib/src/win32_wrappers.dart", "hash": "af7270fd3861278053b1c45a7b66ece3"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/cupertino/activity_indicator.dart", "hash": "0e3d746a279b7f41114247b80c34e841"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/placeholder.dart", "hash": "a69e90f683dddaf61ae8d7f094219026"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/painting/image_resolution.dart", "hash": "0f2a1a61119c0bef3eaf52c47a2ebcf4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_android-0.8.12+23/LICENSE", "hash": "619f69d64af6f097877e92ac5f67f329"}, {"path": "/Users/<USER>/AndroidStudioProjects/Safescan/lib/services/new_scan_workflow_service.dart", "hash": "cdfbce5f2535f4968836afb2c1845817"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/subjects.dart", "hash": "1f923e6c3ab08753130b61ba27256959"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/overlay.dart", "hash": "cd0cbb4d29516ed6b03d1c68f0c08477"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/animated_icons/data/home_menu.g.dart", "hash": "11fc97acd20679368ae2eaa698c6f130"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/cupertino/switch.dart", "hash": "329bc189be2701d02fb1b7975ecf329e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/lottie_property.dart", "hash": "48a507796ce332e2fc9416ecdea2cf31"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_macos-3.2.2/lib/src/messages.g.dart", "hash": "114597dbbcfb24754b14f8261211d90f"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/scroll_position.dart", "hash": "94c0c017ccb267b7cacc7c047ee5b9c3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.4/lib/src/utf16.dart", "hash": "10969c23d56bc924ded3adedeb13ecff"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/physics/gravity_simulation.dart", "hash": "44c1268c1ecafd3b4cd06ab573f6779a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/mime-2.0.0/lib/src/magic_number.dart", "hash": "d9d40cd4fd7e692ca4246d952d48cca8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher-6.3.2/lib/url_launcher.dart", "hash": "10bbfa83fe7c3c8f8a4964a3e96e5b58"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_barcode-7.4.1/lib/src/count/barcode_count_defaults.dart", "hash": "dc8152e846cbf99eed2e11764781e4e6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/supabase-2.8.0/lib/src/realtime_client_options.dart", "hash": "0eea22408553c3948d6178979d8ed79f"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/pages.dart", "hash": "068ea69f3733bd1aa72b910e51b41b12"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/stream_transform-2.1.1/lib/src/common_callbacks.dart", "hash": "edeb3ef79020eb4835ccfc72c6b7a89a"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/gestures/converter.dart", "hash": "ed5548873fcf5a0a5614fc52139600b8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3/lib/src/hive_error.dart", "hash": "705c71a4fde7fd9f2f8130b35b98caa5"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/scheduler/service_extensions.dart", "hash": "f49291d1bc73b109df4c162db10003d2"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/painting/linear_border.dart", "hash": "0fa4800227413041d2699ed47918c7f7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/go_router-16.0.0/lib/src/pages/custom_transition_page.dart", "hash": "bd81c6cc5eb829742ceb3a955cd852d5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_platform_interface-4.3.0/lib/permission_handler_platform_interface.dart", "hash": "54e3fc58f0992b887be63771a3d82202"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/realtime_client-2.5.1/lib/src/retry_timer.dart", "hash": "8581cc783aadb7e85e83e073cbf3b4c2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/posix-6.0.3/LICENSE", "hash": "2a68e6b288e18606a93b3adf27dbf048"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/src/collection_utils.dart", "hash": "add5f0afe8e318e91950e5725be6f333"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/highlighter.dart", "hash": "5265b4bdec5c90bfd2937f140f3ba8fc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/storage_client-2.4.0/lib/storage_client.dart", "hash": "cd75a553b92e8ba4a586b527f4087b91"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/animation/content/greedy_content.dart", "hash": "ca299d047ca6801cfe66e2deb89d1613"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/internals.dart", "hash": "5c4a5af039aad32f5ac9bdbfc1536af4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/src/database_file_system.dart", "hash": "dac02dc6cb13c753a5f3ae19976b1540"}, {"path": "/Users/<USER>/AndroidStudioProjects/Safescan/build/app/intermediates/flutter/debug/flutter_assets/AssetManifest.bin", "hash": "853879b1a8241c659af534593aa3e68a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/supabase_flutter-2.9.1/lib/src/constants.dart", "hash": "d1f3abcfd9b0725cf581e968a63ad8cd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/async_notifier.dart", "hash": "b60a2076a519fde0c9162319239b25eb"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/inherited_model.dart", "hash": "940daf4491e3ab2e15d7eac5d6ce6b23"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/title.dart", "hash": "e556497953d1ee6cd5d7058d92d4e052"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_windows-0.2.1+1/lib/image_picker_windows.dart", "hash": "4a9b1f00f6665e425a008a2201361658"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_core-7.4.1/lib/src/data_capture_version.dart", "hash": "0c9d776bb086d5a330ff75f7d82f6f63"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/painting/text_scaler.dart", "hash": "b6e992b1127f8376358e27027ea7a2ff"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/cupertino/desktop_text_selection_toolbar_button.dart", "hash": "bce1e8ef07d9830bbf99031d77e0b9fc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_design_icons_flutter-7.0.7296/lib/fonts/materialdesignicons-webfont.ttf", "hash": "4192c96835815b160f03a005cabed284"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_core-7.4.1/lib/src/camera.dart", "hash": "2b66d449910ad9b7744d6629c8703ccc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_barcode-7.4.1/lib/src/pick/ui/barcode_pick_view_highlight_style_request.dart", "hash": "ad4515eb5ff543c1f7c94b4ea1ea79f7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camera_platform_interface-2.10.0/lib/src/types/exposure_mode.dart", "hash": "830c503e2d20f6ce44495aa77cbd5e8c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/mime-2.0.0/lib/src/bound_multipart_stream.dart", "hash": "6a792eed43130ef8c5b35bb12106f303"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/circle_avatar.dart", "hash": "3ad691d7f4e0dfc9bac177f56b288925"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/cupertino/text_theme.dart", "hash": "ee36aadc3fac54d5659c94c6aadcd007"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/scrollable_helpers.dart", "hash": "7f2ccd6eece375fce2e247d3995e45c5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_dotenv-5.2.1/lib/src/parser.dart", "hash": "14f50d28cd925e630a3c1b8205fff395"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/model/content/gradient_fill.dart", "hash": "09fc0e1c242c46bff1c2cd7061f89a01"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/race.dart", "hash": "2164e0e3bc00d90bd03708ddfd475ad9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camera_web-0.3.5/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/AndroidStudioProjects/Safescan/assets/images/adaptive-icon.png", "hash": "330ee2d0bca582f846798f5ba8c3fb16"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/go_router-16.0.0/lib/src/misc/inherited_router.dart", "hash": "94325c70d85d9b1d588018f56c56adc8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/async_notifier/auto_dispose_family.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_platform_interface-2.6.2/lib/src/method_channel/method_channel_file_selector.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/parser/scale_xy_parser.dart", "hash": "e47d0ce92822592ea1245b46e72c74a0"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/services/raw_keyboard_fuchsia.dart", "hash": "a06bb87266e0bac30a263d7182aaf68c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences-2.5.3/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/cupertino/sliding_segmented_control.dart", "hash": "2e074f4fb954a719546377c67cb54608"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/chunked_stream_reader.dart", "hash": "14acd577a81cd5aa871c66f430b95d97"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_android-6.3.16/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/supabase_flutter-2.9.1/lib/supabase_flutter.dart", "hash": "d1611e6a97ba94195fd89c128dbd0260"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/chip.dart", "hash": "728c8c2ffdc4b584c67df65b41e6461f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/lib/src/sqflite_import.dart", "hash": "e0a5a25c69f7362ae3d6e493dfc611ee"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/animation/keyframe/mask_keyframe_animation.dart", "hash": "3fa98c2f1890e89b82f911d315773b10"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/archive/compression_type.dart", "hash": "4338dd80d43355def2a83eb7a8cfdd70"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_macos-3.2.2/lib/url_launcher_macos.dart", "hash": "ff296a17d3582fcd8fe99bfb544a3978"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/iterable_zip.dart", "hash": "df699735e3bcd730f16ce377d562f787"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_barcode-7.4.1/lib/src/batch/barcode_batch.dart", "hash": "60a5ff04f87847cbb5ed6cd3a75c964f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/realtime_client-2.5.1/lib/src/websocket/websocket_io.dart", "hash": "89ccd00e3a0b5f68d00b438818560ecf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/model/layer/base_layer.dart", "hash": "cb466ee251dd98423f9b988132728c6a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/gotrue-2.13.0/lib/src/types/gotrue_async_storage.dart", "hash": "7e34c5d752f5c241806a3700bb17b407"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/rendering/list_body.dart", "hash": "18223495a47aa96889552c9834042729"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/tab_indicator.dart", "hash": "ecc072620f2a72e685360292690c8a68"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/functions.dart", "hash": "41f7bdb7d1eb3c86c21489902221b859"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/scroll_view.dart", "hash": "3d5ecec2ff4236c99de1acef7a20a152"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/framework/proxy_provider_listenable.dart", "hash": "2e59aadb17c005953c2accd529aced98"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/lib/sql.dart", "hash": "597e7b293e2531edc3ef788375e11c67"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_barcode-7.4.1/lib/src/ar/barcode_ar_info_annotation_footer.dart", "hash": "fc37d41949fa76fc8cdeb8e13cb58694"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/delegate/stream_sink.dart", "hash": "ef83fcd13366d1d61c5dbb5c6aae5ead"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/gotrue-2.13.0/lib/src/broadcast_stub.dart", "hash": "0bdc822b5db667d89a39a3404cfecc90"}, {"path": "/Users/<USER>/AndroidStudioProjects/Safescan/lib/widgets/gradient_header.dart", "hash": "cc73de416409bf17ab934cf75f01ccee"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/animation/content/trim_path_content.dart", "hash": "80ecb3fe36551c38582cb9b16ebb9cc6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/codecs/zlib/_gzip_decoder_io.dart", "hash": "b611f18199196cc12d618c765d2906bd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_barcode-7.4.1/lib/src/capture/barcode_capture.dart", "hash": "d5e5e45823ee208b61b3c9516c5e550b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/utils/empty.dart", "hash": "ac4e4c808dab498eb2d5c7f813a5006b"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/foundation/basic_types.dart", "hash": "785eedcc96fa6a4fcc7c81a8736a7427"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme_monochrome.dart", "hash": "66272a6751b167051ba879724cfe5749"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/floating_action_button_location.dart", "hash": "964f3ee4853c34a4695db0c7e063eaa3"}, {"path": "/Users/<USER>/AndroidStudioProjects/Safescan/build/app/intermediates/flutter/debug/flutter_assets/assets/images/icon.png", "hash": "93860b45993acbc1e373efcb6259c852"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_core-7.4.1/lib/src/data_capture_view.dart", "hash": "876c623e199b928cf268c9eede50af7f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/http_parser.dart", "hash": "b76ebf453c4f7a78139f5c52af57fda3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/src/cursor.dart", "hash": "5bde4f62a64276d44e1ef4ee3bf194f6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/null_stream_sink.dart", "hash": "cc0ab0117e8a0a54ec3efe6d9251860e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/comparators.dart", "hash": "8ac28b43cbabd2954dafb72dc9a58f01"}, {"path": "/Users/<USER>/AndroidStudioProjects/Safescan/lib/providers/supabase_provider.dart", "hash": "492da6c4f4cf4a5ab71dc96c7395160c"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/overflow_bar.dart", "hash": "d2694042e337ac1f2d99602c25be195a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/result/file_info.dart", "hash": "2f3e8198efb4b9ec92c0126b25986acc"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/scroll_position_with_single_context.dart", "hash": "56a764067b45a1a7cb6b7f186f54e43a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/config/config.dart", "hash": "6e1f276f9f7416f792db31fd51b3e3ea"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/model/content/layer_blend.dart", "hash": "c8da2464406365202840b1f2d49b4b93"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/stream_channel-2.1.4/LICENSE", "hash": "39062f759b587cf2d49199959513204a"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/cupertino/adaptive_text_selection_toolbar.dart", "hash": "5c96449c2a494ea8f3a50ecc3ba9af74"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/cache_managers/base_cache_manager.dart", "hash": "53745062ff0e01e3d763823156d695da"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/restoration.dart", "hash": "04c713cbc0ac5e15c7978a2e91b81488"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/banner_theme.dart", "hash": "355538055d623505dfb5b9bae9481084"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider-2.1.5/lib/path_provider.dart", "hash": "e08429988b4639fb29cd66bfdc497d90"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/codecs/zlib/deflate.dart", "hash": "089fe7c30acaab000337d853f9c72307"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camera_platform_interface-2.10.0/lib/src/utils/utils.dart", "hash": "8631af0b609ed028cbfbccbb4b2232a4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_barcode-7.4.1/lib/src/selection/barcode_selection_type.dart", "hash": "957a707f3c2fe97373d39df472b3f75a"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/selection_container.dart", "hash": "97359ca5bc2635f947e7616f792565c6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_barcode-7.4.1/lib/src/selection/barcode_selection_tap_behaviour.dart", "hash": "cd3d3e29671b353e301ccffef2227117"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3/lib/src/box/box.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/model/cubic_curve_data.dart", "hash": "4406ec852883860bef08eb555b20f239"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/dialog.dart", "hash": "3f3682db58f83007aada4d5c36376b90"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cross_file-0.3.4+2/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/animated_icons/animated_icons.dart", "hash": "97f7922aea45c38413930285b604bf18"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/storage_client-2.4.0/lib/src/storage_file_api.dart", "hash": "64786b8e9e82622195b99088e19ff21e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_barcode-7.4.1/lib/src/spark/spark_scan_preview_behavior.dart", "hash": "6b5fde355892c6ce14de6dc880bcbbc1"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/painting/border_radius.dart", "hash": "b75501071b7ff5d32ddab4c6ea5d2f84"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/realtime_client-2.5.1/lib/src/websocket/websocket.dart", "hash": "b6df125fb6427e7c019ac354a1f909bd"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/cupertino/route.dart", "hash": "7e827f3c407d93dfa01d1c8cac14af80"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/services/keyboard_maps.g.dart", "hash": "2c582bec6fc77f68c975f84d2252ed8d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/style.dart", "hash": "bfb39b98783e4013d9fe5006de40874d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/opengl.dart", "hash": "9e22ead5e19c7b5da6de0678c8c13dca"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camera_platform_interface-2.10.0/lib/src/types/camera_description.dart", "hash": "ebf71a1e414b88af3648217751c47527"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.12.1/lib/src/unparsed_frame.dart", "hash": "0c30a117b0d1fd5c94980510832b81d0"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/material.dart", "hash": "76611c76bf37be8fc89798858b6c7685"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/codecs/bzip2_encoder.dart", "hash": "901730281b8605ecb8ab09a1708ef9ec"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/future_provider/auto_dispose.dart", "hash": "9ab6d0a38467598c8e1f332648cff545"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_platform_interface-2.4.0/lib/src/factory_platform.dart", "hash": "2441a967786bd149053b72e22172ce60"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/where_not_null.dart", "hash": "9b84f667016de96aa99b12338a4dfb57"}, {"path": "/Users/<USER>/AndroidStudioProjects/Safescan/assets/animations/Safescan-Green.json", "hash": "98c2450113e39fdd31f46c96a27a41d7"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/choice_chip.dart", "hash": "3cd5a71cfa881a4d3d6325d6b2c6d902"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/cupertino/button.dart", "hash": "78f88eba40852ba0b7700d94f3ecfec6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/realtime_client-2.5.1/lib/src/transformers.dart", "hash": "52d9a882cdc4bd23e42127714c4893fc"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/icon.dart", "hash": "826b67d0d6c27e72e7b0f702d02afcec"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/local/local_file_system_entity.dart", "hash": "22f170a8dc9abfac2942555e83589e1a"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/grid_paper.dart", "hash": "6aad5f436704faf509d60ddb032f41b4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/utils/characters.dart", "hash": "249f968bb06f2aad70bd7f15ecb10daa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.10/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/lib/src/dev_utils.dart", "hash": "9a4ee08ca541303a2aee95f83f548ce1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/LICENSE", "hash": "3cc5c8282a1f382c0ea02231eacd2962"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/skip_last.dart", "hash": "355616c9fb00a5e0ec803fffa8f33eff"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/logger.dart", "hash": "0cdc9d79f7fc4d0920bc6a8fc02e6872"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/meta-1.16.0/lib/meta_meta.dart", "hash": "0cf5ebf6593fabf6bb7dfb9d82db735b"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/feedback.dart", "hash": "c8f69577793923bfda707dcbb48a08b1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/stream_transform-2.1.1/lib/src/concatenate.dart", "hash": "42804a1a3f9bec032c0743b86b0a5548"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web_socket-1.0.1/lib/io_web_socket.dart", "hash": "d1369a1aef6c47846c44ccee9eeb0a92"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/octo_image-2.1.0/lib/src/image/image.dart", "hash": "e03a984efe74a058d3393aba7c55fe1f"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/cupertino/search_field.dart", "hash": "6dbd6092d46d1cfb37491463002e960e"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/foundation/isolates.dart", "hash": "1dab3723527db6a19410ed34b6acaeed"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/lib/src/messages.g.dart", "hash": "1567572a579e5f2aab31966d4a056855"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/rendering.dart", "hash": "4bd3950a0bf4a9f9b09f97594e363d36"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/gotrue-2.13.0/lib/src/types/session.dart", "hash": "af53eb71d7a6b68874c82c524b11795b"}, {"path": "/Users/<USER>/AndroidStudioProjects/Safescan/assets/animations/SafeScan-blue.json", "hash": "dac327f30b2d735620a38f6fa10ca9cb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/storage_client-2.4.0/lib/src/fetch.dart", "hash": "e7f9786047b53b9c4bb8abcae30d3990"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/selectable_text.dart", "hash": "130ada4ea6283eb536d5d8eb0786a631"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/go_router-16.0.0/lib/src/pages/material.dart", "hash": "61f9ae17975d4d233db25ee3f27633bf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/defer.dart", "hash": "d06420fd88fb8f7cc3acc1643051178a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/gotrue-2.13.0/lib/src/version.dart", "hash": "51fb83ed378faaa6828804c63038a9fb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_web-2.4.1/LICENSE", "hash": "c458aafc65e8993663c76f96f54c51bc"}, {"path": "/Users/<USER>/AndroidStudioProjects/Safescan/assets/animations/Data-not-found.json", "hash": "1a06f1ad989fe14d258d811468ae2d5e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/gtk-2.1.0/lib/src/gtk_settings.dart", "hash": "fb43cbacbb36bd207b78117d844c3248"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/button_style.dart", "hash": "982099e580d09c961e693c63803f768d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/lib/src/get_application_id_real.dart", "hash": "0e5b422d23b62b43ea48da9f0ad7fd47"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/rendering/sliver_tree.dart", "hash": "5cbb66bc2f7ff989a32bc1e5ce5971e6"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/foundation/node.dart", "hash": "a5d0509a39803ffb48cae2803cd4f4bd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/parser/keyframe_parser.dart", "hash": "026849b5614f322ae5f2fc4970f584c5"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/date.dart", "hash": "f36568b4288388242cb6f7775cb60c42"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/memory/memory_file_system.dart", "hash": "9007580fb76ae011692307f00e0a28f1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_barcode-7.4.1/lib/src/selection/barcode_selection_settings.dart", "hash": "1b2c2c7c35854b89f5219481edf7dc9a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/src/database_mixin.dart", "hash": "5e9d885bc066ae16bcca5bf065c9d51f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/go_router-16.0.0/lib/src/route_data.dart", "hash": "6fb769cf3f98ed969c465b682cbc24f3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/src/types/media_options.dart", "hash": "5f44f436ff7b1129b18a489faab45005"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.2/lib/src/generated/top_level.dart", "hash": "15439eaa12b927b0e9a42b9d168e3371"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/gotrue-2.13.0/lib/src/fetch.dart", "hash": "0df16a49a248b11034e4e0accf97d7cf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_linux-3.2.1/lib/src/messages.g.dart", "hash": "814815839a4b6d2924a5a8661780b0cc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_windows-0.2.1+1/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/AndroidStudioProjects/Safescan/lib/screens/auth/login_screen.dart", "hash": "75e743cc0a94f9273c42170e89a24bad"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/rendering/service_extensions.dart", "hash": "d7a6c07c0b77c6d7e5f71ff3d28b86bd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_riverpod-2.6.1/lib/src/builders.dart", "hash": "dc1a141705a29df814f129c65b47b5d3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/supabase-2.8.0/LICENSE", "hash": "bb0a4b2e3d82de4116e8425de9a3927f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_barcode-7.4.1/lib/src/spark/spark_scan_session.dart", "hash": "8ae50c3de6b58821fb240257278fc17c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3/lib/src/io/buffered_file_writer.dart", "hash": "83ad6899b262c42a494ebce50a8974a8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/stream_transform-2.1.1/lib/src/async_map.dart", "hash": "2e3907a6bf1a5ac452581b5e1d72eadd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/model/content/drop_shadow_effect.dart", "hash": "2dd447f5abff8e3f3db827662ae7f018"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/action_icons_theme.dart", "hash": "50dfb9886f462e2b3405f0f8d23f179b"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/heroes.dart", "hash": "a7ca596d88ce54ac52360d6988d7c9c8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/context.dart", "hash": "daeb052f1089d4e84d8a22acf56c1da2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/lib/path_provider_foundation.dart", "hash": "9485ecc20aafb0727c2700cf6e34cb65"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/nested-1.0.0/LICENSE", "hash": "753206f0b81e6116b384683823069537"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/take_last.dart", "hash": "08f42ef74f129fde820b3414026b8d34"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/rendering/viewport_offset.dart", "hash": "e45c87e4aadaebf7ba449f4c60929928"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/dematerialize.dart", "hash": "6f6ced37453e06f063a482bcb9509370"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/services/system_channels.dart", "hash": "b3d31c9c130a73d5425905f361f63957"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/semantics/semantics.dart", "hash": "c789dd4004265224055546db82c4c7c7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_core-7.4.1/LICENSE", "hash": "f957049418f2fbbf4c88bd3dc33554d5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hive_flutter-1.1.0/lib/src/hive_extensions.dart", "hash": "3a5e5ce96980d4eeb6ef4992080817d5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/noise.dart", "hash": "206b1db3ce5f7b9e5efd220712f8d391"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/rendering/proxy_sliver.dart", "hash": "1244032abcc6103795809163331238a9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/constants.dart", "hash": "aa4b5c0cdb6a66685350611b29ca9d38"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/stepper.dart", "hash": "56198ea7cfc4930ad8bcfc81a2061b78"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/dynamiccolor/variant.dart", "hash": "8dea906a9b8773920b6d1ccea59807bf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3/lib/src/registry/type_registry.dart", "hash": "c17abfd46dd4cb9d6b286b913754f6fd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_barcode-7.4.1/lib/src/count/target_barcode.dart", "hash": "fad40ab5d255bab6db2eaf426196d7e5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/LICENSE", "hash": "fb92f0b8decb7b59a08fe851e030948d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/sha512_fastsinks.dart", "hash": "7924bc2d95999b2767d9f34e6ac52f98"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/gestures/hit_test.dart", "hash": "2d3948bf5dd7b63d100270fce62fa2d9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/stream_sink_transformer/reject_errors.dart", "hash": "2f711a88a049130159adb3f7867423c0"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/painting/matrix_utils.dart", "hash": "59475498db21e2333db54d6478af7c94"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/src/types/picked_file/base.dart", "hash": "d0b83bff5ce65e6924939f442ae2c2a7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_barcode-7.4.1/lib/src/pick/ui/barcode_pick_view_highlight_style.dart", "hash": "81486c84434a4a30e1de90130f0363e1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/exhaust_map.dart", "hash": "4c61dffec4ef48c5b09f3009e7765657"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/postgrest-2.4.2/lib/src/postgrest.dart", "hash": "cf33611c4bba93ac0747218b7f98afe0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/value/lottie_value_callback.dart", "hash": "fc613bf20a036fc7ff8b209a6033a94a"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/painting/shape_decoration.dart", "hash": "6486bc074c81ec57bdafc82e6a64683a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.12.1/lib/src/vm_trace.dart", "hash": "9a7022bcfa03c67d126e948062508201"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/future_provider.dart", "hash": "a6705b39e0c01e2fc0e40b8c8c674aac"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/rx.dart", "hash": "f222f3be7d9e176a7d8ba3252825c9f8"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/expansion_panel.dart", "hash": "5cedacfe2fd447a541cd599bfc1aef91"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/codecs/bzip2/bz2_bit_writer.dart", "hash": "23f783831a77900b3e835880f5b4a1b0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/parser/float_parser.dart", "hash": "4a30273fd55b9191d03b945a39a4899e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logging-1.3.0/lib/src/level.dart", "hash": "49f3213e86d2bafdd814ac4df3d114ca"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/input_chip.dart", "hash": "14177be7a74b321668af2b9effa0f396"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/painting/_web_image_info_io.dart", "hash": "e4da90bb20b3980a03665a080c87a098"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/rendering/box.dart", "hash": "********************************"}, {"path": "/Users/<USER>/AndroidStudioProjects/Safescan/build/app/intermediates/flutter/debug/flutter_assets/assets/animations/RippleLoadingScreen.json", "hash": "9f3a71f9bc8304dab18ad509c2a5cb85"}, {"path": "/Users/<USER>/AndroidStudioProjects/Safescan/lib/widgets/product_card.dart", "hash": "3192107c5c0cc06ed342fbffb8d1c4fc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/util/file_buffer.dart", "hash": "bac89ce78a84824b60c4bf876e5510ee"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/codecs/zlib/gzip_decoder_web.dart", "hash": "e244e8e84d2f26e11dc0b7f61c9bde37"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/postgrest-2.4.2/lib/src/postgrest_query_builder.dart", "hash": "d974833b6818428eddb7e2dc4b907921"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/model/animatable/animatable_gradient_color_value.dart", "hash": "3eb6fdd8c9ff064387c409b456ea7177"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/storage/file_system/file_system.dart", "hash": "a0432b1db3ddabe8c3edb6f542c9ef48"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/mime-2.0.0/lib/mime.dart", "hash": "6438480f29034a2c6acd5817c656d94d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/functions_client-2.4.3/lib/src/functions_client.dart", "hash": "f63beaf724c15766b9ff8e2d2bd70ed4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/model/animatable/animatable_point_value.dart", "hash": "cdd6c42f83fd9c9b628367c472b4af4f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/gotrue-2.13.0/lib/src/types/user_attributes.dart", "hash": "18bcbfa8d9d96fc4ef7468eb88e3b8b4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_core-7.4.1/lib/src/data_capture_component.dart", "hash": "33618a04281d9eef3b2d343efd13cf02"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/animation/content/path_content.dart", "hash": "7e110866853796ac0b085c127779df37"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3/lib/hive.dart", "hash": "f038e71fe3279bb9c67e5ef28b3e8afe"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_core-7.4.1/lib/src/frame_source.dart", "hash": "2ea2a609d498d6eb9039fa40efb2674e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/lib/shared_preferences_async_platform_interface.dart", "hash": "03664e80d73ff10d5787d9a828c87313"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/realtime_client-2.5.1/lib/src/realtime_client.dart", "hash": "b8a40270323200007ecb3177dbca4f4a"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/services/raw_keyboard_windows.dart", "hash": "266a40131c9f05494e82934fd7096ed0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme_expressive.dart", "hash": "be096140df774ec827218c6fe69b80e5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_platform_interface-2.3.2/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/animation/content/gradient_fill_content.dart", "hash": "99e7f62b954b102fd66dfe5a465b8923"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/utils/composite_subscription.dart", "hash": "76cc6c2b845bff11813d968688280b36"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/lib/messages.g.dart", "hash": "3f45d05cfb9a45bf524af2fa9e8fb6e6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/image_picker_platform_interface.dart", "hash": "b152cc1792a66ac4574b7f54d8e2c374"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.20.2/LICENSE", "hash": "0c3ca74a99412972e36f02b5d149416a"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/painting/circle_border.dart", "hash": "a2aa815908f2e15493e374b9380e558a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/codecs/zlib/gzip_encoder_web.dart", "hash": "ef94bee55980ea8f1e63f68c441cbf64"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/base_client.dart", "hash": "32a40215ba4c55ed5bb5e9795e404937"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_lints-6.0.0/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/gotrue-2.13.0/lib/src/types/mfa.dart", "hash": "078683282049ea3ef300f21af3cba867"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/src/constant.dart", "hash": "176c6b2c4f4e2d64cd55df2a0dabe5e5"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/texture.dart", "hash": "cd6b036d4e6b746161846a50d182c0b5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cross_file-0.3.4+2/lib/src/x_file.dart", "hash": "d06c42e6c83be207b86412e11889266a"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/typography.dart", "hash": "eea9d5a977d3ff4f46bb63a0f140c738"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/animated_icons/data/play_pause.g.dart", "hash": "9ad11b4bdb179abe4ccb587eb0e2aebc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/stream_splitter.dart", "hash": "698b7b5743b9cfa0aa9d08de156d04b6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_barcode-7.4.1/lib/src/structured_append.dart", "hash": "f33412c5487273b9ca150f7ddd0cc851"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/result/value.dart", "hash": "bf3aeab9379cee97ddcc69d885a477f5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sprintf-7.0.0/lib/src/formatters/string_formatter.dart", "hash": "b5871241f47bc90693cb26fae0bb8616"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/shaders/ink_sparkle.frag", "hash": "a0e89676ccae6cf3669483d52fa61075"}, {"path": "/Users/<USER>/AndroidStudioProjects/Safescan/lib/widgets/safety_score_widget.dart", "hash": "71fda500e46540da0064580439c39d3a"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/paginated_data_table.dart", "hash": "865354d8941afe9359c093d59d7b282f"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/animated_icons/data/arrow_menu.g.dart", "hash": "555fcdeebbe6517cde1cdd95133cabd7"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/gestures/long_press.dart", "hash": "c97a8ffd51479d05a18a54ac27ccba15"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/span_with_context.dart", "hash": "a8f2c6aa382890a1bb34572bd2d264aa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_platform_interface-2.3.2/lib/url_launcher_platform_interface.dart", "hash": "9190f2442b5cf3eee32ab93156e97fb1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/stream_transform-2.1.1/lib/src/from_handlers.dart", "hash": "b631bb6ec102953c2b84347f00544869"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/filled_button.dart", "hash": "3de98898d0fea89f0e609dcbf7b69783"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/painting/alignment.dart", "hash": "ccdbac117e9349d3ceaa005c645277e2"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/physics/simulation.dart", "hash": "0fbec63144acf1cb9e5d3a3d462e244b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/go_router-16.0.0/lib/src/builder.dart", "hash": "7343264717127ebb7016260e9dc45319"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/segmented_button_theme.dart", "hash": "b815d11a718e0a4d6dec5341e2af4c02"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web_socket_channel-3.0.3/lib/io.dart", "hash": "4ab9db6c8df9c15fb96ce458baad9a09"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/dynamiccolor/dynamic_scheme.dart", "hash": "7536ace8732469863c97185648bb15a9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/base_request.dart", "hash": "8ac37c0f7bea9c97df2a0bef6bb3f858"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_barcode-7.4.1/lib/src/spark/spark_scan_mini_preview_size.dart", "hash": "cd8f10c176b921bf3ba9458b6d8f2f62"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/cupertino/context_menu.dart", "hash": "02f1d44813d6293a43e14af1986519ff"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_barcode-7.4.1/lib/src/count/barcode_count_session.dart", "hash": "d1a4bf4cdbeff57608ae2e37cf1a6b31"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/disposable_build_context.dart", "hash": "edd2f9cabffc7ea6a5a9497a1b1beccd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.0/lib/web.dart", "hash": "d7c63cf2f303b7a0aef972ee03d3c7e4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/utils/math_utils.dart", "hash": "e4ee21048ab83cc50d61ac3784afa9f5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/stream_channel-2.1.4/lib/src/stream_channel_completer.dart", "hash": "a64b855dc42d91c681b48548816fec8b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/app_links-6.4.0/LICENSE", "hash": "d229da563da18fe5d58cd95a6467d584"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/painting/_network_image_io.dart", "hash": "be7392100d4028793c499a48ed55cf29"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/stream_completer.dart", "hash": "b9531c458d313a022930a0842db8201e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/stream_closer.dart", "hash": "cbd0196f25d2f055736beb3052a00c19"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/status_transitions.dart", "hash": "59b6b74779849bf5b836b84bb362b99b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/gtk-2.1.0/lib/src/gtk_settings_real.dart", "hash": "81f9396d070e076eb99ccb41c0be5879"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_riverpod-2.6.1/lib/flutter_riverpod.dart", "hash": "05100b6f82b19ef0bab59f9f174ad39e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_core-7.4.1/lib/scandit_flutter_datacapture_core.dart", "hash": "d92cdfde388bfaf42c5d61b7759ea121"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/animation/keyframe/shape_keyframe_animation.dart", "hash": "b7e311ad40249a2e7fd8f22818ae56de"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/v6.dart", "hash": "70ba25c403724d1332ff4a9e426d7e90"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/palettes/tonal_palette.dart", "hash": "44b3c2a3d6e67a3213a49cce58fed932"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/span_mixin.dart", "hash": "89dc3f84db2cd1ea37e349fdb1de09bb"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/foundation/print.dart", "hash": "458f3bf784829a083098291a97123e81"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_barcode-7.4.1/lib/src/ar/barcode_ar_common.dart", "hash": "9f67ff328dbadfae9cb34ad9082cfd6f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/scan.dart", "hash": "352139677d0d5e7dbf8941093403250b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/utils/min_max.dart", "hash": "50c5f00339854085c2f637109c4166f3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/mime-2.0.0/lib/src/char_code.dart", "hash": "4fb96b9e2073cadc554a25b36f55e6dd"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/painting/debug.dart", "hash": "fab9f5f0fb3bdd9295e12a17fef271c1"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/pinned_header_sliver.dart", "hash": "4e04af41f89adf9231bad1579f5bb9a1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/animation/content/shape_modifier_content.dart", "hash": "a0634214932f5b11cd9ce3825b85ca0c"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/annotated_region.dart", "hash": "3bc33c65fa44a57d13430fdedef82bc2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/delegate/stream_consumer.dart", "hash": "987dfee9ed944d2007a00e521d4fbbe4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3/lib/src/backend/storage_backend_memory.dart", "hash": "a8833e6afcfa9f667d78607fb38747ab"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/foundation/_isolates_io.dart", "hash": "f90beedee11a434d706e3152bfb2fd15"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.2/lib/src/generated/glyph_set.dart", "hash": "62d88517fa4f29f5f3bcec07ba6e1b62"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/dart_plugin_registrant.dart", "hash": "44b8efa69ec831d1a0ce74c20ecc27b4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/replay_stream.dart", "hash": "c86f575dce7f62595d9f1e169492f750"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.0/lib/src/printers/prefix_printer.dart", "hash": "129f33e0f404d9fe5ef3eb75dd7762e6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_platform_interface-2.6.2/lib/src/types/types.dart", "hash": "f4d93b039bc86c4a156848d06fbc2917"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/plugin_platform_interface-2.1.8/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/button_bar.dart", "hash": "42c4c0281ec179aea5687dbced56aca7"}, {"path": "/Users/<USER>/AndroidStudioProjects/Safescan/lib/providers/auth_provider.dart", "hash": "5b47ac0e0d9822c902538793ef3a2d92"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/cupertino/colors.dart", "hash": "58490e33e6e99c4e4e313491a36cf23f"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/rendering/proxy_box.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/storage_client-2.4.0/lib/src/storage_client.dart", "hash": "8d6208413e2d7dc639e5a30f7006293f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3/lib/src/crypto/aes_tables.dart", "hash": "e086df7291d9d546cf582d0a519f9848"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/lib/src/sqflite_impl.dart", "hash": "8e1d2c37f506b65c7d8b3274456d8dfb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/supabase_flutter-2.9.1/LICENSE", "hash": "bb0a4b2e3d82de4116e8425de9a3927f"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/rendering/flex.dart", "hash": "4ec9c8dd6d6ecb43d26ebaef03abd1ab"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/util/byte_order.dart", "hash": "ead62f3d4f5132b7d2b9458601a48870"}, {"path": "/Users/<USER>/AndroidStudioProjects/Safescan/lib/widgets/ingredient_card.dart", "hash": "3127e9c7457e6da54408657053d11bd9"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/semantics/semantics_service.dart", "hash": "fbfdd6181c7ea8d5950c24b467debf31"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/navigation_bar_theme.dart", "hash": "b5eb2fd4d6d9a2ec6a861fcebc0793d2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter_tools/lib/src/build_system/targets/native_assets.dart", "hash": "55b4fed5dadc735394ecc0e13867c2eb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme_content.dart", "hash": "78e53d9a4963c0d19c5ea355a0946e5d"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/rendering/binding.dart", "hash": "9c9f1e70fac06b3e87bb33ece047c4cf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/mime-2.0.0/lib/src/extension.dart", "hash": "ef82a025843a9945bb252078a9754fa4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_barcode-7.4.1/lib/src/find/barcode_find_function_names.dart", "hash": "809853186f01880347af2458233ebde2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/async_cache.dart", "hash": "638c6d804d20c1f83790f7f10c4af408"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/LICENSE", "hash": "86d3f3a95c324c9479bd8986968f4327"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/animated_scroll_view.dart", "hash": "62f6d0411965eefd191db935e6594f90"}, {"path": "/Users/<USER>/AndroidStudioProjects/Safescan/build/app/intermediates/flutter/debug/flutter_assets/assets/images/splash-icon.png", "hash": "81a18fd6e285e18c4c1c21e67c088a90"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_barcode-7.4.1/lib/src/capture/barcode_capture_session.dart", "hash": "9f39e6e5a93f947437cec7b07b1537fb"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/animation/animation_style.dart", "hash": "6cf1ca324535366e2ea214049ffc9918"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/src/types/image_source.dart", "hash": "da5faa2d91b7029347d1a39bc0060cb2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_platform_interface-2.4.0/lib/src/platform_exception.dart", "hash": "89ca6560d39efc4e7a136aafd44f8e49"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.0/lib/src/outputs/multi_output.dart", "hash": "8a8ec5edf7a4c3d3a3598480901db44c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/intersection_result.dart", "hash": "789e79772bba1132b3efdb60636a3ccb"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/scheduler/binding.dart", "hash": "2122bbdb5de249ae3f2444fe234a5afb"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/text_selection.dart", "hash": "9c13d1f810b039faf38c54f062c83747"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/material_button.dart", "hash": "c165bb259eb18a2dc493a0e7a1d1ebd9"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/bottom_navigation_bar_theme.dart", "hash": "307c2ee6ebc77b9995c2799e8e0bed81"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/animation.dart", "hash": "29a29ed9169067da757990e05a1476ee"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/list_tile_theme.dart", "hash": "822ae20c3b70355a4198594745c656f2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/gtk-2.1.0/lib/src/constants.dart", "hash": "79164da6b57d9b3a95a59d01d7b8e64b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/result/future.dart", "hash": "18c04a8f8132af2c1b1de5af6909025c"}, {"path": "/Users/<USER>/AndroidStudioProjects/Safescan/build/app/intermediates/flutter/debug/flutter_assets/kernel_blob.bin", "hash": "db20151ebaa49130ffe42dc5ba09775a"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/LICENSE", "hash": "1d84cf16c48e571923f837136633a265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/stream_provider.dart", "hash": "edc6185b4e4994b45acda6675696d87b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/error_helpers.dart", "hash": "39221ca00f5f1e0af7767613695bb5d2"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/material.dart", "hash": "8ef67f192314481983c34c92a81ee5f2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/lib/src/utils.dart", "hash": "2d3b2846d7071fb93d36485c261040ea"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/functions_client-2.4.3/lib/src/version.dart", "hash": "100cceade0f893d6103e233291cad7f4"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/rendering/animated_size.dart", "hash": "6b396237a38f3417babe500724de8a84"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/theme_data.dart", "hash": "112daf1e5c2a46f4b457e3b76cf569ac"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/lib/src/messages.g.dart", "hash": "95bd0247422d589a2b39cd985a000749"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher-6.3.2/lib/url_launcher_string.dart", "hash": "ec94194f35d48443f468a3b06ef69845"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/on_error_resume.dart", "hash": "f79083ce7919dc45b4d2c313bd37af7f"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/scroll_activity.dart", "hash": "bce1bb799fa4cc899b6525721e14c9aa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/quantize/quantizer_celebi.dart", "hash": "f12f9a9b8bb504f4617bfd1c00d403f0"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/gestures/team.dart", "hash": "f6c6b31745eec54a45d25ffe6e5d7816"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/retry_when.dart", "hash": "add862853473647f3bae9dee0b365857"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_barcode-7.4.1/lib/src/batch/barcode_batch_advanced_overlay_container.dart", "hash": "44c17dd931b248bd6613195e33b405aa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/cache_manager.dart", "hash": "b188e0026dde1c7ef925b5efb80450ef"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_barcode-7.4.1/lib/src/find/barcode_find_defaults.dart", "hash": "042aae3babdf44b5af2bc1b5d98c8541"}, {"path": "/Users/<USER>/AndroidStudioProjects/Safescan/build/app/intermediates/flutter/debug/flutter_assets/AssetManifest.json", "hash": "a3c5d23630ee82fbad29e36cc881f1d8"}, {"path": "/Users/<USER>/AndroidStudioProjects/Safescan/lib/screens/scan_screen.dart", "hash": "293e611874eb69a97d05e7463063fc1d"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/data_table_theme.dart", "hash": "956c84257f1efe6f10ab24f3d6702307"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/cupertino/slider.dart", "hash": "1ae1a412c9f9daff34b9dd63e60cec2d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/go_router-16.0.0/lib/src/information_provider.dart", "hash": "e0e6a22d50cab6e16266023c58517b54"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/navigation_rail_theme.dart", "hash": "e472fd233266592e97b3fb39bb1a11dd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.10/lib/src/shared_preferences_android.dart", "hash": "6f05b68df1b893e73008d1831589377c"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/rendering/stack.dart", "hash": "2cf5ffb71954128b5e80f17a36bcde43"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_platform_interface-2.6.2/lib/src/types/file_dialog_options.dart", "hash": "c7a750b73798e6fbab221eff051e22c3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/model/document_data.dart", "hash": "c7f4e3ea1637780b0cf4c30ab8078de1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web_socket_channel-3.0.3/lib/adapter_web_socket_channel.dart", "hash": "dfeee1386146a7ac69a643f9ebe0cf2f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/codecs/tar_encoder.dart", "hash": "e836c87b16c58c6e0c392617d62d0f13"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/forwarding/forwarding_random_access_file.dart", "hash": "8584e5707c45dd6bdd567a10dfd8cd0d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/lib/src/grapheme_clusters/constants.dart", "hash": "0672d853d5097a03eddc7dbe558eeabd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/parser/lottie_composition_parser.dart", "hash": "26c571534ce0668b8fd9bb6117f94954"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/popup_menu_theme.dart", "hash": "384c15d93757a08ae124e6c2edeb4e9e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_barcode-7.4.1/LICENSE", "hash": "f957049418f2fbbf4c88bd3dc33554d5"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/switch.dart", "hash": "1e840a2c03797a7468018e124b957d2f"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/rendering/sliver.dart", "hash": "ebd06d8f4cce7c59735a2ba28d6dba97"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/bottom_navigation_bar_item.dart", "hash": "900a13c9fcd73f4e8e3d069d76af6ffa"}, {"path": "/Users/<USER>/AndroidStudioProjects/Safescan/build/app/intermediates/flutter/debug/flutter_assets/.env", "hash": "b7061eebc0a36a26b1f3d3b4dff0ed72"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/lib/types.dart", "hash": "13e6a7389032c839146b93656e2dd7a3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/stream_transform-2.1.1/lib/src/switch.dart", "hash": "5b436e60ead9eaf8b303aa72abc08744"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/retry-3.1.2/LICENSE", "hash": "175792518e4ac015ab6696d16c4f607e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/iterable_extensions.dart", "hash": "5843b4750179f6099d443212b76f04a2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_windows-3.1.4/lib/url_launcher_windows.dart", "hash": "792062b629f33f12bf4aa68dd6601c50"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/bin/cache/pkg/sky_engine/LICENSE", "hash": "10f2d960c7d6250bbc47fdf5c6875480"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image-3.4.1/lib/src/cached_image_widget.dart", "hash": "4310ddfcafc039210f0221a343c43164"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/painting/shader_warm_up.dart", "hash": "6d0b38802aff8cbe310e72f1a62750d6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/supabase_flutter-2.9.1/lib/src/flutter_go_true_client_options.dart", "hash": "806d93d5a2f3fd5b4862dc2fec82b0cc"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/semantics/semantics_event.dart", "hash": "c069ad8b31e18adb75c27530f218957a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/lib/utils/utils.dart", "hash": "6c479e0fd2351de96aa7368a1bf8f8ac"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/animated_switcher.dart", "hash": "008b3ea4691331636bbea9e057357ceb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3/lib/src/crypto/hive_cipher.dart", "hash": "a2716332bd9726a3ab118d6fd896ac17"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/codecs/zlib/zlib_decoder_web.dart", "hash": "d9bdac65ce6a5202138f25423578265b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/lib/src/method_channel_path_provider.dart", "hash": "77ed8d7112753d0eeaa860ecd9fc5ba0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cross_file-0.3.4+2/lib/src/types/io.dart", "hash": "a45632c7d0440400b3f7a2ce615d21c0"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/no_splash.dart", "hash": "9c053b0efcabd70996cc27e9d6c9303e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/boollist.dart", "hash": "206ef1a664f500f173416d5634d95c8b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/LICENSE", "hash": "5d89b1f468a243c2269dfaceb3d69801"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/spell_check.dart", "hash": "24094ce9de1b9222a8d6548d3c01045a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hive_flutter-1.1.0/lib/src/box_extensions.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/streams.dart", "hash": "25a929555febc01ae405a334b5ab9ce1"}, {"path": "/Users/<USER>/AndroidStudioProjects/Safescan/assets/images/favicon.png", "hash": "23e4d09379a9e9473882dc9c2dd289c0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/local.dart", "hash": "22b26473ffd350c0df39ffb8e1a4ba86"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/providers/file_provider.dart", "hash": "10501d2b7a94a7d9edcad72942c7bba5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/data.dart", "hash": "e0b6567371b3d5f4cc62f768424e28c9"}, {"path": "/Users/<USER>/AndroidStudioProjects/Safescan/lib/screens/product_detail_screen.dart", "hash": "7818f1683e38cead004fd3af12396382"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/render_cache/key.dart", "hash": "906387788a44bd501f3246b627912ba6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/src/stream_sink_transformer/handler_transformer.dart", "hash": "81a6a107cbfd5dc1c55af9a93189bc5d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cross_file-0.3.4+2/lib/src/types/base.dart", "hash": "86039b13313ad468f867bb5522411241"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_riverpod-2.6.1/lib/src/change_notifier_provider.dart", "hash": "f186193f82036b24fc8379b1f332f817"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/supabase_flutter-2.9.1/lib/src/hot_restart_cleanup_stub.dart", "hash": "07883bee218cb087ce63739b65f67894"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/src/factory_mixin.dart", "hash": "47258dc751a1217744986101e934f62c"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/outlined_button.dart", "hash": "438f80a3d5361329aa6113e3409440aa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/model/content/gradient_stroke.dart", "hash": "a5e6340a3fb1d4ff8e9e58dc8ac40896"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3/lib/src/object/hive_storage_backend_preference.dart", "hash": "bd95228b199ffc9f775bb4e037a461ca"}, {"path": "/Users/<USER>/AndroidStudioProjects/Safescan/lib/constants/mcp_notifier.dart", "hash": "b78030b687b04ed61a18b410d1f0b92b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/delay.dart", "hash": "46133866c09984f60ac2731cf9094a27"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/archive/archive.dart", "hash": "5b422e1bbc3ac131bb7a1ad118ad5a3c"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/rendering/viewport.dart", "hash": "c211cb790c5fc59f5bb6dcd61e0abcab"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/form.dart", "hash": "8678afc1455a658ddf2382ad887eec66"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_windows-0.9.3+4/lib/src/messages.g.dart", "hash": "f381ed91de52f40a7dff4d2f0f3f6d4c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/result/download_progress.dart", "hash": "1e0f86acf6978afd1769e17506893606"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logging-1.3.0/lib/src/log_record.dart", "hash": "703c5e391948c58228960d4941618099"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/painting/box_fit.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web_socket_channel-3.0.3/lib/src/exception.dart", "hash": "a8875f2b3b371e151aab119edb22855a"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/button.dart", "hash": "d7a239f8b80f844857527c2012e4fa1c"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/sliver_persistent_header.dart", "hash": "2a374faf6587ee0a408c4097b5ed7a6e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/riverpod.dart", "hash": "9518a1e0696846221033c0434d777377"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/sql.dart", "hash": "9ab11d900c41a880b39e97693f383b5d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/timer.dart", "hash": "24a365985ef5e526e029d73522f4f2fd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher-6.3.2/lib/src/types.dart", "hash": "ce0d3155596e44df8dd0b376d8728971"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web_socket-1.0.1/LICENSE", "hash": "274291edc62b938ad94e61cec4a14bec"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camera_platform_interface-2.10.0/lib/src/types/video_capture_options.dart", "hash": "a72edf4ce0646093785fb57e35d94c64"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/model/content/polystar_shape.dart", "hash": "11c4bda8e0f54cfa084135260c352136"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/_platform_selectable_region_context_menu_io.dart", "hash": "77e3a9ed54e0497465a4346f273bcccf"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/lookup_boundary.dart", "hash": "37f181e3096dc69dc408bf7d07fcd39a"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/display_feature_sub_screen.dart", "hash": "a6d730f196620dffe89ac987b96ef6c3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.10/lib/src/messages.g.dart", "hash": "d8a6ceefc2ed13b75c503d01c8911fd6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.4/lib/src/arena.dart", "hash": "04f3f5a6ad35c823aef3b3033dc66c3c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/multipart_file.dart", "hash": "ad139ffd36c17bbb2c069eb50b2ec5af"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/cupertino/segmented_control.dart", "hash": "8e58a1e955460cf5a4ea1cea2b7606cf"}, {"path": "/Users/<USER>/AndroidStudioProjects/Safescan/lib/services/supabase_service.dart", "hash": "52e4f8bb79713347bed4db9393367886"}, {"path": "/Users/<USER>/AndroidStudioProjects/Safescan/.dart_tool/flutter_build/dart_plugin_registrant.dart", "hash": "1809e1eceb9b45b6c09566abfe4b812a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3/lib/src/adapters/ignored_type_adapter.dart", "hash": "b2ffb1a4d0254b77d2b63bfa6920223e"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/rendering/paragraph.dart", "hash": "d9eb28b2265932eb628ad0c3a123bee7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/animation/content/shape_content.dart", "hash": "f31dbbca73b763851b52ae08dd9da698"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3/lib/src/registry/type_registry_impl.dart", "hash": "74bcfa36a4954c05f1b8a9d5ed663c8d"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/bin/cache/artifacts/material_fonts/MaterialIcons-Regular.otf", "hash": "e7069dfd19b331be16bed984668fe080"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3/lib/src/box_collection/box_collection_stub.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/model/content/shape_fill.dart", "hash": "abb9220a94be6173fb8a76e75ef2e0e7"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/gestures/binding.dart", "hash": "f6345e2a49c93090bc2e068a0a808977"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3/lib/src/binary/binary_writer.dart", "hash": "61da4ed39b7ee4b0a5256d7c7fcd0a61"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/animation/content/rounded_corners_content.dart", "hash": "ba0146e649ee5ce6093afae7d39769cc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/go_router-16.0.0/lib/src/misc/error_screen.dart", "hash": "72d27451431aeaf0b4f073a66bacf00f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hive_flutter-1.1.0/lib/src/watch_box_builder.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/quantize/src/point_provider.dart", "hash": "7504c44d1fa6150901dd65ec78877be0"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/switch_theme.dart", "hash": "a88d8ea7c8c98dd1d35ad2853f04efe1"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/time_picker.dart", "hash": "bf00ea3c58b6ee2b3f5422cfc3e3cd2b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/codecs/xz_encoder.dart", "hash": "0038d8654407851bfe600112ce197113"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/l.dart", "hash": "bb463422e4d01eed580b3530962d2ee7"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/selection_area.dart", "hash": "ed28f6ca17f72062078193cc8053f1bb"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/default_selection_style.dart", "hash": "bbc9542eb5e3c4701c24bc1268b8165c"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/navigator.dart", "hash": "047052ee1e98c394dd79f1ddf5983b4d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_core-7.4.1/lib/src/location_selection.dart", "hash": "9560d5a78ef2740a88d6be24c5177618"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/arc.dart", "hash": "511ff5c6f0e454b22943906697db172f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/byte_stream.dart", "hash": "c02d47d7f7e95654d3eb9b795e416dda"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/parser/polysar_shape_parser.dart", "hash": "3b41f34cd2cfa0f831c3be57fccea86d"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/scheduler/ticker.dart", "hash": "3e8df17480fcb123b3cdc775ca88dd89"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/cupertino/text_form_field_row.dart", "hash": "cbeab9c259374c922b24d3cbd1cb6aa4"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/rendering/sliver_list.dart", "hash": "03001d3ddae80bbf1f35c5e70e0d93e4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/interface/file.dart", "hash": "1026f587763defb6fb1eec88c2154a3d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/gotrue-2.13.0/lib/gotrue.dart", "hash": "815462b7de87f69b4696f9f2a683a2be"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/LICENSE", "hash": "39062f759b587cf2d49199959513204a"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/radio_list_tile.dart", "hash": "cd7a7fd807697152dfdaeb3109e4f4f4"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/dropdown.dart", "hash": "095edf197865d16a71124cfaa427e31f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/text_form_field.dart", "hash": "28219fbae9045c4c3217c0f3fd6fa7ef"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3/lib/src/binary/binary_reader_impl.dart", "hash": "7a1a5e4d4978935357c5815297b253f4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/parser/document_data_parser.dart", "hash": "5f8643a2a575c405c69480f83b3614c8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/animation/content/compound_trim_path_content.dart", "hash": "11b82d245927c29634310d7c48422fec"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sprintf-7.0.0/lib/src/formatters/int_formatter.dart", "hash": "e6646f76f04f9456f5984aea312a50e5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/lib/src/types/picked_file/lost_data.dart", "hash": "3bc26601d19fa0f119ec8e7fc5fd6e23"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/src/sql_builder.dart", "hash": "1c4127d99af22e5232df8132ae79beeb"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/dropdown_menu_theme.dart", "hash": "93c17b2980fc5498f3ba266f24c6b93b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/postgrest-2.4.2/lib/src/version.dart", "hash": "0160af511359b5d7a18fc5c59e5a4abf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/io_streamed_response.dart", "hash": "f179ed2f20226c436293849c724b2c4d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_barcode-7.4.1/lib/src/pick/internal/barcode_pick_consts.dart", "hash": "51fef55b51ec2d55ff247118aecc7c8b"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/tooltip.dart", "hash": "c816d604c95b060fbb4fa0831ad7523d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/codecs/zip_decoder.dart", "hash": "cde313114aac495f73ca0a12f4e90f91"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/parser/shape_path_parser.dart", "hash": "737d6bff31adcc3f3c9e1edcf9516cfc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/storage_client-2.4.0/lib/src/types.dart", "hash": "8c088d6f09d711aa71f5c8e7f6895f97"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/parser/circle_shape_parser.dart", "hash": "3d3ffabd69b886f12cc56bd38e6653c0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/LICENSE", "hash": "619f69d64af6f097877e92ac5f67f329"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/text_selection_toolbar_text_button.dart", "hash": "91bf94aea1db708a8378fa41de066d33"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/stream_transform-2.1.1/lib/src/async_expand.dart", "hash": "edf98e44de04feefa196e98d41cb7813"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fixnum-1.1.1/lib/src/utilities.dart", "hash": "c18ab890f45960c7227edee678cbdf70"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/sliver_tree.dart", "hash": "d99e76320b224b4518e76f311ef4a804"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_platform_interface-4.3.0/lib/src/method_channel/method_channel_permission_handler.dart", "hash": "219013e8bbe8cf19fde2d3520067ba39"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/model/layer/null_layer.dart", "hash": "bb4fd222cd404c9bfd357820af3dac04"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/animation/content/modifier_content.dart", "hash": "0b24d2e8f2f226cc800520619279488a"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/_html_element_view_io.dart", "hash": "61d3c1705094ee0ea6c465e47b457198"}, {"path": "/Users/<USER>/AndroidStudioProjects/Safescan/build/app/intermediates/flutter/debug/flutter_assets/assets/images/favicon.png", "hash": "23e4d09379a9e9473882dc9c2dd289c0"}, {"path": "/Users/<USER>/AndroidStudioProjects/Safescan/lib/widgets/stats_card.dart", "hash": "d8e15349f302bcca8d3964e5d5390b86"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/triangle.dart", "hash": "7d2bdb4801fc8b3a110f36d5e5fa59f5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/util/aes_decrypt.dart", "hash": "1301cb8ba7f61c2dd521e6c1cac7f78e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/plane.dart", "hash": "f0c6d5d05fbdc95ab84f1a63894b7be6"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/debug.dart", "hash": "6f516ffde1d36f8f5e8806e7811b15ba"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/menu_theme.dart", "hash": "89ae530b1eb1ce798ec54bc9b45efdba"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/memory/memory_random_access_file.dart", "hash": "576c23d693f7712935103974ed9312ee"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/util/archive_exception.dart", "hash": "a975475afc0c5c2b69ea26d2f4df57fd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/builders.dart", "hash": "ccd0c138d8f151e1ccec18f4ceb98f01"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/rendering/flow.dart", "hash": "34ebb85f7f2122d2e1265626cf252781"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_dotenv-5.2.1/lib/flutter_dotenv.dart", "hash": "abad4452b42cf93d5d9ff35493cda028"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_riverpod-2.6.1/lib/src/framework.dart", "hash": "d63ca0c723f6a99572c806b4ec989036"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/uuid.dart", "hash": "ebddd1b3c6af3141a7d2025fadf56ada"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_barcode-7.4.1/lib/src/composite_type.dart", "hash": "efd8a5a8584f571167662a520c0633bf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/utils/future.dart", "hash": "8d3f31cb53177f3f6315575373249597"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/octo_image-2.1.0/lib/src/placeholders.dart", "hash": "4ccaab1f2ffd61fd5998a2fe8a9be886"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/utils.dart", "hash": "fe2489ea57393e2508d17e99b05f9c99"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/render_cache/store_raster.dart", "hash": "d26946e7f04955f282718d76d48141ac"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/multipart_request.dart", "hash": "de670519e8f1f432d9f1a21fdd05b4b3"}, {"path": "/Users/<USER>/AndroidStudioProjects/Safescan/.dart_tool/flutter_build/24601892b32eefa3e669ab6ab2e21a45/dart_build_result.json", "hash": "1f8e8e6dbc6166b50ef81df96e58f812"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/utils.dart", "hash": "105813825251a3235085757d723ae97c"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/elevated_button_theme.dart", "hash": "484329e20b76c279413a7d6dc78b3223"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/src/mixin/import_mixin.dart", "hash": "50ef33e165498030b82cc4c8d8408597"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.0/lib/src/output_event.dart", "hash": "afda74edd611c35dd0a44e3028c7ece8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_barcode-7.4.1/lib/src/count/barcode_count_toolbar_settings.dart", "hash": "aff1dd442cf350f5b00a36e43f84d068"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fixnum-1.1.1/lib/fixnum.dart", "hash": "ca96fbf1a27d4f30ff02bfc5812562a6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/hct/hct.dart", "hash": "596fb2e55b1ff1662e4bd67461fdc89d"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/draggable_scrollable_sheet.dart", "hash": "35e99597a2bc1839b114f890463b5dad"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/painting/text_painter.dart", "hash": "93576d7d8731bea65013886f9194df15"}, {"path": "/Users/<USER>/AndroidStudioProjects/Safescan/build/app/intermediates/flutter/debug/flutter_assets/isolate_snapshot_data", "hash": "a4578c9e939f9a7aec6e8897e055b6ec"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/matrix4.dart", "hash": "b5f0b0da99e8a07d58c21ae071800404"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/utils/forwarding_stream.dart", "hash": "3473bd2c623a639ff8cc439276a3825f"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/painting/clip.dart", "hash": "dc2cfe4408f094916cd5eb1d294d1f2f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/go_router-16.0.0/lib/src/state.dart", "hash": "9a453418cc0baa3cf4c4a41655f4a113"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/src/database_file_system_io.dart", "hash": "35c142ea243059f941a4a896a8e053ae"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_windows-0.9.3+4/lib/file_selector_windows.dart", "hash": "0902c41eed709a7841f11130fac2a593"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/src/arg_utils.dart", "hash": "9812b8e536c69068c0e5f3d3db20c140"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_barcode-7.4.1/lib/scandit_flutter_datacapture_spark_scan.dart", "hash": "04106541183de1f23104bf6a17cc0ed5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_barcode-7.4.1/lib/src/barcode_defaults.dart", "hash": "c623ef37f4ee85c5c1f2bd1af9ad9527"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/combined_wrappers/combined_list.dart", "hash": "5b894ae18be3e2442a34288833184ca9"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/focus_traversal.dart", "hash": "fc0c77cc9957db2d82d3e8d56f8ef9d9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/octo_image-2.1.0/lib/octo_image.dart", "hash": "66d6d10e44ad1e696a8e632a5c4883d2"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/refresh_indicator.dart", "hash": "e0b4c38191be9320c3113762d2dfebbb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher-6.3.2/lib/src/type_conversion.dart", "hash": "032c93433e86ca78b8bb93e654c620e8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_riverpod-2.6.1/lib/src/change_notifier_provider/base.dart", "hash": "b21a009902949ddc4ba80d607867fcb7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/storage_client-2.4.0/lib/src/file_io.dart", "hash": "76964a546c84af33fb4bd8b2ba2fefda"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/painting/text_style.dart", "hash": "0cf873bc441372ec89d746477273af13"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/time.dart", "hash": "872d879ea43b6b56c6feb519cc12d5a9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/lib/src/mixin/constant.dart", "hash": "84fdc97cdb402f94c301f5154682112f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3/lib/src/adapters/date_time_adapter.dart", "hash": "cb28076c9c2d74bd04b62483c2e63193"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/animation/keyframe/point_keyframe_animation.dart", "hash": "d644e4c361831ba112051267fadc6378"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/lib/async.dart", "hash": "13c2765ada00f970312dd9680a866556"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/chip_theme.dart", "hash": "525e57b6ade38da2132c8ddb0ea78547"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/rendering/custom_paint.dart", "hash": "43ba6279385eca1e9d14a3e4d020a3ca"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/storage/cache_info_repositories/cache_object_provider.dart", "hash": "39e587e00bba5c8a7978fd25cf983cc8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_platform_interface-2.6.2/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/codecs/zlib/inflate.dart", "hash": "bc1c29a675191d036c42a321c435e5a5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_barcode-7.4.1/lib/src/aruco_dictionary_preset.dart", "hash": "2b35a6f40b308447af39fa794fe1c26b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/palettes/core_palette.dart", "hash": "d35b72b249d19f54a4cd6f22ff3299e9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3/lib/src/box/lazy_box.dart", "hash": "********************************"}, {"path": "/Users/<USER>/AndroidStudioProjects/Safescan/build/app/intermediates/flutter/debug/flutter_assets/assets/animations/Gradient-VOice.json", "hash": "8ae9610de6b555ae3d766578171b297b"}, {"path": "/Users/<USER>/AndroidStudioProjects/Safescan/lib/services/logger_service.dart", "hash": "bb09e61ab7feeb4fd8ee3406c80ae4d1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.2/lib/src/default.dart", "hash": "7f30d05e05b047b274b1c4b45391d698"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3/lib/src/binary/binary_reader.dart", "hash": "7f909b315b723d7060fa20f099d03ba7"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/widgets/inherited_notifier.dart", "hash": "12143f732513790cd579481704256dcd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/framework/provider_base.dart", "hash": "ddbfb4de9e9dc40a09a6bfae74a41dd8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_core-7.4.1/lib/src/defaults.dart", "hash": "36da501d27df7fb60a7a22d75deadf16"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.4.0/lib/src/typed_queue.dart", "hash": "d6f045db9bd5b72180157d44fee9fbfc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_platform_interface-2.6.2/lib/src/types/file_save_location.dart", "hash": "3c21d269eae774b7e06b8adbe73aa18e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/concat.dart", "hash": "eb8f82998d7328c46b04354df987a331"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1/lib/src/framework/family.dart", "hash": "9dcc50108fd667c7744d5bba6b51e1b4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image-3.4.1/lib/src/image_provider/multi_image_stream_completer.dart", "hash": "4870aa3bcaa04ecc633da01dbd2c9560"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/file.dart", "hash": "51ffa7b452686eecd94ed080a1da4275"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sprintf-7.0.0/lib/src/formatters/Formatter.dart", "hash": "35054401ba5ecdc8134dfd5dc1e09f10"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/gestures/tap_and_drag.dart", "hash": "a2f376b739fa28d7a71312ecf31d6465"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/sha1.dart", "hash": "dfb8ebcfda08e6d9b294f49d74ad9f98"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/skip_until.dart", "hash": "22ce2f0be207fd716e4ae18e312f5cf0"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/rendering/table.dart", "hash": "eda0152837e3eb094d8b1f6d0754f088"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/painting/colors.dart", "hash": "65c7fba34475056b1ca7d0ab2c855971"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/stream_channel-2.1.4/lib/src/close_guarantee_channel.dart", "hash": "1536ff203bc26bdb4841b82c51187a6d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/model/content/shape_trim_path.dart", "hash": "2f48c4660e9f4cec2cd73ca8b0d5e9ef"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_windows-3.1.4/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.12.1/lib/src/frame.dart", "hash": "49286617067167600a8c7357dff1dcfd"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/services/text_editing.dart", "hash": "9298606a388e3adb5f1bbe88ae45b1e6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/model/layer/layer.dart", "hash": "c03ce4536e4048ced11ad09061604f1e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/map_to.dart", "hash": "a28073e1b0a1ffd4999c24379f1dfe02"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/model/animatable/animatable_shape_value.dart", "hash": "d388b2670ebb7d6138554f4b9707436f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/supabase-2.8.0/lib/supabase.dart", "hash": "40eda0872e0a3c197106e14dd6b17ee1"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/rendering/selection.dart", "hash": "cc4a516908b08edff4fade47d6945e5c"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/gestures/drag.dart", "hash": "43ba7557388f413902313df64e072389"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_riverpod-2.6.1/LICENSE", "hash": "1a3dbe8e080bf7ea1081e3c716ee17f1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.1/lib/src/lottie.dart", "hash": "1aa0c8ebbe27d9a0f55e5d052f47ae62"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/lib/string_scanner.dart", "hash": "184d3b79d275d28cd02745b455041ee6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/state_notifier-1.0.0/lib/state_notifier.dart", "hash": "5bc3c944f62b4cf5d382a0c0e9b7e09e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scandit_flutter_datacapture_barcode-7.4.1/lib/src/batch/barcode_batch_settings.dart", "hash": "3fe71c3f75a7488d821f1880c4088145"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/material/text_field.dart", "hash": "b0f444b219eafe3ec2bb9e8a09e545f6"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter_tools/lib/src/build_system/targets/common.dart", "hash": "8b65a0312de1594ea0989e8ce1d4b257"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/hive-2.2.3/lib/src/annotations/hive_field.dart", "hash": "c01f3dc3ecfb5ddf08d6b002c90aabfd"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/src/rendering/sliver_fill.dart", "hash": "123520ee3a48eebf4ba444e93436bb1a"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.0/flutter/packages/flutter/lib/semantics.dart", "hash": "4b784d6e4f290bd6d5a1f38bfb5701d8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.0/lib/src/log_output.dart", "hash": "1cc168543c8f88638826f971d68adbae"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_slidable-4.0.0/LICENSE", "hash": "4da7522cdbae881974e8e3e922db74ea"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/codecs/zlib_decoder.dart", "hash": "580ac3706ec7b899665c577c7eaac060"}]}