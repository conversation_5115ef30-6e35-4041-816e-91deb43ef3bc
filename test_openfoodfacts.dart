import 'dart:convert';
import 'package:http/http.dart' as http;

void main() async {
  // Test specific case: 9470001033 -> 894700010335
  print('🔄 Testing specific case: 9470001033 -> 894700010335');

  final testBarcode = '9470001033';
  final targetBarcode = '894700010335';

  print('\n🧪 Testing original truncated barcode: $testBarcode');
  await testOpenFoodFactsAPI(testBarcode);

  print('\n🧪 Testing with 89 prefix: $targetBarcode');
  await testOpenFoodFactsAPI(targetBarcode);

  // Test if we can reconstruct the full barcode
  if (testBarcode.length == 10) {
    final reconstructed = '89$testBarcode';
    print('\n🧪 Testing reconstructed barcode: $reconstructed');
    await testOpenFoodFactsAPI(reconstructed);
  }
}

/// Generate barcode variations to handle partial extractions
List<String> generateBarcodeVariations(String barcode) {
  final variations = <String>[barcode]; // Start with original

  // If barcode is 10 digits, try common prefixes for missing digits
  if (barcode.length == 10) {
    // Common UPC prefixes
    variations.add('0$barcode'); // Single leading zero
    variations.add('00$barcode'); // Double leading zero
    variations.add('89$barcode'); // Common prefix for food products
    variations.add('04$barcode'); // Common prefix for US products
    variations.add('03$barcode'); // Another common prefix

    // Standard padding
    variations.add(barcode.padLeft(12, '0')); // UPC-A
    variations.add(barcode.padLeft(13, '0')); // EAN-13
  }
  // If barcode is 11 digits, try single digit prefixes
  else if (barcode.length == 11) {
    variations.add('0$barcode'); // Single leading zero
    variations.add('8$barcode'); // Common prefix
    variations.add('4$barcode'); // Another common prefix
    variations.add(barcode.padLeft(12, '0')); // UPC-A
    variations.add(barcode.padLeft(13, '0')); // EAN-13
  }
  // If barcode is 12 digits, try adding leading zero for EAN-13
  else if (barcode.length == 12) {
    variations.add('0$barcode'); // EAN-13
  }

  // Remove duplicates while preserving order
  final uniqueVariations = <String>[];
  for (final variation in variations) {
    if (!uniqueVariations.contains(variation)) {
      uniqueVariations.add(variation);
    }
  }

  print('🔄 Generated barcode variations: ${uniqueVariations.join(', ')}');
  return uniqueVariations;
}

Future<void> testOpenFoodFactsAPI(String barcode) async {
  try {
    final url = 'https://world.openfoodfacts.org/api/v0/product/$barcode.json';
    print('📡 Calling: $url');
    
    final response = await http.get(Uri.parse(url));
    print('📊 Status Code: ${response.statusCode}');
    
    if (response.statusCode == 200) {
      final data = json.decode(response.body);
      print('📦 Response status: ${data['status']}');
      
      if (data['status'] == 1 && data['product'] != null) {
        final product = data['product'];
        print('✅ Product found!');
        print('   Name: ${product['product_name'] ?? 'N/A'}');
        print('   Brand: ${product['brands'] ?? 'N/A'}');
        print('   Image: ${product['image_url'] ?? 'N/A'}');
        print('   Ingredients: ${product['ingredients_text'] ?? 'N/A'}');
        print('   Categories: ${product['categories'] ?? 'N/A'}');
      } else {
        print('❌ Product not found in OpenFoodFacts database');
      }
    } else {
      print('❌ HTTP Error: ${response.statusCode}');
      print('   Body: ${response.body}');
    }
  } catch (e) {
    print('💥 Error: $e');
  }
}
